Pulled: registry-1.docker.io/bitnamicharts/grafana-loki:3.0.1
Digest: sha256:ab3ff84aaaa7c9e28c4f21c92da88b5f9640d8eb2c30690a6c4aa053c6f144b5
# Copyright VMware, Inc.
# SPDX-License-Identifier: APACHE-2.0

## @section Global parameters
## Global Docker image parameters
## Please, note that this will override the image parameters, including dependencies, configured to use the global value
## Current available global Docker image parameters: imageRegistry, imagePullSecrets and storageClass
##

## @param global.imageRegistry Global Docker image registry
## @param global.imagePullSecrets Global Docker registry secret names as an array
## @param global.storageClass Global StorageClass for Persistent Volume(s)
##
global:
  imageRegistry: ""
  ## E.g.
  ## imagePullSecrets:
  ##   - myRegistryKeySecretName
  ##
  imagePullSecrets: []
  storageClass: ""
  ## Compatibility adaptations for Kubernetes platforms
  ##
  compatibility:
    ## Compatibility adaptations for Openshift
    ##
    openshift:
      ## @param global.compatibility.openshift.adaptSecurityContext Adapt the securityContext sections of the deployment to make them compatible with Openshift restricted-v2 SCC: remove runAsUser, runAsGroup and fsGroup and let the platform use their allowed default IDs. Possible values: auto (apply if the detected running cluster is Openshift), force (perform the adaptation always), disabled (do not perform adaptation)
      ##
      adaptSecurityContext: auto
## @section Common parameters
##

## @param kubeVersion Override Kubernetes version
##
kubeVersion: ""
## @param nameOverride String to partially override common.names.fullname
##
nameOverride: ""
## @param fullnameOverride String to fully override common.names.fullname
##
fullnameOverride: ""
## @param commonLabels Labels to add to all deployed objects
##
commonLabels: {}
## @param commonAnnotations Annotations to add to all deployed objects
##
commonAnnotations: {}
## @param clusterDomain Kubernetes cluster domain name
##
clusterDomain: cluster.local
## @param extraDeploy Array of extra objects to deploy with the release
##
extraDeploy: []
## Enable diagnostic mode in the deployments/statefulsets
##
diagnosticMode:
  ## @param diagnosticMode.enabled Enable diagnostic mode (all probes will be disabled and the command will be overridden)
  ##
  enabled: false
  ## @param diagnosticMode.command Command to override all containers in the deployments/statefulsets
  ##
  command:
    - sleep
  ## @param diagnosticMode.args Args to override all containers in the deployments/statefulsets
  ##
  args:
    - infinity
## @section Common Grafana Loki Parameters
##
loki:
  ## Bitnami Grafana Loki image
  ## ref: https://hub.docker.com/r/bitnami/grafana-loki/tags/
  ## @param loki.image.registry [default: REGISTRY_NAME] Grafana Loki image registry
  ## @param loki.image.repository [default: REPOSITORY_NAME/grafana-loki] Grafana Loki image repository
  ## @skip loki.image.tag Grafana Loki image tag (immutable tags are recommended)
  ## @param loki.image.digest Grafana Loki image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param loki.image.pullPolicy Grafana Loki image pull policy
  ## @param loki.image.pullSecrets Grafana Loki image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/grafana-loki
    tag: 2.9.6-debian-12-r0
    digest: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param loki.configuration [string] Loki components configuration
  ##
  configuration: |
    auth_enabled: false

    server:
      http_listen_port: {{ .Values.loki.containerPorts.http }}

    common:
      compactor_address: http://{{ include "grafana-loki.compactor.fullname" . }}:{{ .Values.compactor.service.ports.http }}

    distributor:
      ring:
        kvstore:
          store: memberlist
      rate_limiting:
        ingestion_rate_mb: 50
        ingestion_burst_size_mb: 100

    memberlist:
      join_members:
        - {{ include "grafana-loki.gossip-ring.fullname" . }}

    ingester:
      lifecycler:
        ring:
          kvstore:
            store: memberlist
          replication_factor: 1
      chunk_idle_period: 5m
      chunk_block_size: 1048576
      chunk_encoding: snappy
      chunk_retain_period: 30s
      max_transfer_retries: 3
      max_chunk_age: 2h
      flush_check_period: 30s
      wal:
        dir: {{ .Values.loki.dataDir }}/wal

    limits_config:
      retention_period: 168h
      enforce_metric_name: false
      reject_old_samples: true
      reject_old_samples_max_age: 168h
      max_cache_freshness_per_query: 5m
      split_queries_by_interval: 30m
      ingestion_rate_mb: 200
      ingestion_burst_size_mb: 400
      max_line_size: 256KB
      max_streams_per_user: 10000
      max_query_parallelism: 32
      max_query_series: 100000
      max_query_length: 12000h

    schema_config:
      configs:
      - from: 2020-10-24
        store: boltdb-shipper
        object_store: s3
        schema: v11
        index:
          prefix: index_
          period: 24h
      - from: 2024-03-12
        index:
          period: 24h
          prefix: index_
        object_store: s3
        schema: v12
        store: tsdb
        row_shards: 16

    storage_config:
      aws:
        s3: s3://AKIA2MNKRQKYYDCMNUMI:<EMAIL>/ss-staging-loki-ruler
        region: us-east-1
      boltdb_shipper:
        shared_store: s3
        active_index_directory: {{ .Values.loki.dataDir }}/loki/index
        cache_location: {{ .Values.loki.dataDir }}/loki/cache
        cache_ttl: 24h
        resync_interval: 5m
        {{- if .Values.indexGateway.enabled }}
        index_gateway_client:
          server_address: {{ (printf "dns:///%s:9095" (include "grafana-loki.index-gateway.fullname" .)) }}
        {{- end }}
      filesystem:
        directory: {{ .Values.loki.dataDir }}/chunks
      index_queries_cache_config:
        {{- if .Values.memcachedindexqueries.enabled }}
        memcached:
          batch_size: 100
          parallelism: 100
        memcached_client:
          consistent_hash: true
          addresses: dns+{{ include "grafana-loki.memcached-index-queries.host" . }}
          service: http
        {{- end }}
      tsdb_shipper:
        active_index_directory: {{ .Values.loki.dataDir }}/loki/tsdb-index
        cache_location: {{ .Values.loki.dataDir }}/loki/tsdb-cache
        cache_ttl: 24h
        build_per_tenant_index: true
        {{- if .Values.indexGateway.enabled }}
        index_gateway_client:
          server_address: {{ (printf "dns:///%s:9095" (include "grafana-loki.index-gateway.fullname" .)) }}
        {{- end }}
        shared_store: s3

    query_scheduler:
      max_outstanding_requests_per_tenant: 100000

    querier:
      max_concurrent: 32
      query_timeout: 300s

    chunk_store_config:
      max_look_back_period: 0s
      {{- if .Values.memcachedchunks.enabled }}
      chunk_cache_config:
        memcached:
          batch_size: 100
          parallelism: 100
        memcached_client:
          consistent_hash: true
          addresses: dns+{{ include "grafana-loki.memcached-chunks.host" . }}
      {{- end }}
      {{- if .Values.memcachedindexwrites.enabled }}
      write_dedupe_cache_config:
        memcached:
          batch_size: 100
          parallelism: 100
        memcached_client:
          consistent_hash: true
          addresses: dns+{{ include "grafana-loki.memcached-index-writes.host" . }}
      {{- end }}

    table_manager:
      retention_deletes_enabled: false
      retention_period: 0s

    query_range:
      align_queries_with_step: true
      max_retries: 5
      cache_results: true
      parallelise_shardable_queries: true
      results_cache:
        cache:
          {{- if .Values.memcachedfrontend.enabled }}
          memcached_client:
            consistent_hash: true
            addresses: dns+{{ include "grafana-loki.memcached-frontend.host" . }}
            max_idle_conns: 32
            timeout: 200ms
            update_interval: 1m
          {{- else }}
          embedded-cache:
            enabled: true
            max_size_items: 2048
            validity: 24h
          {{- end }}
    {{- if not .Values.queryScheduler.enabled }}
    frontend_worker:
      frontend_address: {{ include "grafana-loki.query-frontend.fullname" . }}:{{ .Values.queryFrontend.service.ports.grpc }}
    {{- end }}

    frontend:
      log_queries_longer_than: 10s
      compress_responses: true
      max_outstanding_per_tenant: 2048
      tail_proxy_url: http://{{ include "grafana-loki.querier.fullname" . }}:{{ .Values.querier.service.ports.http }}

    compactor:
      working_directory: {{ .Values.loki.dataDir }}/loki/retention
      shared_store: s3
      compaction_interval: 5m
      retention_enabled: true
      retention_delete_delay: 1h
      retention_delete_worker_count: 300
      max_compaction_parallelism: 4

    ruler:
      storage:
        type: local
        local:
          directory: {{ .Values.loki.dataDir }}/conf/rules
      ring:
        kvstore:
          store: memberlist
      rule_path: /tmp/loki/scratch
      alertmanager_url: https://alertmanager.xx
      external_url: https://alertmanager.xx
  ## @param loki.overrideConfiguration [object] Loki components configuration override. Values defined here takes precedence over loki.configuration
  ## e.g:
  ## overrideConfiguration:
  ##   auth_enabled: true
  ##
  overrideConfiguration: {}
  ## @param loki.existingConfigmap Name of a ConfigMap with the Loki configuration
  ##
  existingConfigmap: ""
  ## @param loki.dataDir path to the Loki data directory
  ##
  dataDir: "/bitnami/grafana-loki"
  ## @param loki.containerPorts.http Loki components web container port
  ## @param loki.containerPorts.grpc Loki components GRPC container port
  ## @param loki.containerPorts.gossipRing Loki components Gossip Ring container port
  ##
  containerPorts:
    http: 3100
    grpc: 9095
    gossipRing: 7946
  ## Gossip Ring parameters
  ##
  gossipRing:
    ## Gossip Ring service parameters
    ##
    service:
      ## @param loki.gossipRing.service.ports.http Gossip Ring HTTP headless service port
      ##
      ports:
        http: 7946
      ## @param loki.gossipRing.service.annotations Additional custom annotations for Gossip Ring headless service
      ##
      annotations: {}
## @section Compactor Deployment Parameters
##
compactor:
  ## @param compactor.enabled Enable Compactor deployment
  ##
  enabled: true
  ## @param compactor.extraEnvVars Array with extra environment variables to add to compactor nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param compactor.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for compactor nodes
  ##
  extraEnvVarsCM: ""
  ## @param compactor.extraEnvVarsSecret Name of existing Secret containing extra env vars for compactor nodes
  ##
  extraEnvVarsSecret: ""
  ## @param compactor.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param compactor.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param compactor.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param compactor.replicaCount Number of Compactor replicas to deploy
  ##
  replicaCount: 1
  ## Configure extra options for Compactor containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param compactor.livenessProbe.enabled Enable livenessProbe on Compactor nodes
  ## @param compactor.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param compactor.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param compactor.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param compactor.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param compactor.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param compactor.readinessProbe.enabled Enable readinessProbe on Compactor nodes
  ## @param compactor.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param compactor.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param compactor.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param compactor.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param compactor.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param compactor.startupProbe.enabled Enable startupProbe on Compactor containers
  ## @param compactor.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param compactor.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param compactor.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param compactor.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param compactor.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param compactor.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param compactor.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param compactor.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## compactor resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param compactor.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if compactor.resources is set (compactor.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param compactor.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 1000m
      memory: 2Gi
    limits:
      cpu: 2000m
      memory: 4Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param compactor.podSecurityContext.enabled Enabled Compactor pods' Security Context
  ## @param compactor.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param compactor.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param compactor.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param compactor.podSecurityContext.fsGroup Set Compactor pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param compactor.containerSecurityContext.enabled Enable containers' Security Context
  ## @param compactor.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param compactor.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param compactor.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param compactor.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param compactor.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param compactor.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param compactor.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param compactor.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param compactor.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param compactor.lifecycleHooks for the compactor container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param compactor.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param compactor.hostAliases compactor pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param compactor.podLabels Extra labels for compactor pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param compactor.podAnnotations Annotations for compactor pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param compactor.podAffinityPreset Pod affinity preset. Ignored if `compactor.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param compactor.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `compactor.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node compactor.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param compactor.nodeAffinityPreset.type Node affinity preset type. Ignored if `compactor.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param compactor.nodeAffinityPreset.key Node label key to match. Ignored if `compactor.affinity` is set
    ##
    key: ""
    ## @param compactor.nodeAffinityPreset.values Node label values to match. Ignored if `compactor.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param compactor.affinity Affinity for Compactor pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `compactor.podAffinityPreset`, `compactor.podAntiAffinityPreset`, and `compactor.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param compactor.nodeSelector Node labels for Compactor pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    role: loki-backend-node
  ## @param compactor.tolerations Tolerations for Compactor pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations:
    - key: "loki-backend-node"
      value: "true"
      effect: "NoSchedule"
  ## @param compactor.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param compactor.priorityClassName Compactor pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param compactor.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param compactor.updateStrategy.type Compactor statefulset strategy type
  ## @param compactor.updateStrategy.rollingUpdate [object,nullable] Compactor statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param compactor.extraVolumes Optionally specify extra list of additional volumes for the Compactor pod(s)
  ##
  extraVolumes: []
  ## @param compactor.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Compactor container(s)
  ##
  extraVolumeMounts: []
  ## @param compactor.sidecars Add additional sidecar containers to the Compactor pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param compactor.initContainers Add additional init containers to the Compactor pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## Enable persistence using Persistent Volume Claims
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param compactor.persistence.enabled Enable persistence in Compactor instances
    ##
    enabled: true
    ## @param compactor.persistence.existingClaim Name of an existing PVC to use
    ##
    existingClaim: ""
    ## @param compactor.persistence.storageClass PVC Storage Class for Memcached data volume
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    storageClass: ""
    ## @param compactor.persistence.accessModes PVC Access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param compactor.persistence.size PVC Storage Request for Memcached data volume
    ##
    size: 20Gi
    ## @param compactor.persistence.annotations Additional PVC annotations
    ##
    annotations: {}
    ## @param compactor.persistence.selector Selector to match an existing Persistent Volume for Compactor's data PVC
    ## If set, the PVC can't have a PV dynamically provisioned for it
    ## E.g.
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
    ## @param compactor.persistence.dataSource PVC data source
    ##
    dataSource: {}
  ## @section Compactor Traffic Exposure Parameters
  ##

  ## compactor service parameters
  ##
  service:
    ## @param compactor.service.type Compactor service type
    ##
    type: ClusterIP
    ## @param compactor.service.ports.http Compactor HTTP service port
    ##
    ports:
      http: 3100
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param compactor.service.nodePorts.http Node port for HTTP
    ##
    nodePorts:
      http: ""
    ## @param compactor.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param compactor.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param compactor.service.clusterIP Compactor service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param compactor.service.loadBalancerIP Compactor service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param compactor.service.loadBalancerSourceRanges Compactor service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param compactor.service.externalTrafficPolicy Compactor service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param compactor.service.annotations Additional custom annotations for Compactor service
    ##
    annotations: {}
    ## @param compactor.service.extraPorts Extra ports to expose in the Compactor service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param compactor.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param compactor.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param compactor.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param compactor.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param compactor.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param compactor.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param compactor.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Gateway Deployment Parameters
##
gateway:
  ## @param gateway.enabled Enable Gateway deployment
  ##
  enabled: true
  ## Bitnami Nginx image
  ## ref: https://hub.docker.com/r/bitnami/grafana-nginx/tags/
  ## @param gateway.image.registry [default: REGISTRY_NAME] Nginx image registry
  ## @param gateway.image.repository [default: REPOSITORY_NAME/nginx] Nginx image repository
  ## @skip gateway.image.tag Nginx image tag (immutable tags are recommended)
  ## @param gateway.image.digest Nginx image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param gateway.image.pullPolicy Nginx image pull policy
  ## @param gateway.image.pullSecrets Nginx image pull secrets
  ## @param gateway.image.debug Enable debugging in the initialization process
  ##
  image:
    registry: docker.io
    repository: bitnami/nginx
    tag: 1.25.4-debian-12-r3
    digest: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
    debug: false
  ## @param gateway.extraEnvVars Array with extra environment variables to add to gateway nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param gateway.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for gateway nodes
  ##
  extraEnvVarsCM: ""
  ## @param gateway.extraEnvVarsSecret Name of existing Secret containing extra env vars for gateway nodes
  ##
  extraEnvVarsSecret: ""
  ## @param gateway.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param gateway.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param gateway.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param gateway.verboseLogging Show the gateway access_log
  ##
  verboseLogging: false
  ## @param gateway.replicaCount Number of Gateway replicas to deploy
  ##
  replicaCount: 1
  ## @param gateway.auth.enabled Enable basic auth
  ## @param gateway.auth.username Basic auth username
  ## @param gateway.auth.password Basic auth password
  ## @param gateway.auth.existingSecret Name of a secret containing the Basic auth password
  ##
  auth:
    enabled: false
    username: "user"
    password: ""
    existingSecret: ""
  ## Configure extra options for Gateway containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param gateway.livenessProbe.enabled Enable livenessProbe on Gateway nodes
  ## @param gateway.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param gateway.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param gateway.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param gateway.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param gateway.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param gateway.readinessProbe.enabled Enable readinessProbe on Gateway nodes
  ## @param gateway.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param gateway.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param gateway.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param gateway.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param gateway.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param gateway.startupProbe.enabled Enable startupProbe on Gateway containers
  ## @param gateway.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param gateway.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param gateway.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param gateway.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param gateway.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 10
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param gateway.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param gateway.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param gateway.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param gateway.containerPorts.http Gateway HTTP port
  ##
  containerPorts:
    http: 8080
  ## gateway resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param gateway.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if gateway.resources is set (gateway.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param gateway.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 500m
      memory: 512Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param gateway.podSecurityContext.enabled Enabled Gateway pods' Security Context
  ## @param gateway.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param gateway.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param gateway.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param gateway.podSecurityContext.fsGroup Set Gateway pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param gateway.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param gateway.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param gateway.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param gateway.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param gateway.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param gateway.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param gateway.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param gateway.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param gateway.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param gateway.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param gateway.lifecycleHooks for the gateway container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param gateway.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param gateway.hostAliases gateway pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param gateway.podLabels Extra labels for gateway pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param gateway.podAnnotations Annotations for gateway pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param gateway.podAffinityPreset Pod affinity preset. Ignored if `gateway.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param gateway.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `gateway.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node gateway.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param gateway.nodeAffinityPreset.type Node affinity preset type. Ignored if `gateway.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param gateway.nodeAffinityPreset.key Node label key to match. Ignored if `gateway.affinity` is set
    ##
    key: ""
    ## @param gateway.nodeAffinityPreset.values Node label values to match. Ignored if `gateway.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param gateway.affinity Affinity for Gateway pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `gateway.podAffinityPreset`, `gateway.podAntiAffinityPreset`, and `gateway.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param gateway.nodeSelector Node labels for Gateway pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param gateway.tolerations Tolerations for Gateway pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param gateway.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param gateway.priorityClassName Gateway pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param gateway.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param gateway.updateStrategy.type Gateway statefulset strategy type
  ## @param gateway.updateStrategy.rollingUpdate [object,nullable] Gateway statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param gateway.extraVolumes Optionally specify extra list of additional volumes for the Gateway pod(s)
  ##
  extraVolumes: []
  ## @param gateway.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Gateway container(s)
  ##
  extraVolumeMounts: []
  ## @param gateway.sidecars Add additional sidecar containers to the Gateway pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param gateway.initContainers Add additional init containers to the Gateway pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Gateway Traffic Exposure Parameters
  ##

  ## gateway service parameters
  ##
  service:
    ## @param gateway.service.type Gateway service type
    ##
    type: ClusterIP
    ## @param gateway.service.ports.http Gateway HTTP service port
    ##
    ports:
      http: 80
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param gateway.service.nodePorts.http Node port for HTTP
    ##
    nodePorts:
      http: ""
    ## @param gateway.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param gateway.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param gateway.service.clusterIP Gateway service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param gateway.service.loadBalancerIP Gateway service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param gateway.service.loadBalancerSourceRanges Gateway service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param gateway.service.externalTrafficPolicy Gateway service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param gateway.service.annotations Additional custom annotations for Gateway service
    ##
    annotations: {}
    ## @param gateway.service.extraPorts Extra ports to expose in the Gateway service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param gateway.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param gateway.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param gateway.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param gateway.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param gateway.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param gateway.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param gateway.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
  ## Configure the ingress resource that allows you to access the Loki Gateway installation
  ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/
  ##
  ingress:
    ## @param gateway.ingress.enabled Enable ingress record generation for Loki Gateway
    ##
    enabled: false
    ## @param gateway.ingress.pathType Ingress path type
    ##
    pathType: ImplementationSpecific
    ## @param gateway.ingress.apiVersion Force Ingress API version (automatically detected if not set)
    ##
    apiVersion: ""
    ## @param gateway.ingress.ingressClassName IngressClass that will be be used to implement the Ingress (Kubernetes 1.18+)
    ## This is supported in Kubernetes 1.18+ and required if you have more than one IngressClass marked as the default for your cluster .
    ## ref: https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/
    ##
    ingressClassName: ""
    ## @param gateway.ingress.hostname Default host for the ingress record
    ##
    hostname: grafana-loki.local
    ## @param gateway.ingress.path Default path for the ingress record
    ## NOTE: You may need to set this to '/*' in order to use this with ALB ingress controllers
    ##
    path: /
    ## @param gateway.ingress.annotations Additional annotations for the Ingress resource. To enable certificate autogeneration, place here your cert-manager annotations.
    ## For a full list of possible ingress annotations, please see
    ## ref: https://github.com/kubernetes/ingress-nginx/blob/main/docs/user-guide/nginx-configuration/annotations.md
    ## Use this parameter to set the required annotations for cert-manager, see
    ## ref: https://cert-manager.io/docs/usage/ingress/#supported-annotations
    ##
    ## e.g:
    ## annotations:
    ##   kubernetes.io/ingress.class: nginx
    ##   cert-manager.io/cluster-issuer: cluster-issuer-name
    ##
    annotations: {}
    ## @param gateway.ingress.tls Enable TLS configuration for the host defined at `ingress.hostname` parameter
    ## TLS certificates will be retrieved from a TLS secret with name: `{{- printf "%s-tls" .Values.ingress.hostname }}`
    ## You can:
    ##   - Use the `ingress.secrets` parameter to create this TLS secret
    ##   - Rely on cert-manager to create it by setting the corresponding annotations
    ##   - Rely on Helm to create self-signed certificates by setting `ingress.selfSigned=true`
    ##
    tls: false
    ## @param gateway.ingress.selfSigned Create a TLS secret for this ingress record using self-signed certificates generated by Helm
    ##
    selfSigned: false
    ## @param gateway.ingress.extraHosts An array with additional hostname(s) to be covered with the ingress record
    ## e.g:
    ## extraHosts:
    ##   - name: Loki Gateway.local
    ##     path: /
    ##
    extraHosts: []
    ## @param gateway.ingress.extraPaths An array with additional arbitrary paths that may need to be added to the ingress under the main host
    ## e.g:
    ## extraPaths:
    ## - path: /*
    ##   backend:
    ##     serviceName: ssl-redirect
    ##     servicePort: use-annotation
    ##
    extraPaths: []
    ## @param gateway.ingress.extraTls TLS configuration for additional hostname(s) to be covered with this ingress record
    ## ref: https://kubernetes.io/docs/concepts/services-networking/ingress/#tls
    ## e.g:
    ## extraTls:
    ## - hosts:
    ##     - Loki Gateway.local
    ##   secretName: Loki Gateway.local-tls
    ##
    extraTls: []
    ## @param gateway.ingress.secrets Custom TLS certificates as secrets
    ## NOTE: 'key' and 'certificate' are expected in PEM format
    ## NOTE: 'name' should line up with a 'secretName' set further up
    ## If it is not set and you're using cert-manager, this is unneeded, as it will create a secret for you with valid certificates
    ## If it is not set and you're NOT using cert-manager either, self-signed certificates will be created valid for 365 days
    ## It is also possible to create and manage the certificates outside of this helm chart
    ## Please see README.md for more information
    ## e.g:
    ## secrets:
    ##   - name: Loki Gateway.local-tls
    ##     key: |-
    ##       -----BEGIN RSA PRIVATE KEY-----
    ##       ...
    ##       -----END RSA PRIVATE KEY-----
    ##     certificate: |-
    ##       -----BEGIN CERTIFICATE-----
    ##       ...
    ##       -----END CERTIFICATE-----
    ##
    secrets: []
## @section index-gateway Deployment Parameters
##
indexGateway:
  ## @param indexGateway.enabled Enable index-gateway deployment
  ##
  enabled: false
  ## @param indexGateway.extraEnvVars Array with extra environment variables to add to indexGateway nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param indexGateway.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for indexGateway nodes
  ##
  extraEnvVarsCM: ""
  ## @param indexGateway.extraEnvVarsSecret Name of existing Secret containing extra env vars for indexGateway nodes
  ##
  extraEnvVarsSecret: ""
  ## @param indexGateway.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param indexGateway.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param indexGateway.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param indexGateway.replicaCount Number of index-gateway replicas to deploy
  ##
  replicaCount: 1
  ## @param indexGateway.podManagementPolicy podManagementPolicy to manage scaling operation
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#pod-management-policies
  ##
  podManagementPolicy: ""
  ## Configure extra options for index-gateway containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param indexGateway.livenessProbe.enabled Enable livenessProbe on index-gateway nodes
  ## @param indexGateway.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param indexGateway.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param indexGateway.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param indexGateway.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param indexGateway.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param indexGateway.readinessProbe.enabled Enable readinessProbe on index-gateway nodes
  ## @param indexGateway.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param indexGateway.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param indexGateway.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param indexGateway.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param indexGateway.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 60
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param indexGateway.startupProbe.enabled Enable startupProbe on index-gateway containers
  ## @param indexGateway.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param indexGateway.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param indexGateway.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param indexGateway.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param indexGateway.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param indexGateway.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param indexGateway.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param indexGateway.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## indexGateway resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param indexGateway.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if indexGateway.resources is set (indexGateway.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param indexGateway.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param indexGateway.podSecurityContext.enabled Enabled index-gateway pods' Security Context
  ## @param indexGateway.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param indexGateway.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param indexGateway.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param indexGateway.podSecurityContext.fsGroup Set index-gateway pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param indexGateway.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param indexGateway.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param indexGateway.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param indexGateway.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param indexGateway.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param indexGateway.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param indexGateway.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param indexGateway.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param indexGateway.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param indexGateway.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param indexGateway.lifecycleHooks for the indexGateway container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param indexGateway.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param indexGateway.hostAliases indexGateway pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param indexGateway.podLabels Extra labels for indexGateway pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param indexGateway.podAnnotations Annotations for indexGateway pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param indexGateway.podAffinityPreset Pod affinity preset. Ignored if `indexGateway.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param indexGateway.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `indexGateway.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node indexGateway.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param indexGateway.nodeAffinityPreset.type Node affinity preset type. Ignored if `indexGateway.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param indexGateway.nodeAffinityPreset.key Node label key to match. Ignored if `indexGateway.affinity` is set
    ##
    key: ""
    ## @param indexGateway.nodeAffinityPreset.values Node label values to match. Ignored if `indexGateway.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param indexGateway.affinity Affinity for index-gateway pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `indexGateway.podAffinityPreset`, `indexGateway.podAntiAffinityPreset`, and `indexGateway.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param indexGateway.nodeSelector Node labels for index-gateway pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param indexGateway.tolerations Tolerations for index-gateway pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param indexGateway.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param indexGateway.priorityClassName index-gateway pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param indexGateway.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param indexGateway.updateStrategy.type index-gateway statefulset strategy type
  ## @param indexGateway.updateStrategy.rollingUpdate [object,nullable] index-gateway statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param indexGateway.extraVolumes Optionally specify extra list of additional volumes for the index-gateway pod(s)
  ##
  extraVolumes: []
  ## @param indexGateway.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the index-gateway container(s)
  ##
  extraVolumeMounts: []
  ## @param indexGateway.sidecars Add additional sidecar containers to the index-gateway pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param indexGateway.initContainers Add additional init containers to the index-gateway pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section index-gateway Traffic Exposure Parameters
  ##

  ## indexGateway service parameters
  ##
  service:
    ## @param indexGateway.service.type index-gateway service type
    ##
    type: ClusterIP
    ## @param indexGateway.service.ports.http index-gateway HTTP service port
    ## @param indexGateway.service.ports.grpc index-gateway GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param indexGateway.service.nodePorts.http Node port for HTTP
    ## @param indexGateway.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param indexGateway.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param indexGateway.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param indexGateway.service.clusterIP index-gateway service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param indexGateway.service.loadBalancerIP index-gateway service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param indexGateway.service.loadBalancerSourceRanges index-gateway service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param indexGateway.service.externalTrafficPolicy index-gateway service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param indexGateway.service.annotations Additional custom annotations for index-gateway service
    ##
    annotations: {}
    ## @param indexGateway.service.extraPorts Extra ports to expose in the index-gateway service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param indexGateway.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param indexGateway.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param indexGateway.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param indexGateway.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param indexGateway.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param indexGateway.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param indexGateway.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Distributor Deployment Parameters
##
distributor:
  ## @param distributor.extraEnvVars Array with extra environment variables to add to distributor nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param distributor.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for distributor nodes
  ##
  extraEnvVarsCM: ""
  ## @param distributor.extraEnvVarsSecret Name of existing Secret containing extra env vars for distributor nodes
  ##
  extraEnvVarsSecret: ""
  ## @param distributor.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param distributor.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param distributor.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param distributor.replicaCount Number of Distributor replicas to deploy
  ##
  replicaCount: 3
  ## Configure extra options for Distributor containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param distributor.livenessProbe.enabled Enable livenessProbe on Distributor nodes
  ## @param distributor.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param distributor.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param distributor.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param distributor.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param distributor.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param distributor.readinessProbe.enabled Enable readinessProbe on Distributor nodes
  ## @param distributor.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param distributor.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param distributor.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param distributor.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param distributor.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param distributor.startupProbe.enabled Enable startupProbe on Distributor containers
  ## @param distributor.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param distributor.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param distributor.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param distributor.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param distributor.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param distributor.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param distributor.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param distributor.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## distributor resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param distributor.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if distributor.resources is set (distributor.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param distributor.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 1000m
      memory: 2Gi
    limits:
      cpu: 2000m
      memory: 4Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param distributor.podSecurityContext.enabled Enabled Distributor pods' Security Context
  ## @param distributor.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param distributor.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param distributor.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param distributor.podSecurityContext.fsGroup Set Distributor pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param distributor.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param distributor.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param distributor.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param distributor.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param distributor.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param distributor.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param distributor.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param distributor.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param distributor.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param distributor.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param distributor.lifecycleHooks for the distributor container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param distributor.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param distributor.hostAliases distributor pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param distributor.podLabels Extra labels for distributor pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param distributor.podAnnotations Annotations for distributor pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param distributor.podAffinityPreset Pod affinity preset. Ignored if `distributor.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param distributor.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `distributor.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node distributor.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param distributor.nodeAffinityPreset.type Node affinity preset type. Ignored if `distributor.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param distributor.nodeAffinityPreset.key Node label key to match. Ignored if `distributor.affinity` is set
    ##
    key: ""
    ## @param distributor.nodeAffinityPreset.values Node label values to match. Ignored if `distributor.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param distributor.affinity Affinity for Distributor pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `distributor.podAffinityPreset`, `distributor.podAntiAffinityPreset`, and `distributor.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param distributor.nodeSelector Node labels for Distributor pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    role: loki-write-node
  ## @param distributor.tolerations Tolerations for Distributor pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations:
    - key: loki-write-node
      value: "true"
      effect: NoSchedule
  ## @param distributor.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param distributor.priorityClassName Distributor pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param distributor.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param distributor.updateStrategy.type Distributor statefulset strategy type
  ## @param distributor.updateStrategy.rollingUpdate [object,nullable] Distributor statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param distributor.extraVolumes Optionally specify extra list of additional volumes for the Distributor pod(s)
  ##
  extraVolumes: []
  ## @param distributor.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the Distributor container(s)
  ##
  extraVolumeMounts: []
  ## @param distributor.sidecars Add additional sidecar containers to the Distributor pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param distributor.initContainers Add additional init containers to the Distributor pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Distributor Traffic Exposure Parameters
  ##

  ## distributor service parameters
  ##
  service:
    ## @param distributor.service.type Distributor service type
    ##
    type: ClusterIP
    ## @param distributor.service.ports.http Distributor HTTP service port
    ## @param distributor.service.ports.grpc Distributor GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param distributor.service.nodePorts.http Node port for HTTP
    ## @param distributor.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param distributor.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param distributor.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param distributor.service.clusterIP Distributor service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param distributor.service.loadBalancerIP Distributor service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param distributor.service.loadBalancerSourceRanges Distributor service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param distributor.service.externalTrafficPolicy Distributor service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param distributor.service.annotations Additional custom annotations for Distributor service
    ##
    annotations: {}
    ## @param distributor.service.extraPorts Extra ports to expose in the Distributor service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param distributor.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param distributor.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param distributor.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param distributor.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param distributor.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param distributor.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param distributor.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Ingester Deployment Parameters
##
ingester:
  ## @param ingester.extraEnvVars Array with extra environment variables to add to ingester nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param ingester.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for ingester nodes
  ##
  extraEnvVarsCM: ""
  ## @param ingester.extraEnvVarsSecret Name of existing Secret containing extra env vars for ingester nodes
  ##
  extraEnvVarsSecret: ""
  ## @param ingester.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param ingester.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param ingester.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param ingester.replicaCount Number of Ingester replicas to deploy
  ##
  replicaCount: 3
  ## Configure extra options for Ingester containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param ingester.livenessProbe.enabled Enable livenessProbe on Ingester nodes
  ## @param ingester.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param ingester.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param ingester.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param ingester.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param ingester.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param ingester.readinessProbe.enabled Enable readinessProbe on Ingester nodes
  ## @param ingester.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param ingester.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param ingester.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param ingester.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param ingester.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param ingester.startupProbe.enabled Enable startupProbe on Ingester containers
  ## @param ingester.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param ingester.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param ingester.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param ingester.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param ingester.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param ingester.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param ingester.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param ingester.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param ingester.lifecycleHooks for the ingester container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## ingester resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param ingester.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if ingester.resources is set (ingester.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param ingester.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 2000m
      memory: 4Gi
    limits:
      cpu: 4000m
      memory: 8Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param ingester.podSecurityContext.enabled Enabled Ingester pods' Security Context
  ## @param ingester.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param ingester.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param ingester.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param ingester.podSecurityContext.fsGroup Set Ingester pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param ingester.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param ingester.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param ingester.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param ingester.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param ingester.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param ingester.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param ingester.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param ingester.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param ingester.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param ingester.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param ingester.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param ingester.hostAliases ingester pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param ingester.podLabels Extra labels for ingester pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param ingester.podAnnotations Annotations for ingester pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param ingester.podAffinityPreset Pod affinity preset. Ignored if `ingester.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param ingester.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `ingester.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node ingester.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param ingester.nodeAffinityPreset.type Node affinity preset type. Ignored if `ingester.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param ingester.nodeAffinityPreset.key Node label key to match. Ignored if `ingester.affinity` is set
    ##
    key: ""
    ## @param ingester.nodeAffinityPreset.values Node label values to match. Ignored if `ingester.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param ingester.affinity Affinity for ingester pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `ingester.podAffinityPreset`, `ingester.podAntiAffinityPreset`, and `ingester.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param ingester.nodeSelector Node labels for Ingester pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    role: loki-write-node
  ## @param ingester.tolerations Tolerations for Ingester pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations:
    - key: loki-write-node
      value: "true"
      effect: NoSchedule
  ## @param ingester.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param ingester.podManagementPolicy podManagementPolicy to manage scaling operation
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#pod-management-policies
  ##
  podManagementPolicy: ""
  ## @param ingester.priorityClassName Ingester pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param ingester.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param ingester.updateStrategy.type Ingester statefulset strategy type
  ## @param ingester.updateStrategy.rollingUpdate [object,nullable] Ingester statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param ingester.extraVolumes Optionally specify extra list of additional volumes for the Ingester pod(s)
  ##
  extraVolumes: []
  ## @param ingester.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the ingester container(s)
  ##
  extraVolumeMounts: []
  ## @param ingester.sidecars Add additional sidecar containers to the Ingester pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param ingester.initContainers Add additional init containers to the Ingester pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Ingester Persistence Parameters
  ##

  ## Enable persistence using Persistent Volume Claims
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param ingester.persistence.enabled Enable persistence in Ingester instances
    ##
    enabled: true
    ## @param ingester.persistence.storageClass PVC Storage Class for Memcached data volume
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    storageClass: ""
    ## @param ingester.persistence.subPath The subdirectory of the volume to mount to
    ##
    subPath: ""
    ## @param ingester.persistence.accessModes PVC Access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param ingester.persistence.size PVC Storage Request for Memcached data volume
    ##
    size: 20Gi
    ## @param ingester.persistence.annotations Additional PVC annotations
    ##
    annotations: {}
    ## @param ingester.persistence.selector Selector to match an existing Persistent Volume for Ingester's data PVC
    ## If set, the PVC can't have a PV dynamically provisioned for it
    ## E.g.
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
  ## @section Ingester Traffic Exposure Parameters
  ##

  ## ingester service parameters
  ##
  service:
    ## @param ingester.service.type Ingester service type
    ##
    type: ClusterIP
    ## @param ingester.service.ports.http Ingester HTTP service port
    ## @param ingester.service.ports.grpc Ingester GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param ingester.service.nodePorts.http Node port for HTTP
    ## @param ingester.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param ingester.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param ingester.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param ingester.service.clusterIP Ingester service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param ingester.service.loadBalancerIP Ingester service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param ingester.service.loadBalancerSourceRanges Ingester service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param ingester.service.externalTrafficPolicy Ingester service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param ingester.service.annotations Additional custom annotations for Ingester service
    ##
    annotations: {}
    ## @param ingester.service.extraPorts Extra ports to expose in the Ingester service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param ingester.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param ingester.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param ingester.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param ingester.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param ingester.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param ingester.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param ingester.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Querier Deployment Parameters
##
querier:
  ## @param querier.replicaCount Number of Querier replicas to deploy
  ##
  replicaCount: 3
  ## @param querier.extraEnvVars Array with extra environment variables to add to Querier nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param querier.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for Querier nodes
  ##
  extraEnvVarsCM: ""
  ## @param querier.extraEnvVarsSecret Name of existing Secret containing extra env vars for Querier nodes
  ##
  extraEnvVarsSecret: ""
  ## @param querier.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param querier.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param querier.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param querier.podManagementPolicy podManagementPolicy to manage scaling operation
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#pod-management-policies
  ##
  podManagementPolicy: ""
  ## Configure extra options for Querier containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param querier.livenessProbe.enabled Enable livenessProbe on Querier nodes
  ## @param querier.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param querier.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param querier.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param querier.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param querier.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param querier.readinessProbe.enabled Enable readinessProbe on Querier nodes
  ## @param querier.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param querier.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param querier.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param querier.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param querier.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param querier.startupProbe.enabled Enable startupProbe on Querier containers
  ## @param querier.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param querier.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param querier.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param querier.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param querier.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param querier.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param querier.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param querier.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## querier resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param querier.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if querier.resources is set (querier.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param querier.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 1500m
      memory: 3Gi
    limits:
      cpu: 3000m
      memory: 6Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param querier.podSecurityContext.enabled Enabled Querier pods' Security Context
  ## @param querier.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param querier.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param querier.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param querier.podSecurityContext.fsGroup Set Querier pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param querier.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param querier.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param querier.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param querier.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param querier.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param querier.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param querier.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param querier.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param querier.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param querier.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param querier.lifecycleHooks for the Querier container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param querier.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param querier.hostAliases querier pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param querier.podLabels Extra labels for querier pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param querier.podAnnotations Annotations for querier pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param querier.podAffinityPreset Pod affinity preset. Ignored if `querier.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param querier.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `querier.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node querier.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param querier.nodeAffinityPreset.type Node affinity preset type. Ignored if `querier.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param querier.nodeAffinityPreset.key Node label key to match. Ignored if `querier.affinity` is set
    ##
    key: ""
    ## @param querier.nodeAffinityPreset.values Node label values to match. Ignored if `querier.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param querier.affinity Affinity for Querier pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `querier.podAffinityPreset`, `querier.podAntiAffinityPreset`, and `querier.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param querier.nodeSelector Node labels for Querier pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    role: loki-read-node
  ## @param querier.tolerations Tolerations for Querier pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations:
    - key: loki-read-node
      value: "true"
      effect: NoSchedule
  ## @param querier.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param querier.priorityClassName Querier pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param querier.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param querier.updateStrategy.type Querier statefulset strategy type
  ## @param querier.updateStrategy.rollingUpdate [object,nullable] Querier statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param querier.extraVolumes Optionally specify extra list of additional volumes for the Querier pod(s)
  ##
  extraVolumes: []
  ## @param querier.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the querier container(s)
  ##
  extraVolumeMounts: []
  ## @param querier.sidecars Add additional sidecar containers to the Querier pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param querier.initContainers Add additional init containers to the Querier pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Querier Persistence Parameters
  ##

  ## Enable persistence using Persistent Volume Claims
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param querier.persistence.enabled Enable persistence in Querier instances
    ##
    enabled: true
    ## @param querier.persistence.storageClass PVC Storage Class for Memcached data volume
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    storageClass: ""
    ## @param querier.persistence.subPath The subdirectory of the volume to mount to
    ##
    subPath: ""
    ## @param querier.persistence.accessModes PVC Access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param querier.persistence.size PVC Storage Request for Memcached data volume
    ##
    size: 20Gi
    ## @param querier.persistence.annotations Additional PVC annotations
    ##
    annotations: {}
    ## @param querier.persistence.selector Selector to match an existing Persistent Volume for Querier's data PVC
    ## If set, the PVC can't have a PV dynamically provisioned for it
    ## E.g.
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
  ## @section Querier Traffic Exposure Parameters
  ##

  ## querier service parameters
  ##
  service:
    ## @param querier.service.type Querier service type
    ##
    type: ClusterIP
    ## @param querier.service.ports.http Querier HTTP service port
    ## @param querier.service.ports.grpc Querier GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param querier.service.nodePorts.http Node port for HTTP
    ## @param querier.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param querier.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param querier.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param querier.service.clusterIP Querier service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param querier.service.loadBalancerIP Querier service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param querier.service.loadBalancerSourceRanges Querier service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param querier.service.externalTrafficPolicy Querier service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param querier.service.annotations Additional custom annotations for Querier service
    ##
    annotations: {}
    ## @param querier.service.extraPorts Extra ports to expose in the Querier service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param querier.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param querier.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param querier.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param querier.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param querier.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param querier.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param querier.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Query Frontend Deployment Parameters
##
queryFrontend:
  ## @param queryFrontend.extraEnvVars Array with extra environment variables to add to queryFrontend nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param queryFrontend.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for queryFrontend nodes
  ##
  extraEnvVarsCM: ""
  ## @param queryFrontend.extraEnvVarsSecret Name of existing Secret containing extra env vars for queryFrontend nodes
  ##
  extraEnvVarsSecret: ""
  ## @param queryFrontend.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param queryFrontend.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param queryFrontend.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param queryFrontend.replicaCount Number of queryFrontend replicas to deploy
  ##
  replicaCount: 2
  ## Configure extra options for queryFrontend containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param queryFrontend.livenessProbe.enabled Enable livenessProbe on queryFrontend nodes
  ## @param queryFrontend.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param queryFrontend.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param queryFrontend.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param queryFrontend.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param queryFrontend.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param queryFrontend.readinessProbe.enabled Enable readinessProbe on queryFrontend nodes
  ## @param queryFrontend.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param queryFrontend.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param queryFrontend.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param queryFrontend.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param queryFrontend.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param queryFrontend.startupProbe.enabled Enable startupProbe on queryFrontend containers
  ## @param queryFrontend.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param queryFrontend.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param queryFrontend.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param queryFrontend.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param queryFrontend.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param queryFrontend.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param queryFrontend.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param queryFrontend.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## queryFrontend resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param queryFrontend.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if queryFrontend.resources is set (queryFrontend.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param queryFrontend.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param queryFrontend.podSecurityContext.enabled Enabled queryFrontend pods' Security Context
  ## @param queryFrontend.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param queryFrontend.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param queryFrontend.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param queryFrontend.podSecurityContext.fsGroup Set queryFrontend pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param queryFrontend.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param queryFrontend.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param queryFrontend.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param queryFrontend.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param queryFrontend.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param queryFrontend.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param queryFrontend.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param queryFrontend.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param queryFrontend.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param queryFrontend.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param queryFrontend.lifecycleHooks for the queryFrontend container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param queryFrontend.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param queryFrontend.hostAliases queryFrontend pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param queryFrontend.podLabels Extra labels for queryFrontend pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param queryFrontend.podAnnotations Annotations for queryFrontend pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param queryFrontend.podAffinityPreset Pod affinity preset. Ignored if `queryFrontend.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param queryFrontend.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `queryFrontend.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node queryFrontend.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param queryFrontend.nodeAffinityPreset.type Node affinity preset type. Ignored if `queryFrontend.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param queryFrontend.nodeAffinityPreset.key Node label key to match. Ignored if `queryFrontend.affinity` is set
    ##
    key: ""
    ## @param queryFrontend.nodeAffinityPreset.values Node label values to match. Ignored if `queryFrontend.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param queryFrontend.affinity Affinity for queryFrontend pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `queryFrontend.podAffinityPreset`, `queryFrontend.podAntiAffinityPreset`, and `queryFrontend.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param queryFrontend.nodeSelector Node labels for queryFrontend pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    role: loki-read-node
  ## @param queryFrontend.tolerations Tolerations for queryFrontend pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations:
    - key: loki-read-node
      value: "true"
      effect: NoSchedule

  ## @param queryFrontend.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param queryFrontend.priorityClassName queryFrontend pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param queryFrontend.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param queryFrontend.updateStrategy.type queryFrontend statefulset strategy type
  ## @param queryFrontend.updateStrategy.rollingUpdate [object,nullable] queryFrontend statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param queryFrontend.extraVolumes Optionally specify extra list of additional volumes for the queryFrontend pod(s)
  ##
  extraVolumes: []
  ## @param queryFrontend.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the queryFrontend container(s)
  ##
  extraVolumeMounts: []
  ## @param queryFrontend.sidecars Add additional sidecar containers to the queryFrontend pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param queryFrontend.initContainers Add additional init containers to the queryFrontend pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Query Frontend Traffic Exposure Parameters
  ##

  ## queryFrontend service parameters
  ##
  service:
    ## @param queryFrontend.service.type queryFrontend service type
    ##
    type: ClusterIP
    ## @param queryFrontend.service.ports.http queryFrontend HTTP service port
    ## @param queryFrontend.service.ports.grpc queryFrontend GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param queryFrontend.service.nodePorts.http Node port for HTTP
    ## @param queryFrontend.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param queryFrontend.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param queryFrontend.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param queryFrontend.service.clusterIP queryFrontend service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param queryFrontend.service.loadBalancerIP queryFrontend service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param queryFrontend.service.loadBalancerSourceRanges queryFrontend service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param queryFrontend.service.externalTrafficPolicy queryFrontend service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param queryFrontend.service.annotations Additional custom annotations for queryFrontend service
    ##
    annotations: {}
    ## @param queryFrontend.service.extraPorts Extra ports to expose in the queryFrontend service
    ##
    extraPorts: []
    ## Headless service properties
    ##
    headless:
      ## @param queryFrontend.service.headless.annotations Annotations for the headless service.
      ##
      annotations: {}
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param queryFrontend.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param queryFrontend.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param queryFrontend.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param queryFrontend.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param queryFrontend.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param queryFrontend.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param queryFrontend.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Query Scheduler Deployment Parameters
##
queryScheduler:
  ## @param queryScheduler.extraEnvVars Array with extra environment variables to add to queryScheduler nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param queryScheduler.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for queryScheduler nodes
  ##
  extraEnvVarsCM: ""
  ## @param queryScheduler.extraEnvVarsSecret Name of existing Secret containing extra env vars for queryScheduler nodes
  ##
  extraEnvVarsSecret: ""
  ## @param queryScheduler.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param queryScheduler.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param queryScheduler.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param queryScheduler.replicaCount Number of queryScheduler replicas to deploy
  ##
  replicaCount: 2
  ## Configure extra options for queryScheduler containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param queryScheduler.livenessProbe.enabled Enable livenessProbe on queryScheduler nodes
  ## @param queryScheduler.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param queryScheduler.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param queryScheduler.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param queryScheduler.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param queryScheduler.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param queryScheduler.minReadySeconds Minimum time to wait before performing readiness check
  ##
  minReadySeconds: 10
  ## @param queryScheduler.readinessProbe.enabled Enable readinessProbe on queryScheduler nodes
  ## @param queryScheduler.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param queryScheduler.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param queryScheduler.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param queryScheduler.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param queryScheduler.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param queryScheduler.startupProbe.enabled Enable startupProbe on queryScheduler containers
  ## @param queryScheduler.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param queryScheduler.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param queryScheduler.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param queryScheduler.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param queryScheduler.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param queryScheduler.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param queryScheduler.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param queryScheduler.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## queryScheduler resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param queryScheduler.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if queryScheduler.resources is set (queryScheduler.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "none"
  ## @param queryScheduler.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 1000m
      memory: 2Gi
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param queryScheduler.podSecurityContext.enabled Enabled queryScheduler pods' Security Context
  ## @param queryScheduler.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param queryScheduler.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param queryScheduler.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param queryScheduler.podSecurityContext.fsGroup Set queryScheduler pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param queryScheduler.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param queryScheduler.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param queryScheduler.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param queryScheduler.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param queryScheduler.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param queryScheduler.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param queryScheduler.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param queryScheduler.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param queryScheduler.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param queryScheduler.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param queryScheduler.lifecycleHooks for the queryScheduler container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param queryScheduler.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param queryScheduler.hostAliases queryScheduler pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param queryScheduler.podLabels Extra labels for queryScheduler pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param queryScheduler.podAnnotations Annotations for queryScheduler pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param queryScheduler.podAffinityPreset Pod affinity preset. Ignored if `queryScheduler.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param queryScheduler.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `queryScheduler.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node queryScheduler.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param queryScheduler.nodeAffinityPreset.type Node affinity preset type. Ignored if `queryScheduler.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param queryScheduler.nodeAffinityPreset.key Node label key to match. Ignored if `queryScheduler.affinity` is set
    ##
    key: ""
    ## @param queryScheduler.nodeAffinityPreset.values Node label values to match. Ignored if `queryScheduler.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param queryScheduler.affinity Affinity for queryScheduler pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `queryScheduler.podAffinityPreset`, `queryScheduler.podAntiAffinityPreset`, and `queryScheduler.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param queryScheduler.nodeSelector Node labels for queryScheduler pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector:
    role: loki-read-node
  ## @param queryScheduler.tolerations Tolerations for queryScheduler pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations:
    - key: loki-read-node
      value: "true"
      effect: NoSchedule
  ## @param queryScheduler.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param queryScheduler.priorityClassName queryScheduler pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param queryScheduler.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param queryScheduler.updateStrategy.type queryScheduler statefulset strategy type
  ## @param queryScheduler.updateStrategy.rollingUpdate [object,nullable] queryScheduler statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param queryScheduler.extraVolumes Optionally specify extra list of additional volumes for the queryScheduler pod(s)
  ##
  extraVolumes: []
  ## @param queryScheduler.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the queryScheduler container(s)
  ##
  extraVolumeMounts: []
  ## @param queryScheduler.sidecars Add additional sidecar containers to the queryScheduler pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param queryScheduler.initContainers Add additional init containers to the queryScheduler pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Query Scheduler Traffic Exposure Parameters
  ##

  ## queryScheduler service parameters
  ##
  service:
    ## @param queryScheduler.service.type queryScheduler service type
    ##
    type: ClusterIP
    ## @param queryScheduler.service.ports.http queryScheduler HTTP service port
    ## @param queryScheduler.service.ports.grpc queryScheduler GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param queryScheduler.service.nodePorts.http Node port for HTTP
    ## @param queryScheduler.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param queryScheduler.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param queryScheduler.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param queryScheduler.service.clusterIP queryScheduler service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param queryScheduler.service.loadBalancerIP queryScheduler service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param queryScheduler.service.loadBalancerSourceRanges queryScheduler service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param queryScheduler.service.externalTrafficPolicy queryScheduler service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param queryScheduler.service.annotations Additional custom annotations for queryScheduler service
    ##
    annotations: {}
    ## @param queryScheduler.service.extraPorts Extra ports to expose in the queryScheduler service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param queryScheduler.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param queryScheduler.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param queryScheduler.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param queryScheduler.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param queryScheduler.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param queryScheduler.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param queryScheduler.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Ruler Deployment Parameters
##
ruler:
  ## @param ruler.enabled Deploy ruler component
  ##
  enabled: false
  ## @param ruler.extraEnvVars Array with extra environment variables to add to ruler nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param ruler.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for ruler nodes
  ##
  extraEnvVarsCM: ""
  ## @param ruler.extraEnvVarsSecret Name of existing Secret containing extra env vars for ruler nodes
  ##
  extraEnvVarsSecret: ""
  ## @param ruler.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param ruler.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param ruler.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param ruler.podManagementPolicy podManagementPolicy to manage scaling operation
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#pod-management-policies
  ##
  podManagementPolicy: ""
  ## @param ruler.replicaCount Number of Ruler replicas to deploy
  ##
  replicaCount: 1
  ## Configure extra options for Ruler containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param ruler.livenessProbe.enabled Enable livenessProbe on Ruler nodes
  ## @param ruler.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param ruler.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param ruler.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param ruler.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param ruler.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param ruler.readinessProbe.enabled Enable readinessProbe on Ruler nodes
  ## @param ruler.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param ruler.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param ruler.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param ruler.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param ruler.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param ruler.startupProbe.enabled Enable startupProbe on Ruler containers
  ## @param ruler.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param ruler.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param ruler.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param ruler.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param ruler.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param ruler.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param ruler.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param ruler.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param ruler.lifecycleHooks for the ruler container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## ruler resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param ruler.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if ruler.resources is set (ruler.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param ruler.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param ruler.podSecurityContext.enabled Enabled Ruler pods' Security Context
  ## @param ruler.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param ruler.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param ruler.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param ruler.podSecurityContext.fsGroup Set Ruler pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param ruler.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param ruler.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param ruler.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param ruler.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param ruler.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param ruler.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param ruler.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param ruler.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param ruler.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param ruler.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param ruler.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param ruler.hostAliases ruler pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param ruler.podLabels Extra labels for ruler pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param ruler.podAnnotations Annotations for ruler pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param ruler.podAffinityPreset Pod affinity preset. Ignored if `ruler.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param ruler.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `ruler.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node ruler.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param ruler.nodeAffinityPreset.type Node affinity preset type. Ignored if `ruler.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param ruler.nodeAffinityPreset.key Node label key to match. Ignored if `ruler.affinity` is set
    ##
    key: ""
    ## @param ruler.nodeAffinityPreset.values Node label values to match. Ignored if `ruler.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param ruler.affinity Affinity for ruler pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `ruler.podAffinityPreset`, `ruler.podAntiAffinityPreset`, and `ruler.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param ruler.nodeSelector Node labels for Ruler pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param ruler.tolerations Tolerations for Ruler pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param ruler.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param ruler.priorityClassName Ruler pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param ruler.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param ruler.updateStrategy.type Ruler statefulset strategy type
  ## @param ruler.updateStrategy.rollingUpdate [object,nullable] Ruler statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param ruler.extraVolumes Optionally specify extra list of additional volumes for the Ruler pod(s)
  ##
  extraVolumes: []
  ## @param ruler.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the ruler container(s)
  ##
  extraVolumeMounts: []
  ## @param ruler.sidecars Add additional sidecar containers to the Ruler pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param ruler.initContainers Add additional init containers to the Ruler pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section Ruler Persistence Parameters
  ##

  ## Enable persistence using Persistent Volume Claims
  ## ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  ##
  persistence:
    ## @param ruler.persistence.enabled Enable persistence in Ruler instances
    ##
    enabled: true
    ## @param ruler.persistence.storageClass PVC Storage Class for Memcached data volume
    ## If defined, storageClassName: <storageClass>
    ## If set to "-", storageClassName: "", which disables dynamic provisioning
    ## If undefined (the default) or set to null, no storageClassName spec is
    ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
    ##   GKE, AWS & OpenStack)
    ##
    storageClass: ""
    ## @param ruler.persistence.subPath The subdirectory of the volume to mount to
    ##
    subPath: ""
    ## @param ruler.persistence.accessModes PVC Access modes
    ##
    accessModes:
      - ReadWriteOnce
    ## @param ruler.persistence.size PVC Storage Request for Memcached data volume
    ##
    size: 20Gi
    ## @param ruler.persistence.annotations Additional PVC annotations
    ##
    annotations: {}
    ## @param ruler.persistence.selector Selector to match an existing Persistent Volume for Ruler's data PVC
    ## If set, the PVC can't have a PV dynamically provisioned for it
    ## E.g.
    ## selector:
    ##   matchLabels:
    ##     app: my-app
    ##
    selector: {}
  ## @section Ruler Traffic Exposure Parameters
  ##

  ## ruler service parameters
  ##
  service:
    ## @param ruler.service.type Ruler service type
    ##
    type: ClusterIP
    ## @param ruler.service.ports.http Ruler HTTP service port
    ## @param ruler.service.ports.grpc Ruler GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param ruler.service.nodePorts.http Node port for HTTP
    ## @param ruler.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param ruler.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param ruler.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param ruler.service.clusterIP Ruler service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param ruler.service.loadBalancerIP Ruler service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param ruler.service.loadBalancerSourceRanges Ruler service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param ruler.service.externalTrafficPolicy Ruler service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param ruler.service.annotations Additional custom annotations for Ruler service
    ##
    annotations: {}
    ## @param ruler.service.extraPorts Extra ports to expose in the Ruler service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param ruler.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param ruler.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param ruler.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param ruler.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param ruler.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param ruler.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param ruler.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section table-manager Deployment Parameters
##
tableManager:
  ## @param tableManager.enabled Deploy table-manager
  ##
  enabled: false
  ## @param tableManager.extraEnvVars Array with extra environment variables to add to tableManager nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param tableManager.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for tableManager nodes
  ##
  extraEnvVarsCM: ""
  ## @param tableManager.extraEnvVarsSecret Name of existing Secret containing extra env vars for tableManager nodes
  ##
  extraEnvVarsSecret: ""
  ## @param tableManager.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param tableManager.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param tableManager.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param tableManager.replicaCount Number of table-manager replicas to deploy
  ##
  replicaCount: 1
  ## Configure extra options for table-manager containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param tableManager.livenessProbe.enabled Enable livenessProbe on table-manager nodes
  ## @param tableManager.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param tableManager.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param tableManager.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param tableManager.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param tableManager.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param tableManager.readinessProbe.enabled Enable readinessProbe on table-manager nodes
  ## @param tableManager.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param tableManager.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param tableManager.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param tableManager.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param tableManager.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param tableManager.startupProbe.enabled Enable startupProbe on table-manager containers
  ## @param tableManager.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param tableManager.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param tableManager.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param tableManager.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param tableManager.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param tableManager.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param tableManager.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param tableManager.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## tableManager resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param tableManager.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if tableManager.resources is set (tableManager.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param tableManager.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param tableManager.podSecurityContext.enabled Enabled table-manager pods' Security Context
  ## @param tableManager.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param tableManager.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param tableManager.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param tableManager.podSecurityContext.fsGroup Set table-manager pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 1001
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param tableManager.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param tableManager.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param tableManager.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param tableManager.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param tableManager.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param tableManager.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param tableManager.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param tableManager.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param tableManager.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param tableManager.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 1001
    runAsGroup: 1001
    runAsNonRoot: true
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param tableManager.lifecycleHooks for the tableManager container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## @param tableManager.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: false
  ## @param tableManager.hostAliases tableManager pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param tableManager.podLabels Extra labels for tableManager pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param tableManager.podAnnotations Annotations for tableManager pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param tableManager.podAffinityPreset Pod affinity preset. Ignored if `tableManager.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param tableManager.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `tableManager.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node tableManager.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param tableManager.nodeAffinityPreset.type Node affinity preset type. Ignored if `tableManager.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param tableManager.nodeAffinityPreset.key Node label key to match. Ignored if `tableManager.affinity` is set
    ##
    key: ""
    ## @param tableManager.nodeAffinityPreset.values Node label values to match. Ignored if `tableManager.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param tableManager.affinity Affinity for table-manager pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `tableManager.podAffinityPreset`, `tableManager.podAntiAffinityPreset`, and `tableManager.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param tableManager.nodeSelector Node labels for table-manager pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param tableManager.tolerations Tolerations for table-manager pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param tableManager.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param tableManager.priorityClassName table-manager pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param tableManager.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param tableManager.updateStrategy.type table-manager statefulset strategy type
  ## @param tableManager.updateStrategy.rollingUpdate [object,nullable] table-manager statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param tableManager.extraVolumes Optionally specify extra list of additional volumes for the table-manager pod(s)
  ##
  extraVolumes: []
  ## @param tableManager.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the table-manager container(s)
  ##
  extraVolumeMounts: []
  ## @param tableManager.sidecars Add additional sidecar containers to the table-manager pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param tableManager.initContainers Add additional init containers to the table-manager pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @section table-manager Traffic Exposure Parameters
  ##

  ## tableManager service parameters
  ##
  service:
    ## @param tableManager.service.type table-manager service type
    ##
    type: ClusterIP
    ## @param tableManager.service.ports.http table-manager HTTP service port
    ## @param tableManager.service.ports.grpc table-manager GRPC service port
    ##
    ports:
      http: 3100
      grpc: 9095
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param tableManager.service.nodePorts.http Node port for HTTP
    ## @param tableManager.service.nodePorts.grpc Node port for GRPC
    ##
    nodePorts:
      http: ""
      grpc: ""
    ## @param tableManager.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param tableManager.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param tableManager.service.clusterIP table-manager service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param tableManager.service.loadBalancerIP table-manager service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param tableManager.service.loadBalancerSourceRanges table-manager service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param tableManager.service.externalTrafficPolicy table-manager service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param tableManager.service.annotations Additional custom annotations for table-manager service
    ##
    annotations: {}
    ## @param tableManager.service.extraPorts Extra ports to expose in the table-manager service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param tableManager.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param tableManager.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param tableManager.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param tableManager.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param tableManager.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param tableManager.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param tableManager.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
## @section Promtail Deployment Parameters
##
promtail:
  ## @param promtail.enabled Deploy promtail
  ##
  enabled: false
  ## Bitnami Promtail image
  ## ref: https://hub.docker.com/r/bitnami/grafana-promtail/tags/
  ## @param promtail.image.registry [default: REGISTRY_NAME] Grafana Promtail image registry
  ## @param promtail.image.repository [default: REPOSITORY_NAME/promtail] Grafana Promtail image repository
  ## @skip promtail.image.tag Grafana Promtail image tag (immutable tags are recommended)
  ## @param promtail.image.digest Grafana Promtail image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param promtail.image.pullPolicy Grafana Promtail image pull policy
  ## @param promtail.image.pullSecrets Grafana Promtail image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/promtail
    tag: 2.9.5-debian-12-r1
    digest: ""
    ## Specify a imagePullPolicy
    ## Defaults to 'Always' if image tag is 'latest', else set to 'IfNotPresent'
    ## ref: https://kubernetes.io/docs/concepts/containers/images/#pre-pulled-images
    ##
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## @param promtail.extraEnvVars Array with extra environment variables to add to promtail nodes
  ## e.g:
  ## extraEnvVars:
  ##   - name: FOO
  ##     value: "bar"
  ##
  extraEnvVars: []
  ## @param promtail.extraEnvVarsCM Name of existing ConfigMap containing extra env vars for promtail nodes
  ##
  extraEnvVarsCM: ""
  ## @param promtail.extraEnvVarsSecret Name of existing Secret containing extra env vars for promtail nodes
  ##
  extraEnvVarsSecret: ""
  ## @param promtail.command Override default container command (useful when using custom images)
  ##
  command: []
  ## @param promtail.args Override default container args (useful when using custom images)
  ##
  args: []
  ## @param promtail.extraArgs Additional container args (will be concatenated to args, unless diagnosticMode is enabled)
  ##
  extraArgs: []
  ## @param promtail.containerPorts.http Promtail HTTP port
  ##
  containerPorts:
    http: 8080
  ## Configure extra options for Promtail containers' liveness, readiness and startup probes
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/#configure-probes
  ## @param promtail.livenessProbe.enabled Enable livenessProbe on Promtail nodes
  ## @param promtail.livenessProbe.initialDelaySeconds Initial delay seconds for livenessProbe
  ## @param promtail.livenessProbe.periodSeconds Period seconds for livenessProbe
  ## @param promtail.livenessProbe.timeoutSeconds Timeout seconds for livenessProbe
  ## @param promtail.livenessProbe.failureThreshold Failure threshold for livenessProbe
  ## @param promtail.livenessProbe.successThreshold Success threshold for livenessProbe
  ##
  livenessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param promtail.readinessProbe.enabled Enable readinessProbe on Promtail nodes
  ## @param promtail.readinessProbe.initialDelaySeconds Initial delay seconds for readinessProbe
  ## @param promtail.readinessProbe.periodSeconds Period seconds for readinessProbe
  ## @param promtail.readinessProbe.timeoutSeconds Timeout seconds for readinessProbe
  ## @param promtail.readinessProbe.failureThreshold Failure threshold for readinessProbe
  ## @param promtail.readinessProbe.successThreshold Success threshold for readinessProbe
  ##
  readinessProbe:
    enabled: true
    failureThreshold: 3
    initialDelaySeconds: 10
    periodSeconds: 10
    successThreshold: 1
    timeoutSeconds: 1
  ## @param promtail.startupProbe.enabled Enable startupProbe on Promtail containers
  ## @param promtail.startupProbe.initialDelaySeconds Initial delay seconds for startupProbe
  ## @param promtail.startupProbe.periodSeconds Period seconds for startupProbe
  ## @param promtail.startupProbe.timeoutSeconds Timeout seconds for startupProbe
  ## @param promtail.startupProbe.failureThreshold Failure threshold for startupProbe
  ## @param promtail.startupProbe.successThreshold Success threshold for startupProbe
  ##
  startupProbe:
    enabled: false
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 1
    failureThreshold: 15
    successThreshold: 1
  ## @param promtail.customLivenessProbe Custom livenessProbe that overrides the default one
  ##
  customLivenessProbe: {}
  ## @param promtail.customReadinessProbe Custom readinessProbe that overrides the default one
  ##
  customReadinessProbe: {}
  ## @param promtail.customStartupProbe Custom startupProbe that overrides the default one
  ##
  customStartupProbe: {}
  ## @param promtail.lifecycleHooks for the promtail container(s) to automate configuration before or after startup
  ##
  lifecycleHooks: {}
  ## promtail resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param promtail.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if promtail.resources is set (promtail.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param promtail.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Configure Pods Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param promtail.podSecurityContext.enabled Enabled Promtail pods' Security Context
  ## @param promtail.podSecurityContext.fsGroupChangePolicy Set filesystem group change policy
  ## @param promtail.podSecurityContext.sysctls Set kernel settings using the sysctl interface
  ## @param promtail.podSecurityContext.supplementalGroups Set filesystem extra groups
  ## @param promtail.podSecurityContext.fsGroup Set Promtail pod's Security Context fsGroup
  ##
  podSecurityContext:
    enabled: true
    fsGroupChangePolicy: Always
    sysctls: []
    supplementalGroups: []
    fsGroup: 0
  ## Configure Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-pod
  ## @param promtail.containerSecurityContext.enabled Enabled containers' Security Context
  ## @param promtail.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param promtail.containerSecurityContext.runAsUser Set containers' Security Context runAsUser
  ## @param promtail.containerSecurityContext.runAsGroup Set containers' Security Context runAsGroup
  ## @param promtail.containerSecurityContext.runAsNonRoot Set container's Security Context runAsNonRoot
  ## @param promtail.containerSecurityContext.privileged Set container's Security Context privileged
  ## @param promtail.containerSecurityContext.readOnlyRootFilesystem Set container's Security Context readOnlyRootFilesystem
  ## @param promtail.containerSecurityContext.allowPrivilegeEscalation Set container's Security Context allowPrivilegeEscalation
  ## @param promtail.containerSecurityContext.capabilities.drop List of capabilities to be dropped
  ## @param promtail.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ##
  containerSecurityContext:
    enabled: true
    seLinuxOptions: {}
    runAsUser: 0
    runAsGroup: 0
    runAsNonRoot: false
    privileged: false
    readOnlyRootFilesystem: true
    allowPrivilegeEscalation: false
    capabilities:
      drop: ["ALL"]
    seccompProfile:
      type: "RuntimeDefault"
  ## @param promtail.automountServiceAccountToken Mount Service Account token in pod
  ##
  automountServiceAccountToken: true
  ## @param promtail.hostAliases promtail pods host aliases
  ## https://kubernetes.io/docs/concepts/services-networking/add-entries-to-pod-etc-hosts-with-host-aliases/
  ##
  hostAliases: []
  ## @param promtail.podLabels Extra labels for promtail pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
  ##
  podLabels: {}
  ## @param promtail.podAnnotations Annotations for promtail pods
  ## ref: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
  ##
  podAnnotations: {}
  ## @param promtail.podAffinityPreset Pod affinity preset. Ignored if `promtail.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAffinityPreset: ""
  ## @param promtail.podAntiAffinityPreset Pod anti-affinity preset. Ignored if `promtail.affinity` is set. Allowed values: `soft` or `hard`
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#inter-pod-affinity-and-anti-affinity
  ##
  podAntiAffinityPreset: soft
  ## Node promtail.affinity preset
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/#node-affinity
  ##
  nodeAffinityPreset:
    ## @param promtail.nodeAffinityPreset.type Node affinity preset type. Ignored if `promtail.affinity` is set. Allowed values: `soft` or `hard`
    ##
    type: ""
    ## @param promtail.nodeAffinityPreset.key Node label key to match. Ignored if `promtail.affinity` is set
    ##
    key: ""
    ## @param promtail.nodeAffinityPreset.values Node label values to match. Ignored if `promtail.affinity` is set
    ## E.g.
    ## values:
    ##   - e2e-az1
    ##   - e2e-az2
    ##
    values: []
  ## @param promtail.affinity Affinity for promtail pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/#affinity-and-anti-affinity
  ## NOTE: `promtail.podAffinityPreset`, `promtail.podAntiAffinityPreset`, and `promtail.nodeAffinityPreset` will be ignored when it's set
  ##
  affinity: {}
  ## @param promtail.nodeSelector Node labels for Promtail pods assignment
  ## ref: https://kubernetes.io/docs/concepts/scheduling-eviction/assign-pod-node/
  ##
  nodeSelector: {}
  ## @param promtail.tolerations Tolerations for Promtail pods assignment
  ## ref: https://kubernetes.io/docs/concepts/configuration/taint-and-toleration/
  ##
  tolerations: []
  ## @param promtail.topologySpreadConstraints Topology Spread Constraints for pod assignment spread across your cluster among failure-domains
  ## Ref: https://kubernetes.io/docs/concepts/workloads/pods/pod-topology-spread-constraints/#spread-constraints-for-pods
  ##
  topologySpreadConstraints: []
  ## @param promtail.priorityClassName Promtail pods' priorityClassName
  ##
  priorityClassName: ""
  ## @param promtail.schedulerName Kubernetes pod scheduler registry
  ## https://kubernetes.io/docs/tasks/administer-cluster/configure-multiple-schedulers/
  ##
  schedulerName: ""
  ## @param promtail.updateStrategy.type Promtail statefulset strategy type
  ## @param promtail.updateStrategy.rollingUpdate [object,nullable] Promtail statefulset rolling update configuration parameters
  ## ref: https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/#update-strategies
  ##
  updateStrategy:
    type: RollingUpdate
    rollingUpdate: null
  ## @param promtail.extraVolumes Optionally specify extra list of additional volumes for the Promtail pod(s)
  ##
  extraVolumes: []
  ## @param promtail.extraVolumeMounts Optionally specify extra list of additional volumeMounts for the promtail container(s)
  ##
  extraVolumeMounts: []
  ## @param promtail.sidecars Add additional sidecar containers to the Promtail pod(s)
  ## e.g:
  ## sidecars:
  ##   - name: your-image-name
  ##     image: your-image
  ##     imagePullPolicy: Always
  ##     ports:
  ##       - name: portname
  ##         containerPort: 1234
  ##
  sidecars: []
  ## @param promtail.initContainers Add additional init containers to the Promtail pod(s)
  ## ref: https://kubernetes.io/docs/concepts/workloads/pods/init-containers/
  ## e.g:
  ## initContainers:
  ##  - name: your-image-name
  ##    image: your-image
  ##    imagePullPolicy: Always
  ##    command: ['sh', '-c', 'echo "hello world"']
  ##
  initContainers: []
  ## @param promtail.configuration [string] Promtail configuration
  ##
  configuration: |
    server:
      log_level: {{ .Values.promtail.logLevel }}
      http_listen_port: {{ .Values.promtail.containerPorts.http }}

    clients:
      - url: http://{{ include "grafana-loki.gateway.fullname" . }}:{{ .Values.gateway.service.ports.http }}/loki/api/v1/push
        {{- if .Values.gateway.auth.enabled }}
        basic_auth:
          # The username to use for basic auth
          username: {{ .Values.gateway.auth.username }}
          password_file: /bitnami/promtail/conf/secrets/password
        {{- end }}
    positions:
      filename: /run/promtail/positions.yaml

    scrape_configs:
      # See also https://github.com/grafana/loki/blob/master/production/ksonnet/promtail/scrape_config.libsonnet for reference
      - job_name: kubernetes-pods
        pipeline_stages:
          - cri: {}
        kubernetes_sd_configs:
          - role: pod
        relabel_configs:
          - source_labels:
              - __meta_kubernetes_pod_controller_name
            regex: ([0-9a-z-.]+?)(-[0-9a-f]{8,10})?
            action: replace
            target_label: __tmp_controller_name
          - source_labels:
              - __meta_kubernetes_pod_label_app_kubernetes_io_name
              - __meta_kubernetes_pod_label_app
              - __tmp_controller_name
              - __meta_kubernetes_pod_name
            regex: ^;*([^;]+)(;.*)?$
            action: replace
            target_label: app
          - source_labels:
              - __meta_kubernetes_pod_label_app_kubernetes_io_component
              - __meta_kubernetes_pod_label_component
            regex: ^;*([^;]+)(;.*)?$
            action: replace
            target_label: component
          - action: replace
            source_labels:
            - __meta_kubernetes_pod_node_name
            target_label: node_name
          - action: replace
            source_labels:
            - __meta_kubernetes_namespace
            target_label: namespace
          - action: replace
            replacement: $1
            separator: /
            source_labels:
            - namespace
            - app
            target_label: job
          - action: replace
            source_labels:
            - __meta_kubernetes_pod_name
            target_label: pod
          - action: replace
            source_labels:
            - __meta_kubernetes_pod_container_name
            target_label: container
          - action: replace
            replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
            - __meta_kubernetes_pod_uid
            - __meta_kubernetes_pod_container_name
            target_label: __path__
          - action: replace
            regex: true/(.*)
            replacement: /var/log/pods/*$1/*.log
            separator: /
            source_labels:
            - __meta_kubernetes_pod_annotationpresent_kubernetes_io_config_hash
            - __meta_kubernetes_pod_annotation_kubernetes_io_config_hash
            - __meta_kubernetes_pod_container_name
            target_label: __path__
  ## @param promtail.existingSecret Name of a Secret that contains the Promtail configuration
  ##
  existingSecret: ""
  ## @param promtail.logLevel Promtail logging level
  ##
  logLevel: info
  ## @section Promtail Traffic Exposure Parameters
  ##

  ## promtail service parameters
  ##
  service:
    ## @param promtail.service.type Promtail service type
    ##
    type: ClusterIP
    ## @param promtail.service.ports.http Promtail HTTP service port
    ##
    ports:
      http: 3100
    ## Node ports to expose
    ## NOTE: choose port between <30000-32767>
    ## @param promtail.service.nodePorts.http Node port for HTTP
    ##
    nodePorts:
      http: ""
    ## @param promtail.service.sessionAffinityConfig Additional settings for the sessionAffinity
    ## sessionAffinityConfig:
    ##   clientIP:
    ##     timeoutSeconds: 300
    ##
    sessionAffinityConfig: {}
    ## @param promtail.service.sessionAffinity Control where client requests go, to the same pod or round-robin
    ## Values: ClientIP or None
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/
    ##
    sessionAffinity: None
    ## @param promtail.service.clusterIP Promtail service Cluster IP
    ## e.g.:
    ## clusterIP: None
    ##
    clusterIP: ""
    ## @param promtail.service.loadBalancerIP Promtail service Load Balancer IP
    ## ref: https://kubernetes.io/docs/concepts/services-networking/service/#type-loadbalancer
    ##
    loadBalancerIP: ""
    ## @param promtail.service.loadBalancerSourceRanges Promtail service Load Balancer sources
    ## ref: https://kubernetes.io/docs/tasks/access-application-cluster/configure-cloud-provider-firewall/#restrict-access-for-loadbalancer-service
    ## e.g:
    ## loadBalancerSourceRanges:
    ##   - **********/24
    ##
    loadBalancerSourceRanges: []
    ## @param promtail.service.externalTrafficPolicy Promtail service external traffic policy
    ## ref https://kubernetes.io/docs/tasks/access-application-cluster/create-external-load-balancer/#preserving-the-client-source-ip
    ##
    externalTrafficPolicy: Cluster
    ## @param promtail.service.annotations Additional custom annotations for Promtail service
    ##
    annotations: {}
    ## @param promtail.service.extraPorts Extra ports to expose in the Promtail service
    ##
    extraPorts: []
  ## Network Policies
  ## Ref: https://kubernetes.io/docs/concepts/services-networking/network-policies/
  ##
  networkPolicy:
    ## @param promtail.networkPolicy.enabled Specifies whether a NetworkPolicy should be created
    ##
    enabled: true
    ## @param promtail.networkPolicy.allowExternal Don't require server label for connections
    ## The Policy model to apply. When set to false, only pods with the correct
    ## server label will have network access to the ports server is listening
    ## on. When true, server will accept connections from any source
    ## (with the correct destination port).
    ##
    allowExternal: true
    ## @param promtail.networkPolicy.allowExternalEgress Allow the pod to access any range of port and all destinations.
    ##
    allowExternalEgress: true
    ## @param promtail.networkPolicy.kubeAPIServerPorts [array] List of possible endpoints to kube-apiserver (limit to your cluster settings to increase security)
    ##
    kubeAPIServerPorts: [443, 6443, 8443]
    ## @param promtail.networkPolicy.extraIngress [array] Add extra ingress rules to the NetworkPolice
    ## e.g:
    ## extraIngress:
    ##   - ports:
    ##       - port: 1234
    ##     from:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    extraIngress: []
    ## @param promtail.networkPolicy.extraEgress [array] Add extra ingress rules to the NetworkPolicy
    ## e.g:
    ## extraEgress:
    ##   - ports:
    ##       - port: 1234
    ##     to:
    ##       - podSelector:
    ##           - matchLabels:
    ##               - role: frontend
    ##       - podSelector:
    ##           - matchExpressions:
    ##               - key: role
    ##                 operator: In
    ##                 values:
    ##                   - frontend
    ##
    extraEgress: []
    ## @param promtail.networkPolicy.ingressNSMatchLabels [object] Labels to match to allow traffic from other namespaces
    ## @param promtail.networkPolicy.ingressNSPodMatchLabels [object] Pod labels to match to allow traffic from other namespaces
    ##
    ingressNSMatchLabels: {}
    ingressNSPodMatchLabels: {}
  ## Create RBAC
  ## @param promtail.rbac.create Create RBAC rules
  ##
  rbac:
    create: true
  ## Service account for Loki to use
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
  ##
  serviceAccount:
    ## @param promtail.serviceAccount.create Enable creation of ServiceAccount for Promtail pods
    ##
    create: true
    ## @param promtail.serviceAccount.name The name of the ServiceAccount to use
    ## If not set and create is true, a name is generated using the common.names.fullname template
    ##
    name: ""
    ## @param promtail.serviceAccount.automountServiceAccountToken Allows auto mount of ServiceAccountToken on the promtail.serviceAccount.created
    ## Can be set to false if pods using this promtail.serviceAccount.do not need to use K8s API
    ##
    automountServiceAccountToken: false
    ## @param promtail.serviceAccount.annotations Additional custom annotations for the ServiceAccount
    ##
    annotations: {}
## @section Init Container Parameters
##

## 'volumePermissions' init container parameters
## Changes the owner and group of the persistent volume mount point to runAsUser:fsGroup values
##   based on the *podSecurityContext/*containerSecurityContext parameters
##
volumePermissions:
  ## @param volumePermissions.enabled Enable init container that changes the owner/group of the PV mount point to `runAsUser:fsGroup`
  ##
  enabled: false
  ## OS Shell + Utility image
  ## ref: https://hub.docker.com/r/bitnami/os-shell/tags/
  ## @param volumePermissions.image.registry [default: REGISTRY_NAME] OS Shell + Utility image registry
  ## @param volumePermissions.image.repository [default: REPOSITORY_NAME/os-shell] OS Shell + Utility image repository
  ## @skip volumePermissions.image.tag OS Shell + Utility image tag (immutable tags are recommended)
  ## @param volumePermissions.image.digest OS Shell + Utility image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ## @param volumePermissions.image.pullPolicy OS Shell + Utility image pull policy
  ## @param volumePermissions.image.pullSecrets OS Shell + Utility image pull secrets
  ##
  image:
    registry: docker.io
    repository: bitnami/os-shell
    tag: 12-debian-12-r16
    digest: ""
    pullPolicy: IfNotPresent
    ## Optionally specify an array of imagePullSecrets.
    ## Secrets must be manually created in the namespace.
    ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
    ## e.g:
    ## pullSecrets:
    ##   - myRegistryKeySecretName
    ##
    pullSecrets: []
  ## Init container's resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param volumePermissions.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if volumePermissions.resources is set (volumePermissions.resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param volumePermissions.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
  ## Init container Container Security Context
  ## ref: https://kubernetes.io/docs/tasks/configure-pod-container/security-context/#set-the-security-context-for-a-container
  ## @param volumePermissions.containerSecurityContext.seLinuxOptions [object,nullable] Set SELinux options in container
  ## @param volumePermissions.containerSecurityContext.runAsUser Set init container's Security Context runAsUser
  ## @param volumePermissions.containerSecurityContext.seccompProfile.type Set container's Security Context seccomp profile
  ## NOTE: when runAsUser is set to special value "auto", init container will try to chown the
  ##   data folder to auto-determined user&group, using commands: `id -u`:`id -G | cut -d" " -f2`
  ##   "auto" is especially useful for OpenShift which has scc with dynamic user ids (and 0 is not allowed)
  ##
  containerSecurityContext:
    seLinuxOptions: {}
    runAsUser: 0
    seccompProfile:
      type: "RuntimeDefault"
## @section Other Parameters
##

## Service account for Loki to use
## ref: https://kubernetes.io/docs/tasks/configure-pod-container/configure-service-account/
##
serviceAccount:
  ## @param serviceAccount.create Enable creation of ServiceAccount for Loki pods
  ##
  create: true
  ## @param serviceAccount.name The name of the ServiceAccount to use
  ## If not set and create is true, a name is generated using the common.names.fullname template
  ##
  name: ""
  ## @param serviceAccount.automountServiceAccountToken Allows auto mount of ServiceAccountToken on the serviceAccount created
  ## Can be set to false if pods using this serviceAccount do not need to use K8s API
  ##
  automountServiceAccountToken: false
  ## @param serviceAccount.annotations Additional custom annotations for the ServiceAccount
  ##
  annotations: {}
## @section Metrics Parameters
## Prometheus Exporter / Metrics
##
metrics:
  ## @param metrics.enabled Enable metrics
  ##
  enabled: false
  ## Prometheus Operator ServiceMonitor configuration
  ##
  serviceMonitor:
    ## @param metrics.serviceMonitor.enabled Create ServiceMonitor Resource for scraping metrics using Prometheus Operator
    ##
    enabled: false
    ## @param metrics.serviceMonitor.namespace Namespace for the ServiceMonitor Resource (defaults to the Release Namespace)
    ##
    namespace: ""
    ## @param metrics.serviceMonitor.interval Interval at which metrics should be scraped.
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
    ##
    interval: ""
    ## @param metrics.serviceMonitor.scrapeTimeout Timeout after which the scrape is ended
    ## ref: https://github.com/coreos/prometheus-operator/blob/master/Documentation/api.md#endpoint
    ##
    scrapeTimeout: ""
    ## @param metrics.serviceMonitor.labels Additional labels that can be used so ServiceMonitor will be discovered by Prometheus
    ##
    labels: {}
    ## @param metrics.serviceMonitor.selector Prometheus instance selector labels
    ## ref: https://github.com/bitnami/charts/tree/main/bitnami/prometheus-operator#prometheus-configuration
    ##
    selector: {}
    ## @param metrics.serviceMonitor.relabelings RelabelConfigs to apply to samples before scraping
    ##
    relabelings: []
    ## @param metrics.serviceMonitor.metricRelabelings MetricRelabelConfigs to apply to samples before ingestion
    ##
    metricRelabelings: []
    ## @param metrics.serviceMonitor.honorLabels Specify honorLabels parameter to add the scrape endpoint
    ##
    honorLabels: false
    ## @param metrics.serviceMonitor.jobLabel The name of the label on the target service to use as the job name in prometheus.
    ##
    jobLabel: ""
## @section External Memcached (Chunks) Parameters
##
externalMemcachedChunks:
  ## @param externalMemcachedChunks.host Host of a running external memcached instance
  ##
  host: ""
  ## @param externalMemcachedChunks.port Port of a running external memcached instance
  ##
  port: 11211
## @section Memcached Sub-chart Parameters (Chunks)
## Memcached sub-chart (Chunks)
##
memcachedchunks:
  ## @param memcachedchunks.enabled Deploy memcached sub-chart
  ##
  enabled: true
  ## Bitnami Memcached image version
  ## ref: https://hub.docker.com/r/bitnami/memcached/tags/
  ## @param memcachedchunks.image.registry [default: REGISTRY_NAME] Memcached image registry
  ## @param memcachedchunks.image.repository [default: REPOSITORY_NAME/memcached] Memcached image repository
  ## @skip memcachedchunks.image.tag Memcached image tag (immutable tags are recommended)
  ## @param memcachedchunks.image.digest Memcached image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ##
  image:
    registry: docker.io
    repository: bitnami/memcached
    tag: 1.6.25-debian-12-r0
    digest: ""
  ## @param memcachedchunks.nameOverride override the subchart name
  ##
  nameOverride: ""
  ## @param memcachedchunks.architecture Memcached architecture
  ##
  architecture: high-availability
  ## @param memcachedchunks.service.ports.memcached Memcached service port
  ##
  service:
    ports:
      memcached: 11211
  ## Memcached resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param memcachedchunks.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param memcachedchunks.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  tolerations:
  - key: "memcached-node"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
  resources: {}
## @section External Memcached (Frontend) Parameters
##
externalMemcachedFrontend:
  ## @param externalMemcachedFrontend.host Host of a running external memcached instance
  ##
  host: ""
  ## @param externalMemcachedFrontend.port Port of a running external memcached instance
  ##
  port: 11211
## @section Memcached Sub-chart Parameters (Frontend)
## Memcached sub-chart (Frontend)
##
memcachedfrontend:
  ## @param memcachedfrontend.enabled Deploy memcached sub-chart
  ##
  enabled: true
  ## Bitnami Memcached image version
  ## ref: https://hub.docker.com/r/bitnami/memcached/tags/
  ## @param memcachedfrontend.image.registry [default: REGISTRY_NAME] Memcached image registry
  ## @param memcachedfrontend.image.repository [default: REPOSITORY_NAME/memcached] Memcached image repository
  ## @skip memcachedfrontend.image.tag Memcached image tag (immutable tags are recommended)
  ## @param memcachedfrontend.image.digest Memcached image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ##
  image:
    registry: docker.io
    repository: bitnami/memcached
    tag: 1.6.25-debian-12-r0
    digest: ""
  ## @param memcachedfrontend.architecture Memcached architecture
  ##
  architecture: high-availability
  ## @param memcachedfrontend.nameOverride override the subchart name
  ##
  nameOverride: ""
  ## @param memcachedfrontend.service.ports.memcached Memcached service port
  ##
  service:
    ports:
      memcached: 11211
  ## Memcached resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param memcachedfrontend.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param memcachedfrontend.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
## @section External Memcached (Index-Queries) Parameters
  tolerations:
  - key: "memcached-node"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
##
externalMemcachedIndexQueries:
  ## @param externalMemcachedIndexQueries.host Host of a running external memcached instance
  ##
  host: ""
  ## @param externalMemcachedIndexQueries.port Port of a running external memcached instance
  ##
  port: 11211
## @section Memcached Sub-chart Parameters (Index-Queries)
## Memcached sub-chart (Index-Queries)
##
memcachedindexqueries:
  ## @param memcachedindexqueries.enabled Deploy memcached sub-chart
  ##
  enabled: false
  ## Bitnami Memcached image version
  ## ref: https://hub.docker.com/r/bitnami/memcached/tags/
  ## @param memcachedindexqueries.image.registry [default: REGISTRY_NAME] Memcached image registry
  ## @param memcachedindexqueries.image.repository [default: REPOSITORY_NAME/memcached] Memcached image repository
  ## @skip memcachedindexqueries.image.tag Memcached image tag (immutable tags are recommended)
  ## @param memcachedindexqueries.image.digest Memcached image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ##
  image:
    registry: docker.io
    repository: bitnami/memcached
    tag: 1.6.25-debian-12-r0
    digest: ""
  ## @param memcachedindexqueries.architecture Memcached architecture
  ##
  architecture: high-availability
  ## @param memcachedindexqueries.nameOverride override the subchart name
  ##
  nameOverride: ""
  ## @param memcachedindexqueries.service.ports.memcached Memcached service port
  ##
  service:
    ports:
      memcached: 11211
  ## Memcached resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param memcachedindexqueries.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param memcachedindexqueries.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}
## @section External Memcached (IndexWrites) Parameters
##
  tolerations:
  - key: "memcached-node"
    operator: "Equal"
    value: "true"
    effect: "NoSchedule"
externalMemcachedIndexWrites:
  ## @param externalMemcachedIndexWrites.host Host of a running external memcached instance
  ##
  host: ""
  ## @param externalMemcachedIndexWrites.port Port of a running external memcached instance
  ##
  port: 11211
## @section Memcached Sub-chart Parameters (Index-Writes)
## Memcached sub-chart (Index-Writes)
##
memcachedindexwrites:
  ## @param memcachedindexwrites.enabled Deploy memcached sub-chart
  ##
  enabled: false
  ## Bitnami Memcached image version
  ## ref: https://hub.docker.com/r/bitnami/memcached/tags/
  ## @param memcachedindexwrites.image.registry [default: REGISTRY_NAME] Memcached image registry
  ## @param memcachedindexwrites.image.repository [default: REPOSITORY_NAME/memcached] Memcached image repository
  ## @skip memcachedindexwrites.image.tag Memcached image tag (immutable tags are recommended)
  ## @param memcachedindexwrites.image.digest Memcached image digest in the way sha256:aa.... Please note this parameter, if set, will override the tag
  ##
  image:
    registry: docker.io
    repository: bitnami/memcached
    tag: 1.6.25-debian-12-r0
    digest: ""
  ## @param memcachedindexwrites.architecture Memcached architecture
  ##
  architecture: high-availability
  ## @param memcachedindexwrites.nameOverride override the subchart name
  ##
  nameOverride: ""
  ## @param memcachedindexwrites.service.ports.memcached Memcached service port
  ##
  service:
    ports:
      memcached: 11211
  ## Memcached resource requests and limits
  ## ref: https://kubernetes.io/docs/concepts/configuration/manage-compute-resources-container/
  ## @param memcachedindexwrites.resourcesPreset Set container resources according to one common preset (allowed values: none, nano, small, medium, large, xlarge, 2xlarge). This is ignored if resources is set (resources is recommended for production).
  ## More information: https://github.com/bitnami/charts/blob/main/bitnami/common/templates/_resources.tpl#L15
  ##
  resourcesPreset: "nano"
  ## @param memcachedindexwrites.resources Set container requests and limits for different resources like CPU or memory (essential for production workloads)
  ## Example:
  ## resources:
  ##   requests:
  ##     cpu: 2
  ##     memory: 512Mi
  ##   limits:
  ##     cpu: 3
  ##     memory: 1024Mi
  ##
  resources: {}

