{{- if (and .Values.serviceAccount.create .Values.serviceAccount.annotations) -}}
# Even though Scylla Operator creates and manages Service Account used by ScyllaClusters
# we have to keep it here to keep backward compatibility of setting custom annotations via helm chart.
# Operator will adopt it anyways on creation and updates.
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ printf "%s-member" (include "scylla.fullname" .) }}
  namespace: {{ .Release.Namespace }}
  labels:
  {{- include "scylla.labels" . | nindent 4 }}
  annotations:
    {{- toYaml .Values.serviceAccount.annotations | nindent 4 }}
{{- end }}
