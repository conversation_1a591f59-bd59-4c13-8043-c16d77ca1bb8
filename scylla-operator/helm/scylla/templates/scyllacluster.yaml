apiVersion: scylla.scylladb.com/v1
kind: ScyllaCluster
metadata:
  name: {{ include "scylla.fullname" . }}
  namespace: {{ .Release.Namespace }}
spec:
  {{- with .Values.podMetadata }}
  podMetadata:
    {{- . | toYaml | nindent 4 }}
  {{- end }}
  version: {{ .Values.scyllaImage.tag }}
  agentVersion: {{ .Values.agentImage.tag }}
  {{- if .Values.scyllaImage.repository }}
  repository: {{ .Values.scyllaImage.repository }}
  {{- end }}
  {{- if .Values.agentImage.repository }}
  agentRepository: {{ .Values.agentImage.repository }}
  {{- end }}
  {{- with .Values.imagePullSecrets }}
  imagePullSecrets:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if .Values.alternator.enabled }}
  alternator:
    {{- omit .Values.alternator "enabled" | toYaml | nindent 4 }}
  {{- end }}
  {{- if .Values.developerMode }}
  developerMode: {{ .Values.developerMode }}
  {{- end }}
  {{- if .Values.forceRedeploymentReason }}
  forceRedeploymentReason: {{ .Values.forceRedeploymentReason }}
  {{- end }}
  {{- if .Values.cpuset }}
  cpuset: {{ .Values.cpuset }}
  {{- end }}
  {{- if .Values.automaticOrphanedNodeCleanup }}
  automaticOrphanedNodeCleanup: {{ .Values.automaticOrphanedNodeCleanup }}
  {{- end }}
  {{- with .Values.sysctls }}
  sysctls:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if .Values.hostNetworking}}
  network:
    hostNetworking: {{ .Values.hostNetworking }}
  {{- end }}
  {{- with .Values.backups }}
  backups:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.repairs }}
  repairs:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.dnsDomains }}
  dnsDomains:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.exposeOptions }}
  exposeOptions:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- if .Values.scyllaArgs }}
  scyllaArgs: {{ .Values.scyllaArgs }}
  {{- end }}
  {{- with .Values.externalSeeds }}
  externalSeeds:
    {{- toYaml . | nindent 4 }}
  {{- end }}
  datacenter:
    name: {{ .Values.datacenter }}
    racks:
    {{- range .Values.racks }}
    - name: {{ .name }}
      {{- with .scyllaConfig }}
      scyllaConfig: {{ . }}
      {{- end }}
      {{- with .scyllaAgentConfig }}
      scyllaAgentConfig: {{ . }}
      {{- end }}
      members: {{ .members }}
      storage:
        {{- .storage | toYaml | nindent 8 }}
      resources:
        {{- toYaml .resources | nindent 8 }}
      {{- if .agentResources }}
      agentResources:
        {{- toYaml .agentResources | nindent 8 }}
      {{- end }}
      {{- if .volumes }}
      volumes:
        {{- toYaml .volumes | nindent 8 }}
      {{- end }}
      {{- if .volumeMounts }}
      volumeMounts:
        {{- toYaml .volumeMounts | nindent 8 }}
      {{- end }}
      {{- if .agentVolumeMounts }}
      agentVolumeMounts:
        {{- toYaml .agentVolumeMounts | nindent 8 }}
      {{- end }}
      {{- if .placement }}
      placement:
        {{- if .placement.podAffinity }}
        podAffinity:
          {{- toYaml .placement.podAffinity | nindent 10 }}
        {{- end }}
        {{- if .placement.podAntiAffinity }}
        podAntiAffinity:
          {{- toYaml .placement.podAntiAffinity | nindent 10 }}
        {{- end }}
        {{- if .placement.nodeAffinity }}
        nodeAffinity:
          {{- toYaml .placement.nodeAffinity | nindent 10 }}
        {{- end }}
        {{- if .placement.tolerations }}
        tolerations:
          {{- toYaml .placement.tolerations | nindent 10 }}
        {{- end }}
      {{- end }}
    {{- end }}
