{{- if .Values.serviceMonitor.create -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ printf "%s-service-monitor" (include "scylla.fullname" .) }}
  namespace: {{ .Release.Namespace }}
spec:
  jobLabel: "app"
  targetLabels: ["scylla/cluster"]
  podTargetLabels: ["scylla/datacenter"]
  selector:
    matchLabels:
      app: scylla
  endpoints:
  - port: agent-prometheus
    metricRelabelings:
    # rename job label to 'manager_agent' due to hardcoded name
    # in Scylla Monitoring.
    - sourceLabels: [ endpoint ]
      targetLabel: job
      regex: agent-prometheus
      replacement: manager_agent
  - port: prometheus
    metricRelabelings:
    - sourceLabels: [ scylla_cluster ]
      targetLabel: cluster
      regex: (.*)
      replacement: ${1}
      action: replace
    - sourceLabels: [ scylla_datacenter ]
      targetLabel: dc
      regex: (.*)
      replacement: ${1}
      action: replace
{{ end }}
