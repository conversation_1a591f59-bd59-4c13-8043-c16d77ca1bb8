# Allows to override Scylla name showing up in recommended k8s labels
nameOverride: ""
# Allows to override names used in Scylla k8s objects.
fullnameOverride: ""

# Allows to customize Scylla image
scyllaImage:
  repository: scylladb/scylla
  # Overrides the image tag whose default is the chart appVersion.
  tag: 6.1.0
# Allows to customize Scylla image
agentImage:
  repository: scylladb/scylla-manager-agent
  # Overrides the image tag whose default is the chart appVersion.
  tag: 3.3.0

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}

alternator:
  # Allows to enable Alternator (DynamoDB compatible API) frontend
  enabled: true
  # Enables insecure HTTP port to be backwards compatible. This should be set to "false" in the overrides.
  insecureEnableHTTP: true
  writeIsolation: "always"

# If set to a non-empty string, it forces a rolling restart of Scylla. Change it again to trigger a new restart.
forceRedeploymentReason: ""
# Whether developer mode should be enabled.
developerMode: true
# cpuset determines if the cluster will use cpu-pinning for max performance.
cpuset: false
# Whether to enable host networking in Scylla Pod
hostNetworking: false
# Whether Scylla Operator should perform automatic cleanup of orphaned Pods
automaticOrphanedNodeCleanup: false
# Sysctl properties to be applied during initialization given as a list of key=value pairs
sysctls: []

# Scylla Manager Backups task definition
backups: []
# Scylla Manager Repair task definition
repairs: []

# scyllaArgs will be appended to Scylla binary startup parameters.
scyllaArgs: ""

# ImagePullSecrets used for pulling Scylla and Agent images
imagePullSecrets: []

# Name of datacenter
datacenter: "us-east-1"
# List of racks
racks:
- name: "us-east-1a"
  # Name of additional scylla config configMap
  scyllaConfig: "scylla-config"
  # Name of additional scylla manager agent config stored as secret
  scyllaAgentConfig: "scylla-agent-config"
  # Number of rack members (nodes)
  members: 3
  # Storage definition
  storage:
    storageClassName: scylladb-local-xfs
    capacity: 10Gi
  # Scylla container resource definition
  resources:
     limits:
       cpu: 1
       memory: 4Gi
     requests:
       cpu: 1
       memory: 4Gi

# Whether to create Prometheus ServiceMonitor
serviceMonitor:
  create: false
