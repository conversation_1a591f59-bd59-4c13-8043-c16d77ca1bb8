kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: scyllacluster-view
  labels:
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    rbac.authorization.k8s.io/aggregate-to-edit: "true"
    rbac.authorization.k8s.io/aggregate-to-view: "true"
rules:
- apiGroups:
  - scylla.scylladb.com
  resources:
  - scyllaclusters
  - scylladbmonitorings
  verbs:
  - get
  - list
  - watch
