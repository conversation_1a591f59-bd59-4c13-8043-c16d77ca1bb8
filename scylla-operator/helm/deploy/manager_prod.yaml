# Scylla Manager image
image:
  repository: docker.io/scylladb

controllerImage:
  repository: docker.io/scylladb

logLevel: info

# Resources allocated to Scylla Manager pods
resources:
  requests:
    cpu: 10m
    memory: 20Mi
# Resources allocated to Scylla Manager Controller pods
controllerResources:
  requests:
    cpu: 10m
    memory: 20Mi

# Scylla instance for Manager
scylla:
  fullnameOverride: scylla-manager-cluster
  scyllaImage:
    repository: docker.io/scylladb/scylla
    tag: 6.1.0
  agentImage:
    tag: 3.3.0
    repository: docker.io/scylladb/scylla-manager-agent
  developerMode: true
  cpuset: true
  datacenter: manager-dc
  racks:
  - name: manager-rack
    members: 1
    storage:
      capacity: 5Gi
      storageClassName: scylladb-local-xfs
    resources:
      limits:
        cpu: 1
        memory: 200Mi
      requests:
        cpu: 1
        memory: 200Mi
