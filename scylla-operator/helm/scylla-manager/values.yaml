# Allows to override Scylla Manager name showing up in recommended k8s labels
nameOverride: ""
# Allows to override names used in Scylla Manager k8s objects.
fullnameOverride: ""

# Allows to customize Scylla Manager image
image:
  repository: scylladb
  pullPolicy: IfNotPresent
  tag: 3.3.0@sha256:e8c5b62c9330f91dfca24f109b033df78113d3ffaac306edf6d3c4346e1fa0bf

# Allows to customize Scylla Manager Controller image
controllerImage:
  repository: scylladb
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

# Scylla Manager log level, allowed values are: error, warn, info, debug, trace
logLevel: info
# Resources allocated to Scylla Manager pods
resources:
  requests:
    cpu: 10m
    memory: 20Mi
# Resources allocated to Scylla Manager pods
controllerResources:
  requests:
    cpu: 10m
    memory: 20Mi
# Node selector for Scylla Manager pod
nodeSelector: 
  app: scylla

# Tolerations for Scylla Manager pod
tolerations:
  - key: app
    value: scylla-only
    effect: NoSchedule
    operator: Equal

# Affinity for Scylla Manager pod
affinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 1
      podAffinityTerm:
        labelSelector:
          matchLabels:
            app: scylla
        topologyKey: kubernetes.io/hostname

## SecurityContext holds pod-level security attributes
securityContext: {}

# Node selector for Scylla Manager Controller pod
controllerNodeSelector: 
  app: scylla

# Tolerations for Scylla Manager Controller pod
controllerTolerations:
  - key: app
    value: scylla-only
    effect: NoSchedule
    operator: Equal

# Affinity for Scylla Manager Controller pod
controllerAffinity:
  podAntiAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
    - weight: 1
      podAffinityTerm:
        labelSelector:
          matchLabels:
            app: scylla
        topologyKey: kubernetes.io/hostname

## ControllerSecurityContext holds pod-level security attributes
controllerSecurityContext: {}

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

controllerServiceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

scylla:
  developerMode: true
  scyllaImage:
    tag: 6.1.0
  agentImage:
    tag: 3.3.0
  datacenter: manager-dc
  racks:
  - name: manager-rack
    members: 1
    storage:
      capacity: 5Gi
      storageClassName: scylladb-local-xfs
    resources:
      limits:
        cpu: 1
        memory: 200Mi
      requests:
        cpu: 1
        memory: 200Mi

# Whether to create Prometheus ServiceMonitor
serviceMonitor:
  create: false
