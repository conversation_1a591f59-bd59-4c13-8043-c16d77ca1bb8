{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"affinity": {"type": "object"}, "controllerAffinity": {"type": "object"}, "controllerImage": {"type": "object", "properties": {"pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "controllerResources": {"type": "object", "properties": {"requests": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}, "limits": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}}}, "controllerSecurityContext": {"type": "object"}, "image": {"type": "object", "properties": {"pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "logLevel": {"type": "string"}, "nodeSelector": {"type": "object"}, "controllerNodeSelector": {"type": "object"}, "resources": {"type": "object", "properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}}}, "scylla": {"description": "spec defines the desired state of this scylla cluster.", "properties": {"agentRepository": {"default": "docker.io/scylladb/scylla-manager-agent", "description": "agentRepository is the repository to pull the agent image from.", "type": "string"}, "agentVersion": {"default": "latest", "description": "agentVersion indicates the version of Scylla Manager Agent to use.", "type": "string"}, "alternator": {"description": "alternator designates this cluster an Alternator cluster.", "properties": {"insecureDisableAuthorization": {"description": "insecureDisableAuthorization disables Alternator authorization. If not specified, the authorization is enabled. For backwards compatibility the authorization is disabled when this field is not specified and a manual port is used.", "type": "boolean"}, "insecureEnableHTTP": {"description": "insecureEnableHTTP enables serving Alternator traffic also on insecure HTTP port.", "type": "boolean"}, "port": {"description": "port is the port number used to bind the Alternator API. Deprecated: `port` is deprecated and may be ignored in the future. Please make sure to avoid using hostNetworking and work with standard Kubernetes concepts like Services.", "format": "int32", "type": "integer"}, "servingCertificate": {"default": {"type": "OperatorManaged"}, "description": "servingCertificate references a TLS certificate for serving secure traffic.", "properties": {"operatorManagedOptions": {"description": "operatorManagedOptions specifies options for certificates manged by the operator.", "properties": {"additionalDNSNames": {"description": "additionalDNSNames represents external DNS names that the certificates should be signed for.", "items": {"type": "string"}, "type": "array"}, "additionalIPAddresses": {"description": "additionalIPAddresses represents external IP addresses that the certificates should be signed for.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "type": {"description": "type determines the source of this certificate.", "enum": ["OperatorManaged", "UserManaged"], "type": "string"}, "userManagedOptions": {"description": "userManagedOptions specifies options for certificates manged by users.", "properties": {"secretName": {"description": "secretName references a kubernetes.io/tls type secret containing the TLS cert and key.", "type": "string"}}, "type": "object"}}, "type": "object"}, "writeIsolation": {"description": "writeIsolation indicates the isolation level.", "type": "string"}}, "type": "object"}, "automaticOrphanedNodeCleanup": {"description": "automaticOrphanedNodeCleanup controls if automatic orphan node cleanup should be performed.", "type": "boolean"}, "backups": {"description": "backups specifies backup tasks in Scylla Manager. When Scylla Manager is not installed, these will be ignored.", "items": {"properties": {"cron": {"description": "cron specifies the task schedule as a cron expression. It supports an extended syntax including @monthly, @weekly, @daily, @midnight, @hourly, @every X[h|m|s].", "type": "string"}, "dc": {"description": "dc is a list of datacenter glob patterns, e.g. 'dc1,!otherdc*' used to specify the DCs to include or exclude from backup.", "items": {"type": "string"}, "type": "array"}, "interval": {"description": "interval represents a task schedule interval e.g. 3d2h10m, valid units are d, h, m, s. Deprecated: please use cron instead.", "type": "string"}, "keyspace": {"description": "keyspace is a list of keyspace/tables glob patterns, e.g. 'keyspace,!keyspace.table_prefix_*' used to include or exclude keyspaces from repair.", "items": {"type": "string"}, "type": "array"}, "location": {"description": "location is a list of backup locations in the format [<dc>:]<provider>:<name> ex. s3:my-bucket. The <dc>: part is optional and is only needed when different datacenters are being used to upload data to different locations. <name> must be an alphanumeric string and may contain a dash and or a dot, but other characters are forbidden. The only supported storage <provider> at the moment are s3 and gcs.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "name specifies the name of a task.", "type": "string"}, "numRetries": {"default": 3, "description": "numRetries indicates how many times a scheduled task will be retried before failing.", "format": "int64", "type": "integer"}, "rateLimit": {"description": "rateLimit is a list of megabytes (MiB) per second rate limits expressed in the format [<dc>:]<limit>. The <dc>: part is optional and only needed when different datacenters need different upload limits. Set to 0 for no limit (default 100).", "items": {"type": "string"}, "type": "array"}, "retention": {"default": 3, "description": "retention is the number of backups which are to be stored.", "format": "int64", "type": "integer"}, "snapshotParallel": {"description": "snapshotParallel is a list of snapshot parallelism limits in the format [<dc>:]<limit>. The <dc>: part is optional and allows for specifying different limits in selected datacenters. If The <dc>: part is not set, the limit is global (e.g. 'dc1:2,5') the runs are parallel in n nodes (2 in dc1) and n nodes in all the other datacenters.", "items": {"type": "string"}, "type": "array"}, "startDate": {"description": "startDate specifies the task start date expressed in the RFC3339 format or now[+duration], e.g. now+3d2h10m, valid units are d, h, m, s.", "type": "string"}, "timezone": {"description": "timezone specifies the timezone of cron field.", "type": "string"}, "uploadParallel": {"description": "uploadParallel is a list of upload parallelism limits in the format [<dc>:]<limit>. The <dc>: part is optional and allows for specifying different limits in selected datacenters. If The <dc>: part is not set the limit is global (e.g. 'dc1:2,5') the runs are parallel in n nodes (2 in dc1) and n nodes in all the other datacenters.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "type": "array"}, "cpuset": {"description": "cpuset determines if the cluster will use cpu-pinning. Deprecated: `cpuset` is deprecated and may be ignored in the future.", "type": "boolean"}, "datacenter": {"type": "string"}, "developerMode": {"description": "developerMode determines if the cluster runs in developer-mode.", "type": "boolean"}, "dnsDomains": {"description": "dnsDomains is a list of DNS domains this cluster is reachable by. These domains are used when setting up the infrastructure, like certificates. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "items": {"type": "string"}, "type": "array"}, "exposeOptions": {"description": "exposeOptions specifies options for exposing ScyllaCluster services. This field is immutable. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "properties": {"broadcastOptions": {"description": "BroadcastOptions defines how ScyllaDB node publishes its IP address to other nodes and clients.", "properties": {"clients": {"default": {"type": "ServiceClusterIP"}, "description": "clients specifies options related to the address that is broadcasted for communication with clients. This field controls the `broadcast_rpc_address` value in ScyllaDB config.", "properties": {"podIP": {"description": "podIP holds options related to Pod IP address.", "properties": {"source": {"default": "Status", "description": "sourceType specifies source of the Pod IP.", "type": "string"}}, "type": "object"}, "type": {"description": "type of the address that is broadcasted.", "type": "string"}}, "type": "object"}, "nodes": {"default": {"type": "ServiceClusterIP"}, "description": "nodes specifies options related to the address that is broadcasted for communication with other nodes. This field controls the `broadcast_address` value in ScyllaDB config.", "properties": {"podIP": {"description": "podIP holds options related to Pod IP address.", "properties": {"source": {"default": "Status", "description": "sourceType specifies source of the Pod IP.", "type": "string"}}, "type": "object"}, "type": {"description": "type of the address that is broadcasted.", "type": "string"}}, "type": "object"}}, "type": "object"}, "cql": {"description": "cql specifies expose options for CQL SSL backend. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "properties": {"ingress": {"description": "ingress is an Ingress configuration options. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "annotations is a custom key value map that gets merged with managed object annotations.", "type": "object"}, "disabled": {"description": "disabled controls if Ingress object creation is disabled. Unless disabled, there is an Ingress objects created for every Scylla node. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "type": "boolean"}, "ingressClassName": {"description": "ingressClassName specifies Ingress class name. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "labels is a custom key value map that gets merged with managed object labels.", "type": "object"}}, "type": "object"}}, "type": "object"}, "nodeService": {"default": {"type": "ClusterIP"}, "description": "nodeService controls properties of Service dedicated for each ScyllaCluster node.", "properties": {"allocateLoadBalancerNodePorts": {"description": "allocateLoadBalancerNodePorts controls value of service.spec.allocateLoadBalancerNodePorts of each node Service. Check Kubernetes corev1.Service documentation about semantic of this field.", "type": "boolean"}, "annotations": {"additionalProperties": {"type": "string"}, "description": "annotations is a custom key value map that gets merged with managed object annotations.", "type": "object"}, "externalTrafficPolicy": {"description": "externalTrafficPolicy controls value of service.spec.externalTrafficPolicy of each node Service. Check Kubernetes corev1.Service documentation about semantic of this field.", "type": "string"}, "internalTrafficPolicy": {"description": "internalTrafficPolicy controls value of service.spec.internalTrafficPolicy of each node Service. Check Kubernetes corev1.Service documentation about semantic of this field.", "type": "string"}, "labels": {"additionalProperties": {"type": "string"}, "description": "labels is a custom key value map that gets merged with managed object labels.", "type": "object"}, "loadBalancerClass": {"description": "loadBalancerClass controls value of service.spec.loadBalancerClass of each node Service. Check Kubernetes corev1.Service documentation about semantic of this field.", "type": "string"}, "type": {"description": "type is the Kubernetes Service type.", "type": "string"}}, "required": ["type"], "type": "object"}}, "type": "object"}, "externalSeeds": {"description": "externalSeeds specifies the external seeds to propagate to ScyllaDB binary on startup as \"seeds\" parameter of seed-provider.", "items": {"type": "string"}, "type": "array"}, "forceRedeploymentReason": {"description": "forceRedeploymentReason can be used to force a rolling update of all racks by providing a unique string.", "type": "string"}, "genericUpgrade": {"description": "genericUpgrade allows to configure behavior of generic upgrade logic.", "properties": {"failureStrategy": {"default": "Retry", "description": "failureStrategy specifies which logic is executed when upgrade failure happens. Currently only Retry is supported.", "type": "string"}, "pollInterval": {"default": "1s", "description": "pollInterval specifies how often upgrade logic polls on state updates. Increasing this value should lower number of requests sent to apiserver, but it may affect overall time spent during upgrade. DEPRECATED.", "type": "string"}}, "type": "object"}, "imagePullSecrets": {"description": "imagePullSecrets is an optional list of references to secrets in the same namespace used for pulling Scylla and Agent images.", "items": {"description": "LocalObjectReference contains enough information to let you locate the referenced object inside the same namespace.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "type": "array"}, "minReadySeconds": {"description": "minReadySeconds is the minimum number of seconds for which a newly created ScyllaDB node should be ready for it to be considered available. When used to control load balanced traffic, this can give the load balancer in front of a node enough time to notice that the node is ready and start forwarding traffic in time. Because it all depends on timing, the order is not guaranteed and, if possible, you should use readinessGates instead. If not provided, Operator will determine this value.", "format": "int32", "type": "integer"}, "minTerminationGracePeriodSeconds": {"description": "minTerminationGracePeriodSeconds specifies minimum duration in seconds to wait before every drained node is terminated. This gives time to potential load balancer in front of a node to notice that node is not ready anymore and stop forwarding new requests. This applies only when node is terminated gracefully. If not provided, Operator will determine this value. EXPERIMENTAL. Do not rely on any particular behaviour controlled by this field.", "format": "int32", "type": "integer"}, "network": {"description": "network holds the networking config.", "properties": {"dnsPolicy": {"description": "dnsPolicy defines how a pod's DNS will be configured.", "type": "string"}, "hostNetworking": {"description": "hostNetworking determines if scylla uses the host's network namespace. Setting this option avoids going through Kubernetes SDN and exposes scylla on node's IP.", "type": "boolean"}}, "type": "object"}, "podMetadata": {"description": "podMetadata controls shared metadata for all pods created based on this spec.", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "annotations is a custom key value map that gets merged with managed object annotations.", "type": "object"}, "labels": {"additionalProperties": {"type": "string"}, "description": "labels is a custom key value map that gets merged with managed object labels.", "type": "object"}}, "type": "object"}, "readinessGates": {"description": "readinessGates specifies custom readiness gates that will be evaluated for every ScyllaDB Pod readiness. It's projected into every ScyllaDB Pod as its readinessGate. Refer to upstream documentation to learn more about readiness gates.", "items": {"description": "PodReadinessGate contains the reference to a pod condition", "properties": {"conditionType": {"description": "ConditionType refers to a condition in the pod's condition list with matching type.", "type": "string"}}, "required": ["conditionType"], "type": "object"}, "type": "array"}, "repairs": {"description": "repairs specify repair tasks in Scylla Manager. When Scylla Manager is not installed, these will be ignored.", "items": {"properties": {"cron": {"description": "cron specifies the task schedule as a cron expression. It supports an extended syntax including @monthly, @weekly, @daily, @midnight, @hourly, @every X[h|m|s].", "type": "string"}, "dc": {"description": "dc is a list of datacenter glob patterns, e.g. 'dc1', '!otherdc*' used to specify the DCs to include or exclude from backup.", "items": {"type": "string"}, "type": "array"}, "failFast": {"description": "failFast indicates if a repair should be stopped on first error.", "type": "boolean"}, "host": {"description": "host specifies a host to repair. If empty, all hosts are repaired.", "type": "string"}, "intensity": {"default": "1", "description": "intensity indicates how many token ranges (per shard) to repair in a single Scylla repair job. By default this is 1. If you set it to 0 the number of token ranges is adjusted to the maximum supported by node (see max_repair_ranges_in_parallel in Scylla logs). Valid values are 0 and integers >= 1. Higher values will result in increased cluster load and slightly faster repairs. Changing the intensity impacts repair granularity if you need to resume it, the higher the value the more work on resume. For Scylla clusters that *do not support row-level repair*, intensity can be a decimal between (0,1). In that case it specifies percent of shards that can be repaired in parallel on a repair master node. For Scylla clusters that are row-level repair enabled, setting intensity below 1 has the same effect as setting intensity 1.", "type": "string"}, "interval": {"description": "interval represents a task schedule interval e.g. 3d2h10m, valid units are d, h, m, s. Deprecated: please use cron instead.", "type": "string"}, "keyspace": {"description": "keyspace is a list of keyspace/tables glob patterns, e.g. 'keyspace,!keyspace.table_prefix_*' used to include or exclude keyspaces from repair.", "items": {"type": "string"}, "type": "array"}, "name": {"description": "name specifies the name of a task.", "type": "string"}, "numRetries": {"default": 3, "description": "numRetries indicates how many times a scheduled task will be retried before failing.", "format": "int64", "type": "integer"}, "parallel": {"default": 0, "description": "parallel is the maximum number of Scylla repair jobs that can run at the same time (on different token ranges and replicas). Each node can take part in at most one repair at any given moment. By default the maximum possible parallelism is used. The effective parallelism depends on a keyspace replication factor (RF) and the number of nodes. The formula to calculate it is as follows: number of nodes / RF, ex. for 6 node cluster with RF=3 the maximum parallelism is 2.", "format": "int64", "type": "integer"}, "smallTableThreshold": {"default": "1GiB", "description": "smallTableThreshold enable small table optimization for tables of size lower than given threshold. Supported units [B, MiB, GiB, TiB].", "type": "string"}, "startDate": {"description": "startDate specifies the task start date expressed in the RFC3339 format or now[+duration], e.g. now+3d2h10m, valid units are d, h, m, s.", "type": "string"}, "timezone": {"description": "timezone specifies the timezone of cron field.", "type": "string"}}, "type": "object"}, "type": "array"}, "repository": {"default": "docker.io/scylladb/scylla", "description": "repository is the image repository to pull the <PERSON><PERSON><PERSON> image from.", "type": "string"}, "scyllaArgs": {"description": "scyllaArgs will be appended to Scylla binary during startup. This is supported from 4.2.0 Scylla version.", "type": "string"}, "sysctls": {"description": "sysctls holds the sysctl properties to be applied during initialization given as a list of key=value pairs. Example: fs.aio-max-nr=232323", "items": {"type": "string"}, "type": "array"}, "version": {"description": "version is a version tag of <PERSON><PERSON><PERSON> to use.", "type": "string"}, "racks": {"description": "racks specify the racks in the datacenter.", "items": {"description": "RackSpec is the desired state for a Scylla Rack.", "properties": {"agentResources": {"default": {"requests": {"cpu": "50m", "memory": "10M"}}, "description": "agentResources specify the resources for the Agent container.", "properties": {"claims": {"description": "Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container. \n This is an alpha field and requires enabling the DynamicResourceAllocation feature gate. \n This field is immutable. It can only be set for containers.", "items": {"description": "ResourceClaim references one entry in PodSpec.ResourceClaims.", "properties": {"name": {"description": "Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.", "type": "string"}}, "required": ["name"], "type": "object"}, "type": "array", "x-kubernetes-list-map-keys": ["name"], "x-kubernetes-list-type": "map"}, "limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "description": "Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "description": "Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": "object"}}, "type": "object"}, "agentVolumeMounts": {"description": "AgentVolumeMounts to be added to Agent container.", "items": {"description": "VolumeMount describes a mounting of a Volume within a container.", "properties": {"mountPath": {"description": "Path within the container at which the volume should be mounted.  Must not contain ':'.", "type": "string"}, "mountPropagation": {"description": "mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.", "type": "string"}, "name": {"description": "This must match the Name of a Volume.", "type": "string"}, "readOnly": {"description": "Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.", "type": "boolean"}, "subPath": {"description": "Path within the volume from which the container's volume should be mounted. Defaults to \"\" (volume's root).", "type": "string"}, "subPathExpr": {"description": "Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \"\" (volume's root). SubPathExpr and SubPath are mutually exclusive.", "type": "string"}}, "required": ["mount<PERSON>ath", "name"], "type": "object"}, "type": "array"}, "members": {"description": "members is the number of Scylla instances in this rack.", "format": "int32", "type": "integer"}, "name": {"description": "name is the name of the Scylla Rack. Used in the cassandra-rackdc.properties file.", "type": "string"}, "placement": {"description": "placement describes restrictions for the nodes <PERSON><PERSON><PERSON> is scheduled on.", "properties": {"nodeAffinity": {"description": "nodeAffinity describes node affinity scheduling rules for the pod.", "properties": {"preferredDuringSchedulingIgnoredDuringExecution": {"description": "The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node matches the corresponding matchExpressions; the node(s) with the highest sum are the most preferred.", "items": {"description": "An empty preferred scheduling term matches all objects with implicit weight 0 (i.e. it's a no-op). A null preferred scheduling term matches no objects (i.e. is also a no-op).", "properties": {"preference": {"description": "A node selector term, associated with the corresponding weight.", "properties": {"matchExpressions": {"description": "A list of node selector requirements by node's labels.", "items": {"description": "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "The label key that the selector applies to.", "type": "string"}, "operator": {"description": "Represents a key's relationship to a set of values. Valid operators are <PERSON>, Not<PERSON>n, Exists, DoesNotExist. Gt, and Lt.", "type": "string"}, "values": {"description": "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchFields": {"description": "A list of node selector requirements by node's fields.", "items": {"description": "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "The label key that the selector applies to.", "type": "string"}, "operator": {"description": "Represents a key's relationship to a set of values. Valid operators are <PERSON>, Not<PERSON>n, Exists, DoesNotExist. Gt, and Lt.", "type": "string"}, "values": {"description": "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "weight": {"description": "Weight associated with matching the corresponding nodeSelectorTerm, in the range 1-100.", "format": "int32", "type": "integer"}}, "required": ["preference", "weight"], "type": "object"}, "type": "array"}, "requiredDuringSchedulingIgnoredDuringExecution": {"description": "If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to an update), the system may or may not try to eventually evict the pod from its node.", "properties": {"nodeSelectorTerms": {"description": "Required. A list of node selector terms. The terms are ORed.", "items": {"description": "A null or empty node selector term matches no objects. The requirements of them are ANDed. The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.", "properties": {"matchExpressions": {"description": "A list of node selector requirements by node's labels.", "items": {"description": "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "The label key that the selector applies to.", "type": "string"}, "operator": {"description": "Represents a key's relationship to a set of values. Valid operators are <PERSON>, Not<PERSON>n, Exists, DoesNotExist. Gt, and Lt.", "type": "string"}, "values": {"description": "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchFields": {"description": "A list of node selector requirements by node's fields.", "items": {"description": "A node selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "The label key that the selector applies to.", "type": "string"}, "operator": {"description": "Represents a key's relationship to a set of values. Valid operators are <PERSON>, Not<PERSON>n, Exists, DoesNotExist. Gt, and Lt.", "type": "string"}, "values": {"description": "An array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. If the operator is Gt or Lt, the values array must have a single element, which will be interpreted as an integer. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "type": "array"}}, "required": ["nodeSelectorTerms"], "type": "object", "x-kubernetes-map-type": "atomic"}}, "type": "object"}, "podAffinity": {"description": "podAffinity describes pod affinity scheduling rules.", "properties": {"preferredDuringSchedulingIgnoredDuringExecution": {"description": "The scheduler will prefer to schedule pods to nodes that satisfy the affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.", "items": {"description": "The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)", "properties": {"podAffinityTerm": {"description": "Required. A pod affinity term, associated with the corresponding weight.", "properties": {"labelSelector": {"description": "A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "matchLabelKeys": {"description": "MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. Also, MatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "mismatchLabelKeys": {"description": "MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MismatchLabelKeys and LabelSelector. Also, MismatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "namespaceSelector": {"description": "A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \"this pod's namespace\". An empty selector ({}) matches all namespaces.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "namespaces": {"description": "namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \"this pod's namespace\".", "items": {"type": "string"}, "type": "array"}, "topologyKey": {"description": "This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.", "type": "string"}}, "required": ["<PERSON><PERSON><PERSON>"], "type": "object"}, "weight": {"description": "weight associated with matching the corresponding podAffinityTerm, in the range 1-100.", "format": "int32", "type": "integer"}}, "required": ["podAffinityTerm", "weight"], "type": "object"}, "type": "array"}, "requiredDuringSchedulingIgnoredDuringExecution": {"description": "If the affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.", "items": {"description": "Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key <topologyKey> matches that of any node on which a pod of the set of pods is running", "properties": {"labelSelector": {"description": "A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "matchLabelKeys": {"description": "MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. Also, MatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "mismatchLabelKeys": {"description": "MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MismatchLabelKeys and LabelSelector. Also, MismatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "namespaceSelector": {"description": "A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \"this pod's namespace\". An empty selector ({}) matches all namespaces.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "namespaces": {"description": "namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \"this pod's namespace\".", "items": {"type": "string"}, "type": "array"}, "topologyKey": {"description": "This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.", "type": "string"}}, "required": ["<PERSON><PERSON><PERSON>"], "type": "object"}, "type": "array"}}, "type": "object"}, "podAntiAffinity": {"description": "podAntiAffinity describes pod anti-affinity scheduling rules.", "properties": {"preferredDuringSchedulingIgnoredDuringExecution": {"description": "The scheduler will prefer to schedule pods to nodes that satisfy the anti-affinity expressions specified by this field, but it may choose a node that violates one or more of the expressions. The node that is most preferred is the one with the greatest sum of weights, i.e. for each node that meets all of the scheduling requirements (resource request, requiredDuringScheduling anti-affinity expressions, etc.), compute a sum by iterating through the elements of this field and adding \"weight\" to the sum if the node has pods which matches the corresponding podAffinityTerm; the node(s) with the highest sum are the most preferred.", "items": {"description": "The weights of all of the matched WeightedPodAffinityTerm fields are added per-node to find the most preferred node(s)", "properties": {"podAffinityTerm": {"description": "Required. A pod affinity term, associated with the corresponding weight.", "properties": {"labelSelector": {"description": "A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "matchLabelKeys": {"description": "MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. Also, MatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "mismatchLabelKeys": {"description": "MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MismatchLabelKeys and LabelSelector. Also, MismatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "namespaceSelector": {"description": "A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \"this pod's namespace\". An empty selector ({}) matches all namespaces.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "namespaces": {"description": "namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \"this pod's namespace\".", "items": {"type": "string"}, "type": "array"}, "topologyKey": {"description": "This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.", "type": "string"}}, "required": ["<PERSON><PERSON><PERSON>"], "type": "object"}, "weight": {"description": "weight associated with matching the corresponding podAffinityTerm, in the range 1-100.", "format": "int32", "type": "integer"}}, "required": ["podAffinityTerm", "weight"], "type": "object"}, "type": "array"}, "requiredDuringSchedulingIgnoredDuringExecution": {"description": "If the anti-affinity requirements specified by this field are not met at scheduling time, the pod will not be scheduled onto the node. If the anti-affinity requirements specified by this field cease to be met at some point during pod execution (e.g. due to a pod label update), the system may or may not try to eventually evict the pod from its node. When there are multiple elements, the lists of nodes corresponding to each podAffinityTerm are intersected, i.e. all terms must be satisfied.", "items": {"description": "Defines a set of pods (namely those matching the labelSelector relative to the given namespace(s)) that this pod should be co-located (affinity) or not co-located (anti-affinity) with, where co-located is defined as running on a node whose value of the label with key <topologyKey> matches that of any node on which a pod of the set of pods is running", "properties": {"labelSelector": {"description": "A label query over a set of resources, in this case pods. If it's null, this PodAffinityTerm matches with no Pods.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "matchLabelKeys": {"description": "MatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key in (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MatchLabelKeys and LabelSelector. Also, MatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "mismatchLabelKeys": {"description": "MismatchLabelKeys is a set of pod label keys to select which pods will be taken into consideration. The keys are used to lookup values from the incoming pod labels, those key-value labels are merged with `LabelSelector` as `key notin (value)` to select the group of existing pods which pods will be taken into consideration for the incoming pod's pod (anti) affinity. Keys that don't exist in the incoming pod labels will be ignored. The default value is empty. The same key is forbidden to exist in both MismatchLabelKeys and LabelSelector. Also, MismatchLabelKeys cannot be set when LabelSelector isn't set. This is an alpha field and requires enabling MatchLabelKeysInPodAffinity feature gate.", "items": {"type": "string"}, "type": "array", "x-kubernetes-list-type": "atomic"}, "namespaceSelector": {"description": "A label query over the set of namespaces that the term applies to. The term is applied to the union of the namespaces selected by this field and the ones listed in the namespaces field. null selector and null or empty namespaces list means \"this pod's namespace\". An empty selector ({}) matches all namespaces.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "namespaces": {"description": "namespaces specifies a static list of namespace names that the term applies to. The term is applied to the union of the namespaces listed in this field and the ones selected by namespaceSelector. null or empty namespaces list and null namespaceSelector means \"this pod's namespace\".", "items": {"type": "string"}, "type": "array"}, "topologyKey": {"description": "This pod should be co-located (affinity) or not co-located (anti-affinity) with the pods matching the labelSelector in the specified namespaces, where co-located is defined as running on a node whose value of the label with key topologyKey matches that of any node on which any of the selected pods is running. Empty topologyKey is not allowed.", "type": "string"}}, "required": ["<PERSON><PERSON><PERSON>"], "type": "object"}, "type": "array"}}, "type": "object"}, "tolerations": {"description": "tolerations allow the pod to tolerate any taint that matches the triple <key,value,effect> using the matching operator.", "items": {"description": "The pod this Toleration is attached to tolerates any taint that matches the triple <key,value,effect> using the matching operator <operator>.", "properties": {"effect": {"description": "Effect indicates the taint effect to match. Empty means match all taint effects. When specified, allowed values are NoSchedule, PreferNoSchedule and NoExecute.", "type": "string"}, "key": {"description": "Key is the taint key that the toleration applies to. Empty means match all taint keys. If the key is empty, operator must be Exists; this combination means to match all values and all keys.", "type": "string"}, "operator": {"description": "Operator represents a key's relationship to the value. Valid operators are Exists and Equal. Defaults to Equal. Exists is equivalent to wildcard for value, so that a pod can tolerate all taints of a particular category.", "type": "string"}, "tolerationSeconds": {"description": "TolerationSeconds represents the period of time the toleration (which must be of effect NoExecute, otherwise this field is ignored) tolerates the taint. By default, it is not set, which means tolerate the taint forever (do not evict). Zero and negative values will be treated as 0 (evict immediately) by the system.", "format": "int64", "type": "integer"}, "value": {"description": "Value is the taint value the toleration matches to. If the operator is Exists, the value should be empty, otherwise just a regular string.", "type": "string"}}, "type": "object"}, "type": "array"}}, "type": "object"}, "resources": {"description": "resources the Scylla container will use.", "properties": {"claims": {"description": "Claims lists the names of resources, defined in spec.resourceClaims, that are used by this container. \n This is an alpha field and requires enabling the DynamicResourceAllocation feature gate. \n This field is immutable. It can only be set for containers.", "items": {"description": "ResourceClaim references one entry in PodSpec.ResourceClaims.", "properties": {"name": {"description": "Name must match the name of one entry in pod.spec.resourceClaims of the Pod where this field is used. It makes that resource available inside a container.", "type": "string"}}, "required": ["name"], "type": "object"}, "type": "array", "x-kubernetes-list-map-keys": ["name"], "x-kubernetes-list-type": "map"}, "limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "description": "Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "description": "Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": "object"}}, "type": "object"}, "scyllaAgentConfig": {"description": "Sc<PERSON>la config map name to customize scylla manager agent", "type": "string"}, "scyllaConfig": {"description": "Scylla config map name to customize scylla.yaml", "type": "string"}, "storage": {"description": "storage describes the underlying storage that <PERSON><PERSON><PERSON> will consume.", "properties": {"capacity": {"description": "capacity describes the requested size of each persistent volume.", "type": "string"}, "metadata": {"description": "metadata controls shared metadata for the volume claim for this rack. At this point, the values are applied only for the initial claim and are not reconciled during its lifetime. Note that this may get fixed in the future and this behaviour shouldn't be relied on in any way.", "properties": {"annotations": {"additionalProperties": {"type": "string"}, "description": "annotations is a custom key value map that gets merged with managed object annotations.", "type": "object"}, "labels": {"additionalProperties": {"type": "string"}, "description": "labels is a custom key value map that gets merged with managed object labels.", "type": "object"}}, "type": "object"}, "storageClassName": {"description": "storageClassName is the name of a storageClass to request.", "type": "string"}}, "type": "object"}, "volumeMounts": {"description": "VolumeMounts to be added to Scylla container.", "items": {"description": "VolumeMount describes a mounting of a Volume within a container.", "properties": {"mountPath": {"description": "Path within the container at which the volume should be mounted.  Must not contain ':'.", "type": "string"}, "mountPropagation": {"description": "mountPropagation determines how mounts are propagated from the host to container and the other way around. When not set, MountPropagationNone is used. This field is beta in 1.10.", "type": "string"}, "name": {"description": "This must match the Name of a Volume.", "type": "string"}, "readOnly": {"description": "Mounted read-only if true, read-write otherwise (false or unspecified). Defaults to false.", "type": "boolean"}, "subPath": {"description": "Path within the volume from which the container's volume should be mounted. Defaults to \"\" (volume's root).", "type": "string"}, "subPathExpr": {"description": "Expanded path within the volume from which the container's volume should be mounted. Behaves similarly to SubPath but environment variable references $(VAR_NAME) are expanded using the container's environment. Defaults to \"\" (volume's root). SubPathExpr and SubPath are mutually exclusive.", "type": "string"}}, "required": ["mount<PERSON>ath", "name"], "type": "object"}, "type": "array"}, "volumes": {"description": "Volumes added to Scylla Pod.", "items": {"description": "Volume represents a named volume in a pod that may be accessed by any container in the pod.", "properties": {"awsElasticBlockStore": {"description": "awsElasticBlockStore represents an AWS Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore", "properties": {"fsType": {"description": "fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore TODO: how do we prevent errors in the filesystem from compromising the machine", "type": "string"}, "partition": {"description": "partition is the partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \"1\". Similarly, the volume partition for /dev/sda is \"0\" (or you can leave the property empty).", "format": "int32", "type": "integer"}, "readOnly": {"description": "readOnly value true will force the readOnly setting in VolumeMounts. More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore", "type": "boolean"}, "volumeID": {"description": "volumeID is unique ID of the persistent disk resource in AWS (Amazon EBS volume). More info: https://kubernetes.io/docs/concepts/storage/volumes#awselasticblockstore", "type": "string"}}, "required": ["volumeID"], "type": "object"}, "azureDisk": {"description": "azureDisk represents an Azure Data Disk mount on the host and bind mount to the pod.", "properties": {"cachingMode": {"description": "cachingMode is the Host Caching mode: None, Read Only, Read Write.", "type": "string"}, "diskName": {"description": "diskName is the Name of the data disk in the blob storage", "type": "string"}, "diskURI": {"description": "diskURI is the URI of data disk in the blob storage", "type": "string"}, "fsType": {"description": "fsType is Filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.", "type": "string"}, "kind": {"description": "kind expected values are Shared: multiple blob disks per storage account  Dedicated: single blob disk per storage account  Managed: azure managed data disk (only in managed availability set). defaults to shared", "type": "string"}, "readOnly": {"description": "readOnly Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}}, "required": ["diskName", "diskURI"], "type": "object"}, "azureFile": {"description": "azureFile represents an Azure File Service mount on the host and bind mount to the pod.", "properties": {"readOnly": {"description": "readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}, "secretName": {"description": "secretName is the  name of secret that contains Azure Storage Account Name and Key", "type": "string"}, "shareName": {"description": "shareName is the azure share Name", "type": "string"}}, "required": ["secretName", "shareName"], "type": "object"}, "cephfs": {"description": "cephFS represents a Ceph FS mount on the host that shares a pod's lifetime", "properties": {"monitors": {"description": "monitors is Required: Monitors is a collection of Ceph monitors More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it", "items": {"type": "string"}, "type": "array"}, "path": {"description": "path is Optional: Used as the mounted root, rather than the full Ceph tree, default is /", "type": "string"}, "readOnly": {"description": "readOnly is Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it", "type": "boolean"}, "secretFile": {"description": "secretFile is Optional: SecretFile is the path to key ring for User, default is /etc/ceph/user.secret More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it", "type": "string"}, "secretRef": {"description": "secretRef is Optional: SecretRef is reference to the authentication secret for User, default is empty. More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "user": {"description": "user is optional: User is the rados user name, default is admin More info: https://examples.k8s.io/volumes/cephfs/README.md#how-to-use-it", "type": "string"}}, "required": ["monitors"], "type": "object"}, "cinder": {"description": "cinder represents a cinder volume attached and mounted on kubelets host machine. More info: https://examples.k8s.io/mysql-cinder-pd/README.md", "properties": {"fsType": {"description": "fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://examples.k8s.io/mysql-cinder-pd/README.md", "type": "string"}, "readOnly": {"description": "readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts. More info: https://examples.k8s.io/mysql-cinder-pd/README.md", "type": "boolean"}, "secretRef": {"description": "secretRef is optional: points to a secret object containing parameters used to connect to OpenStack.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "volumeID": {"description": "volumeID used to identify the volume in cinder. More info: https://examples.k8s.io/mysql-cinder-pd/README.md", "type": "string"}}, "required": ["volumeID"], "type": "object"}, "configMap": {"description": "configMap represents a configMap that should populate this volume", "properties": {"defaultMode": {"description": "defaultMode is optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "items": {"description": "items if unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.", "items": {"description": "Maps a string key to a path within a volume.", "properties": {"key": {"description": "key is the key to project.", "type": "string"}, "mode": {"description": "mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.", "type": "string"}}, "required": ["key", "path"], "type": "object"}, "type": "array"}, "name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}, "optional": {"description": "optional specify whether the ConfigMap or its keys must be defined", "type": "boolean"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "csi": {"description": "csi (Container Storage Interface) represents ephemeral storage that is handled by certain external CSI drivers (Beta feature).", "properties": {"driver": {"description": "driver is the name of the CSI driver that handles this volume. Consult with your admin for the correct name as registered in the cluster.", "type": "string"}, "fsType": {"description": "fsType to mount. Ex. \"ext4\", \"xfs\", \"ntfs\". If not provided, the empty value is passed to the associated CSI driver which will determine the default filesystem to apply.", "type": "string"}, "nodePublishSecretRef": {"description": "nodePublishSecretRef is a reference to the secret object containing sensitive information to pass to the CSI driver to complete the CSI NodePublishVolume and NodeUnpublishVolume calls. This field is optional, and  may be empty if no secret is required. If the secret object contains more than one secret, all secret references are passed.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "readOnly": {"description": "readOnly specifies a read-only configuration for the volume. Defaults to false (read/write).", "type": "boolean"}, "volumeAttributes": {"additionalProperties": {"type": "string"}, "description": "volumeAttributes stores driver-specific properties that are passed to the CSI driver. Consult your driver's documentation for supported values.", "type": "object"}}, "required": ["driver"], "type": "object"}, "downwardAPI": {"description": "downwardAPI represents downward API about the pod that should populate this volume", "properties": {"defaultMode": {"description": "Optional: mode bits to use on created files by default. Must be a Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "items": {"description": "Items is a list of downward API volume file", "items": {"description": "DownwardAPIVolumeFile represents information to create the file containing the pod field", "properties": {"fieldRef": {"description": "Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.", "properties": {"apiVersion": {"description": "Version of the schema the FieldPath is written in terms of, defaults to \"v1\".", "type": "string"}, "fieldPath": {"description": "Path of the field to select in the specified API version.", "type": "string"}}, "required": ["fieldPath"], "type": "object", "x-kubernetes-map-type": "atomic"}, "mode": {"description": "Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'", "type": "string"}, "resourceFieldRef": {"description": "Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.", "properties": {"containerName": {"description": "Container name: required for volumes, optional for env vars", "type": "string"}, "divisor": {"anyOf": [{"type": "integer"}, {"type": "string"}], "description": "Specifies the output format of the exposed resources, defaults to \"1\"", "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "resource": {"description": "Required: resource to select", "type": "string"}}, "required": ["resource"], "type": "object", "x-kubernetes-map-type": "atomic"}}, "required": ["path"], "type": "object"}, "type": "array"}}, "type": "object"}, "emptyDir": {"description": "emptyDir represents a temporary directory that shares a pod's lifetime. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir", "properties": {"medium": {"description": "medium represents what type of storage medium should back this directory. The default is \"\" which means to use the node's default medium. Must be an empty string (default) or Memory. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir", "type": "string"}, "sizeLimit": {"anyOf": [{"type": "integer"}, {"type": "string"}], "description": "sizeLimit is the total amount of local storage required for this EmptyDir volume. The size limit is also applicable for memory medium. The maximum usage on memory medium EmptyDir would be the minimum value between the SizeLimit specified here and the sum of memory limits of all containers in a pod. The default is nil which means that the limit is undefined. More info: https://kubernetes.io/docs/concepts/storage/volumes#emptydir", "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}}, "type": "object"}, "ephemeral": {"description": "ephemeral represents a volume that is handled by a cluster storage driver. The volume's lifecycle is tied to the pod that defines it - it will be created before the pod starts, and deleted when the pod is removed. \n Use this if: a) the volume is only needed while the pod runs, b) features of normal volumes like restoring from snapshot or capacity tracking are needed, c) the storage driver is specified through a storage class, and d) the storage driver supports dynamic volume provisioning through a PersistentVolumeClaim (see EphemeralVolumeSource for more information on the connection between this volume type and PersistentVolumeClaim). \n Use PersistentVolumeClaim or one of the vendor-specific APIs for volumes that persist for longer than the lifecycle of an individual pod. \n Use CSI for light-weight local ephemeral volumes if the CSI driver is meant to be used that way - see the documentation of the driver for more information. \n A pod can use both types of ephemeral volumes and persistent volumes at the same time.", "properties": {"volumeClaimTemplate": {"description": "Will be used to create a stand-alone PVC to provision the volume. The pod in which this EphemeralVolumeSource is embedded will be the owner of the PVC, i.e. the PVC will be deleted together with the pod.  The name of the PVC will be `<pod name>-<volume name>` where `<volume name>` is the name from the `PodSpec.Volumes` array entry. Pod validation will reject the pod if the concatenated name is not valid for a PVC (for example, too long). \n An existing PVC with that name that is not owned by the pod will *not* be used for the pod to avoid using an unrelated volume by mistake. Starting the pod is then blocked until the unrelated PVC is removed. If such a pre-created PVC is meant to be used by the pod, the PVC has to updated with an owner reference to the pod once the pod exists. Normally this should not be necessary, but it may be useful when manually reconstructing a broken cluster. \n This field is read-only and no changes will be made by Kubernetes to the PVC after it has been created. \n Required, must not be nil.", "properties": {"metadata": {"description": "May contain labels and annotations that will be copied into the PVC when creating it. No other fields are allowed and will be rejected during validation.", "type": "object"}, "spec": {"description": "The specification for the PersistentVolumeClaim. The entire content is copied unchanged into the PVC that gets created from this template. The same fields as in a PersistentVolumeClaim are also valid here.", "properties": {"accessModes": {"description": "accessModes contains the desired access modes the volume should have. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#access-modes-1", "items": {"type": "string"}, "type": "array"}, "dataSource": {"description": "dataSource field can be used to specify either: * An existing VolumeSnapshot object (snapshot.storage.k8s.io/VolumeSnapshot) * An existing PVC (PersistentVolumeClaim) If the provisioner or an external controller can support the specified data source, it will create a new volume based on the contents of the specified data source. When the AnyVolumeDataSource feature gate is enabled, dataSource contents will be copied to dataSourceRef, and dataSourceRef contents will be copied to dataSource when dataSourceRef.namespace is not specified. If the namespace is specified, then dataSourceRef will not be copied to dataSource.", "properties": {"apiGroup": {"description": "APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.", "type": "string"}, "kind": {"description": "Kind is the type of resource being referenced", "type": "string"}, "name": {"description": "Name is the name of resource being referenced", "type": "string"}}, "required": ["kind", "name"], "type": "object", "x-kubernetes-map-type": "atomic"}, "dataSourceRef": {"description": "dataSourceRef specifies the object from which to populate the volume with data, if a non-empty volume is desired. This may be any object from a non-empty API group (non core object) or a PersistentVolumeClaim object. When this field is specified, volume binding will only succeed if the type of the specified object matches some installed volume populator or dynamic provisioner. This field will replace the functionality of the dataSource field and as such if both fields are non-empty, they must have the same value. For backwards compatibility, when namespace isn't specified in dataSourceRef, both fields (dataSource and dataSourceRef) will be set to the same value automatically if one of them is empty and the other is non-empty. When namespace is specified in dataSourceRef, dataSource isn't set to the same value and must be empty. There are three important differences between dataSource and dataSourceRef: * While dataSource only allows two specific types of objects, dataSourceRef allows any non-core object, as well as PersistentVolumeClaim objects. * While dataSource ignores disallowed values (dropping them), dataSourceRef preserves all values, and generates an error if a disallowed value is specified. * While dataSource only allows local objects, dataSourceRef allows objects in any namespaces. (Beta) Using this field requires the AnyVolumeDataSource feature gate to be enabled. (Alpha) Using the namespace field of dataSourceRef requires the CrossNamespaceVolumeDataSource feature gate to be enabled.", "properties": {"apiGroup": {"description": "APIGroup is the group for the resource being referenced. If APIGroup is not specified, the specified Kind must be in the core API group. For any other third-party types, APIGroup is required.", "type": "string"}, "kind": {"description": "Kind is the type of resource being referenced", "type": "string"}, "name": {"description": "Name is the name of resource being referenced", "type": "string"}, "namespace": {"description": "Namespace is the namespace of resource being referenced Note that when a namespace is specified, a gateway.networking.k8s.io/ReferenceGrant object is required in the referent namespace to allow that namespace's owner to accept the reference. See the ReferenceGrant documentation for details. (Alpha) This field requires the CrossNamespaceVolumeDataSource feature gate to be enabled.", "type": "string"}}, "required": ["kind", "name"], "type": "object"}, "resources": {"description": "resources represents the minimum resources the volume should have. If RecoverVolumeExpansionFailure feature is enabled users are allowed to specify resource requirements that are lower than previous value but must still be higher than capacity recorded in the status field of the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#resources", "properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "description": "Limits describes the maximum amount of compute resources allowed. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "integer"}, {"type": "string"}], "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "description": "Requests describes the minimum amount of compute resources required. If Requests is omitted for a container, it defaults to Limits if that is explicitly specified, otherwise to an implementation-defined value. Requests cannot exceed Limits. More info: https://kubernetes.io/docs/concepts/configuration/manage-resources-containers/", "type": "object"}}, "type": "object"}, "selector": {"description": "selector is a label query over volumes to consider for binding.", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "storageClassName": {"description": "storageClassName is the name of the StorageClass required by the claim. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#class-1", "type": "string"}, "volumeAttributesClassName": {"description": "volumeAttributesClassName may be used to set the VolumeAttributesClass used by this claim. If specified, the CSI driver will create or update the volume with the attributes defined in the corresponding VolumeAttributesClass. This has a different purpose than storageClassName, it can be changed after the claim is created. An empty string value means that no VolumeAttributesClass will be applied to the claim but it's not allowed to reset this field to empty string once it is set. If unspecified and the PersistentVolumeClaim is unbound, the default VolumeAttributesClass will be set by the persistentvolume controller if it exists. If the resource referred to by volumeAttributesClass does not exist, this PersistentVolumeClaim will be set to a Pending state, as reflected by the modifyVolumeStatus field, until such as a resource exists. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#volumeattributesclass (Alpha) Using this field requires the VolumeAttributesClass feature gate to be enabled.", "type": "string"}, "volumeMode": {"description": "volumeMode defines what type of volume is required by the claim. Value of Filesystem is implied when not included in claim spec.", "type": "string"}, "volumeName": {"description": "volumeName is the binding reference to the PersistentVolume backing this claim.", "type": "string"}}, "type": "object"}}, "required": ["spec"], "type": "object"}}, "type": "object"}, "fc": {"description": "fc represents a Fibre Channel resource that is attached to a kubelet's host machine and then exposed to the pod.", "properties": {"fsType": {"description": "fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. TODO: how do we prevent errors in the filesystem from compromising the machine", "type": "string"}, "lun": {"description": "lun is Optional: FC target lun number", "format": "int32", "type": "integer"}, "readOnly": {"description": "readOnly is Optional: Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}, "targetWWNs": {"description": "targetWWNs is Optional: FC target worldwide names (WWNs)", "items": {"type": "string"}, "type": "array"}, "wwids": {"description": "wwids Optional: FC volume world wide identifiers (wwids) Either wwids or combination of targetWWNs and lun must be set, but not both simultaneously.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "flexVolume": {"description": "flexVolume represents a generic volume resource that is provisioned/attached using an exec based plugin.", "properties": {"driver": {"description": "driver is the name of the driver to use for this volume.", "type": "string"}, "fsType": {"description": "fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". The default filesystem depends on FlexVolume script.", "type": "string"}, "options": {"additionalProperties": {"type": "string"}, "description": "options is Optional: this field holds extra command options if any.", "type": "object"}, "readOnly": {"description": "readOnly is Optional: defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}, "secretRef": {"description": "secretRef is Optional: secretRef is reference to the secret object containing sensitive information to pass to the plugin scripts. This may be empty if no secret object is specified. If the secret object contains more than one secret, all secrets are passed to the plugin scripts.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}}, "required": ["driver"], "type": "object"}, "flocker": {"description": "flocker represents a Flocker volume attached to a kubelet's host machine. This depends on the Flocker control service being running", "properties": {"datasetName": {"description": "datasetName is Name of the dataset stored as metadata -> name on the dataset for <PERSON><PERSON><PERSON> should be considered as deprecated", "type": "string"}, "datasetUUID": {"description": "datasetUUID is the UUID of the dataset. This is unique identifier of a <PERSON><PERSON><PERSON> dataset", "type": "string"}}, "type": "object"}, "gcePersistentDisk": {"description": "gcePersistentDisk represents a GCE Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk", "properties": {"fsType": {"description": "fsType is filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk TODO: how do we prevent errors in the filesystem from compromising the machine", "type": "string"}, "partition": {"description": "partition is the partition in the volume that you want to mount. If omitted, the default is to mount by volume name. Examples: For volume /dev/sda1, you specify the partition as \"1\". Similarly, the volume partition for /dev/sda is \"0\" (or you can leave the property empty). More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk", "format": "int32", "type": "integer"}, "pdName": {"description": "pdName is unique name of the PD resource in GCE. Used to identify the disk in GCE. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk", "type": "string"}, "readOnly": {"description": "readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#gcepersistentdisk", "type": "boolean"}}, "required": ["pdName"], "type": "object"}, "gitRepo": {"description": "gitRepo represents a git repository at a particular revision. DEPRECATED: GitRepo is deprecated. To provision a container with a git repo, mount an EmptyDir into an InitContainer that clones the repo using git, then mount the EmptyDir into the Pod's container.", "properties": {"directory": {"description": "directory is the target directory name. Must not contain or start with '..'.  If '.' is supplied, the volume directory will be the git repository.  Otherwise, if specified, the volume will contain the git repository in the subdirectory with the given name.", "type": "string"}, "repository": {"description": "repository is the URL", "type": "string"}, "revision": {"description": "revision is the commit hash for the specified revision.", "type": "string"}}, "required": ["repository"], "type": "object"}, "glusterfs": {"description": "glusterfs represents a Glusterfs mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/glusterfs/README.md", "properties": {"endpoints": {"description": "endpoints is the endpoint name that details Glusterfs topology. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod", "type": "string"}, "path": {"description": "path is the Glusterfs volume path. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod", "type": "string"}, "readOnly": {"description": "readOnly here will force the Glusterfs volume to be mounted with read-only permissions. Defaults to false. More info: https://examples.k8s.io/volumes/glusterfs/README.md#create-a-pod", "type": "boolean"}}, "required": ["endpoints", "path"], "type": "object"}, "hostPath": {"description": "hostPath represents a pre-existing file or directory on the host machine that is directly exposed to the container. This is generally used for system agents or other privileged things that are allowed to see the host machine. Most containers will NOT need this. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath --- TODO(jonesdl) We need to restrict who can use host directory mounts and who can/can not mount host directories as read/write.", "properties": {"path": {"description": "path of the directory on the host. If the path is a symlink, it will follow the link to the real path. More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath", "type": "string"}, "type": {"description": "type for HostPath Volume Defaults to \"\" More info: https://kubernetes.io/docs/concepts/storage/volumes#hostpath", "type": "string"}}, "required": ["path"], "type": "object"}, "iscsi": {"description": "iscsi represents an ISCSI Disk resource that is attached to a kubelet's host machine and then exposed to the pod. More info: https://examples.k8s.io/volumes/iscsi/README.md", "properties": {"chapAuthDiscovery": {"description": "chapAuthDiscovery defines whether support iSCSI Discovery CHAP authentication", "type": "boolean"}, "chapAuthSession": {"description": "chapAuthSession defines whether support iSCSI Session CHAP authentication", "type": "boolean"}, "fsType": {"description": "fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#iscsi TODO: how do we prevent errors in the filesystem from compromising the machine", "type": "string"}, "initiatorName": {"description": "initiator<PERSON>ame is the custom iSCSI Initiator Name. If initiator<PERSON><PERSON> is specified with iscsiInterface simultaneously, new iSCSI interface <target portal>:<volume name> will be created for the connection.", "type": "string"}, "iqn": {"description": "iqn is the target iSCSI Qualified Name.", "type": "string"}, "iscsiInterface": {"description": "iscsiInterface is the interface Name that uses an iSCSI transport. Defaults to 'default' (tcp).", "type": "string"}, "lun": {"description": "lun represents iSCSI Target Lun number.", "format": "int32", "type": "integer"}, "portals": {"description": "portals is the iSCSI Target Portal List. The portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).", "items": {"type": "string"}, "type": "array"}, "readOnly": {"description": "readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false.", "type": "boolean"}, "secretRef": {"description": "secretRef is the CHAP Secret for iSCSI target and initiator authentication", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "targetPortal": {"description": "targetPortal is iSCSI Target Portal. The Portal is either an IP or ip_addr:port if the port is other than default (typically TCP ports 860 and 3260).", "type": "string"}}, "required": ["iqn", "lun", "targetPortal"], "type": "object"}, "name": {"description": "name of the volume. Must be a DNS_LABEL and unique within the pod. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names", "type": "string"}, "nfs": {"description": "nfs represents an NFS mount on the host that shares a pod's lifetime More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs", "properties": {"path": {"description": "path that is exported by the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs", "type": "string"}, "readOnly": {"description": "readOnly here will force the NFS export to be mounted with read-only permissions. Defaults to false. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs", "type": "boolean"}, "server": {"description": "server is the hostname or IP address of the NFS server. More info: https://kubernetes.io/docs/concepts/storage/volumes#nfs", "type": "string"}}, "required": ["path", "server"], "type": "object"}, "persistentVolumeClaim": {"description": "persistentVolumeClaimVolumeSource represents a reference to a PersistentVolumeClaim in the same namespace. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims", "properties": {"claimName": {"description": "claimName is the name of a PersistentVolumeClaim in the same namespace as the pod using this volume. More info: https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims", "type": "string"}, "readOnly": {"description": "readOnly Will force the ReadOnly setting in VolumeMounts. Default false.", "type": "boolean"}}, "required": ["claim<PERSON>ame"], "type": "object"}, "photonPersistentDisk": {"description": "photonPersistentDisk represents a PhotonController persistent disk attached and mounted on kubelets host machine", "properties": {"fsType": {"description": "fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.", "type": "string"}, "pdID": {"description": "pdID is the ID that identifies Photon Controller persistent disk", "type": "string"}}, "required": ["pdID"], "type": "object"}, "portworxVolume": {"description": "portworxVolume represents a portworx volume attached and mounted on kubelets host machine", "properties": {"fsType": {"description": "fSType represents the filesystem type to mount Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\". Implicitly inferred to be \"ext4\" if unspecified.", "type": "string"}, "readOnly": {"description": "readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}, "volumeID": {"description": "volumeID uniquely identifies a Portworx volume", "type": "string"}}, "required": ["volumeID"], "type": "object"}, "projected": {"description": "projected items for all in one resources secrets, configmaps, and downward API", "properties": {"defaultMode": {"description": "defaultMode are the mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "sources": {"description": "sources is the list of volume projections", "items": {"description": "Projection that may be projected along with other supported volume types", "properties": {"clusterTrustBundle": {"description": "ClusterTrustBundle allows a pod to access the `.spec.trustBundle` field of ClusterTrustBundle objects in an auto-updating file. \n Alpha, gated by the ClusterTrustBundleProjection feature gate. \n ClusterTrustBundle objects can either be selected by name, or by the combination of signer name and a label selector. \n Kubelet performs aggressive normalization of the PEM contents written into the pod filesystem.  Esoteric PEM features such as inter-block comments and block headers are stripped.  Certificates are deduplicated. The ordering of certificates within the file is arbitrary, and <PERSON><PERSON><PERSON> may change the order over time.", "properties": {"labelSelector": {"description": "Select all ClusterTrustBundles that match this label selector.  Only has effect if signer<PERSON><PERSON> is set.  Mutually-exclusive with name.  If unset, interpreted as \"match nothing\".  If set but empty, interpreted as \"match everything\".", "properties": {"matchExpressions": {"description": "matchExpressions is a list of label selector requirements. The requirements are ANDed.", "items": {"description": "A label selector requirement is a selector that contains values, a key, and an operator that relates the key and values.", "properties": {"key": {"description": "key is the label key that the selector applies to.", "type": "string"}, "operator": {"description": "operator represents a key's relationship to a set of values. Valid operators are In, NotIn, Exists and DoesNotExist.", "type": "string"}, "values": {"description": "values is an array of string values. If the operator is In or NotIn, the values array must be non-empty. If the operator is Exists or DoesNotExist, the values array must be empty. This array is replaced during a strategic merge patch.", "items": {"type": "string"}, "type": "array"}}, "required": ["key", "operator"], "type": "object"}, "type": "array"}, "matchLabels": {"additionalProperties": {"type": "string"}, "description": "matchLabels is a map of {key,value} pairs. A single {key,value} in the match<PERSON>abe<PERSON> map is equivalent to an element of matchExpressions, whose key field is \"key\", the operator is \"In\", and the values array contains only \"value\". The requirements are ANDed.", "type": "object"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "name": {"description": "Select a single ClusterTrustBundle by object name.  Mutually-exclusive with signerName and labelSelector.", "type": "string"}, "optional": {"description": "If true, don't block pod startup if the referenced ClusterTrustBundle(s) aren't available.  If using name, then the named ClusterTrustBundle is allowed not to exist.  If using signerName, then the combination of signerName and labelSelector is allowed to match zero ClusterTrustBundles.", "type": "boolean"}, "path": {"description": "Relative path from the volume root to write the bundle.", "type": "string"}, "signerName": {"description": "Select all ClusterTrustBundles that match this signer name. Mutually-exclusive with name.  The contents of all selected ClusterTrustBundles will be unified and deduplicated.", "type": "string"}}, "required": ["path"], "type": "object"}, "configMap": {"description": "configMap information about the configMap data to project", "properties": {"items": {"description": "items if unspecified, each key-value pair in the Data field of the referenced ConfigMap will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the ConfigMap, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.", "items": {"description": "Maps a string key to a path within a volume.", "properties": {"key": {"description": "key is the key to project.", "type": "string"}, "mode": {"description": "mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.", "type": "string"}}, "required": ["key", "path"], "type": "object"}, "type": "array"}, "name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}, "optional": {"description": "optional specify whether the ConfigMap or its keys must be defined", "type": "boolean"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "downwardAPI": {"description": "downwardAPI information about the downwardAPI data to project", "properties": {"items": {"description": "Items is a list of DownwardAPIVolume file", "items": {"description": "DownwardAPIVolumeFile represents information to create the file containing the pod field", "properties": {"fieldRef": {"description": "Required: Selects a field of the pod: only annotations, labels, name and namespace are supported.", "properties": {"apiVersion": {"description": "Version of the schema the FieldPath is written in terms of, defaults to \"v1\".", "type": "string"}, "fieldPath": {"description": "Path of the field to select in the specified API version.", "type": "string"}}, "required": ["fieldPath"], "type": "object", "x-kubernetes-map-type": "atomic"}, "mode": {"description": "Optional: mode bits used to set permissions on this file, must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "Required: Path is  the relative path name of the file to be created. Must not be absolute or contain the '..' path. Must be utf-8 encoded. The first item of the relative path must not start with '..'", "type": "string"}, "resourceFieldRef": {"description": "Selects a resource of the container: only resources limits and requests (limits.cpu, limits.memory, requests.cpu and requests.memory) are currently supported.", "properties": {"containerName": {"description": "Container name: required for volumes, optional for env vars", "type": "string"}, "divisor": {"anyOf": [{"type": "integer"}, {"type": "string"}], "description": "Specifies the output format of the exposed resources, defaults to \"1\"", "pattern": "^(\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))(([KMGTPE]i)|[numkMGTPE]|([eE](\\+|-)?(([0-9]+(\\.[0-9]*)?)|(\\.[0-9]+))))?$", "x-kubernetes-int-or-string": true}, "resource": {"description": "Required: resource to select", "type": "string"}}, "required": ["resource"], "type": "object", "x-kubernetes-map-type": "atomic"}}, "required": ["path"], "type": "object"}, "type": "array"}}, "type": "object"}, "secret": {"description": "secret information about the secret data to project", "properties": {"items": {"description": "items if unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.", "items": {"description": "Maps a string key to a path within a volume.", "properties": {"key": {"description": "key is the key to project.", "type": "string"}, "mode": {"description": "mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.", "type": "string"}}, "required": ["key", "path"], "type": "object"}, "type": "array"}, "name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}, "optional": {"description": "optional field specify whether the Secret or its key must be defined", "type": "boolean"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "serviceAccountToken": {"description": "serviceAccountToken is information about the serviceAccountToken data to project", "properties": {"audience": {"description": "audience is the intended audience of the token. A recipient of a token must identify itself with an identifier specified in the audience of the token, and otherwise should reject the token. The audience defaults to the identifier of the apiserver.", "type": "string"}, "expirationSeconds": {"description": "expirationSeconds is the requested duration of validity of the service account token. As the token approaches expiration, the kubelet volume plugin will proactively rotate the service account token. The kubelet will start trying to rotate the token if the token is older than 80 percent of its time to live or if the token is older than 24 hours.Defaults to 1 hour and must be at least 10 minutes.", "format": "int64", "type": "integer"}, "path": {"description": "path is the path relative to the mount point of the file to project the token into.", "type": "string"}}, "required": ["path"], "type": "object"}}, "type": "object"}, "type": "array"}}, "type": "object"}, "quobyte": {"description": "quobyte represents a Quobyte mount on the host that shares a pod's lifetime", "properties": {"group": {"description": "group to map volume access to <PERSON><PERSON><PERSON> is no group", "type": "string"}, "readOnly": {"description": "readOnly here will force the Quobyte volume to be mounted with read-only permissions. Defaults to false.", "type": "boolean"}, "registry": {"description": "registry represents a single or multiple Quobyte Registry services specified as a string as host:port pair (multiple entries are separated with commas) which acts as the central registry for volumes", "type": "string"}, "tenant": {"description": "tenant owning the given Quobyte volume in the Backend Used with dynamically provisioned Quobyte volumes, value is set by the plugin", "type": "string"}, "user": {"description": "user to map volume access to Defaults to serivceaccount user", "type": "string"}, "volume": {"description": "volume is a string that references an already created Quobyte volume by name.", "type": "string"}}, "required": ["registry", "volume"], "type": "object"}, "rbd": {"description": "rbd represents a Rados Block Device mount on the host that shares a pod's lifetime. More info: https://examples.k8s.io/volumes/rbd/README.md", "properties": {"fsType": {"description": "fsType is the filesystem type of the volume that you want to mount. Tip: Ensure that the filesystem type is supported by the host operating system. Examples: \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified. More info: https://kubernetes.io/docs/concepts/storage/volumes#rbd TODO: how do we prevent errors in the filesystem from compromising the machine", "type": "string"}, "image": {"description": "image is the rados image name. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "type": "string"}, "keyring": {"description": "keyring is the path to key ring for RBDUser. Default is /etc/ceph/keyring. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "type": "string"}, "monitors": {"description": "monitors is a collection of Ceph monitors. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "items": {"type": "string"}, "type": "array"}, "pool": {"description": "pool is the rados pool name. Default is rbd. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "type": "string"}, "readOnly": {"description": "readOnly here will force the ReadOnly setting in VolumeMounts. Defaults to false. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "type": "boolean"}, "secretRef": {"description": "secretRef is name of the authentication secret for RBDUser. If provided overrides keyring. Default is nil. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "user": {"description": "user is the rados user name. Default is admin. More info: https://examples.k8s.io/volumes/rbd/README.md#how-to-use-it", "type": "string"}}, "required": ["image", "monitors"], "type": "object"}, "scaleIO": {"description": "scaleIO represents a ScaleIO persistent volume attached and mounted on Kubernetes nodes.", "properties": {"fsType": {"description": "fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Default is \"xfs\".", "type": "string"}, "gateway": {"description": "gateway is the host address of the ScaleIO API Gateway.", "type": "string"}, "protectionDomain": {"description": "protectionDomain is the name of the ScaleIO Protection Domain for the configured storage.", "type": "string"}, "readOnly": {"description": "readOnly Defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}, "secretRef": {"description": "secretRef references to the secret for ScaleIO user and other sensitive information. If this is not provided, Login operation will fail.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "sslEnabled": {"description": "sslEnabled Flag enable/disable SSL communication with Gateway, default false", "type": "boolean"}, "storageMode": {"description": "storageMode indicates whether the storage for a volume should be ThickProvisioned or ThinProvisioned. Default is ThinProvisioned.", "type": "string"}, "storagePool": {"description": "storagePool is the ScaleIO Storage Pool associated with the protection domain.", "type": "string"}, "system": {"description": "system is the name of the storage system as configured in ScaleIO.", "type": "string"}, "volumeName": {"description": "volumeName is the name of a volume already created in the ScaleIO system that is associated with this volume source.", "type": "string"}}, "required": ["gateway", "secretRef", "system"], "type": "object"}, "secret": {"description": "secret represents a secret that should populate this volume. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret", "properties": {"defaultMode": {"description": "defaultMode is Optional: mode bits used to set permissions on created files by default. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. Defaults to 0644. Directories within the path are not affected by this setting. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "items": {"description": "items If unspecified, each key-value pair in the Data field of the referenced Secret will be projected into the volume as a file whose name is the key and content is the value. If specified, the listed keys will be projected into the specified paths, and unlisted keys will not be present. If a key is specified which is not present in the Secret, the volume setup will error unless it is marked optional. Paths must be relative and may not contain the '..' path or start with '..'.", "items": {"description": "Maps a string key to a path within a volume.", "properties": {"key": {"description": "key is the key to project.", "type": "string"}, "mode": {"description": "mode is Optional: mode bits used to set permissions on this file. Must be an octal value between 0000 and 0777 or a decimal value between 0 and 511. YAML accepts both octal and decimal values, JSON requires decimal values for mode bits. If not specified, the volume defaultMode will be used. This might be in conflict with other options that affect the file mode, like fsGroup, and the result can be other mode bits set.", "format": "int32", "type": "integer"}, "path": {"description": "path is the relative path of the file to map the key to. May not be an absolute path. May not contain the path element '..'. May not start with the string '..'.", "type": "string"}}, "required": ["key", "path"], "type": "object"}, "type": "array"}, "optional": {"description": "optional field specify whether the Secret or its keys must be defined", "type": "boolean"}, "secretName": {"description": "secretName is the name of the secret in the pod's namespace to use. More info: https://kubernetes.io/docs/concepts/storage/volumes#secret", "type": "string"}}, "type": "object"}, "storageos": {"description": "storageOS represents a StorageOS volume attached and mounted on Kubernetes nodes.", "properties": {"fsType": {"description": "fsType is the filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.", "type": "string"}, "readOnly": {"description": "readOnly defaults to false (read/write). ReadOnly here will force the ReadOnly setting in VolumeMounts.", "type": "boolean"}, "secretRef": {"description": "secretRef specifies the secret to use for obtaining the StorageOS API credentials.  If not specified, default values will be attempted.", "properties": {"name": {"description": "Name of the referent. More info: https://kubernetes.io/docs/concepts/overview/working-with-objects/names/#names TODO: Add other useful fields. apiVersion, kind, uid?", "type": "string"}}, "type": "object", "x-kubernetes-map-type": "atomic"}, "volumeName": {"description": "volumeName is the human-readable name of the StorageOS volume.  Volume names are only unique within a namespace.", "type": "string"}, "volumeNamespace": {"description": "volumeNamespace specifies the scope of the volume within StorageOS.  If no namespace is specified then the Pod's namespace will be used.  This allows the Kubernetes name scoping to be mirrored within StorageOS for tighter integration. Set VolumeName to any name to override the default behaviour. Set to \"default\" if you are not using namespaces within StorageOS. Namespaces that do not pre-exist within StorageOS will be created.", "type": "string"}}, "type": "object"}, "vsphereVolume": {"description": "vsphereVolume represents a vSphere volume attached and mounted on kubelets host machine", "properties": {"fsType": {"description": "fsType is filesystem type to mount. Must be a filesystem type supported by the host operating system. Ex. \"ext4\", \"xfs\", \"ntfs\". Implicitly inferred to be \"ext4\" if unspecified.", "type": "string"}, "storagePolicyID": {"description": "storagePolicyID is the storage Policy Based Management (SPBM) profile ID associated with the StoragePolicyName.", "type": "string"}, "storagePolicyName": {"description": "storagePolicyName is the storage Policy Based Management (SPBM) profile name.", "type": "string"}, "volumePath": {"description": "volumePath is the path that identifies vSphere volume vmdk", "type": "string"}}, "required": ["volumePath"], "type": "object"}}, "required": ["name"], "type": "object"}, "type": "array"}}, "type": "object"}, "type": "array"}}, "type": "object"}, "serviceAccount": {"type": "object", "properties": {"annotations": {"type": "object"}, "create": {"type": "boolean"}, "name": {"type": "string"}}}, "securityContext": {"type": "object"}, "tolerations": {"type": "array"}, "controllerTolerations": {"type": "array"}, "serviceMonitor": {"type": "object", "properties": {"create": {"type": "boolean"}}}}}