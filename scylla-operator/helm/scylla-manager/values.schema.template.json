{"$schema": "http://json-schema.org/schema#", "type": "object", "properties": {"affinity": {"type": "object"}, "controllerAffinity": {"type": "object"}, "controllerImage": {"type": "object", "properties": {"pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "controllerResources": {"type": "object", "properties": {"requests": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}, "limits": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}}}, "controllerSecurityContext": {"type": "object"}, "image": {"type": "object", "properties": {"pullPolicy": {"type": "string"}, "repository": {"type": "string"}, "tag": {"type": "string"}}}, "logLevel": {"type": "string"}, "nodeSelector": {"type": "object"}, "controllerNodeSelector": {"type": "object"}, "resources": {"type": "object", "properties": {"limits": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}, "requests": {"additionalProperties": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "object"}}}, "scylla": {"type": "object", "properties": {}}, "serviceAccount": {"type": "object", "properties": {"annotations": {"type": "object"}, "create": {"type": "boolean"}, "name": {"type": "string"}}}, "securityContext": {"type": "object"}, "tolerations": {"type": "array"}, "controllerTolerations": {"type": "array"}, "serviceMonitor": {"type": "object", "properties": {"create": {"type": "boolean"}}}}}