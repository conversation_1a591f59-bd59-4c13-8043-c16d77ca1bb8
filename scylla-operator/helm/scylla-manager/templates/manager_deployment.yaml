apiVersion: apps/v1
kind: Deployment
metadata:
  name: scylla-manager
  namespace: scylla-manager
  labels:
    {{- include "scylla-manager.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      {{- include "scylla-manager.selectorLabels" . | nindent 6 }}
  strategy:
    type: RollingUpdate
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "scylla-manager.labels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "scylla-manager.serviceAccountName" . }}
      {{- with .Values.securityContext }}
      securityContext: {{ toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: {{ .Chart.Name }}
        image: {{ .Values.image.repository }}/scylla-manager:{{ .Values.image.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.image.pullPolicy }}
        command:
        - /usr/bin/scylla-manager
        args:
        - --config-file=/mnt/etc/scylla-manager/scylla-manager.yaml
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        volumeMounts:
        - mountPath: /mnt/etc/scylla-manager
          name: scylla-manager-config
        readinessProbe:
          httpGet:
            path: /api/v1/clusters
            port: 5080
          periodSeconds: 10
          timeoutSeconds: 3
      volumes:
      - configMap:
          name: scylla-manager-config
        name: scylla-manager-config
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
