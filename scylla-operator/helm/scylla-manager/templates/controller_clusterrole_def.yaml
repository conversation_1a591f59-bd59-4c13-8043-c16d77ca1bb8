apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: scylladb:controller:aggregate-to-manager-controller
  labels:
    rbac.operator.scylladb.com/aggregate-to-scylla-manager-controller: "true"
rules:
- apiGroups:
  - ""
  resources:
  - events
  verbs:
  - create
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - secrets
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - ""
  resources:
  - services
  verbs:
  - get
  - list
- apiGroups:
  - apps
  resources:
  - statefulsets
  verbs:
  - get
  - list
- apiGroups:
  - scylla.scylladb.com
  resources:
  - scyllaclusters
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - scylla.scylladb.com
  resources:
  - scyllaclusters/status
  verbs:
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - delete
  - get
  - list
  - patch
  - update
  - watch
