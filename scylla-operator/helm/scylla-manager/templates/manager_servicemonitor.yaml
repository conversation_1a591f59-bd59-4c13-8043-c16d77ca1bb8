{{- if .Values.serviceMonitor.create -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: scylla-manager
  namespace: scylla-manager
spec:
  jobLabel: "app"
  selector:
    matchLabels:
      {{- include "scylla-manager.selectorLabels" . | nindent 6 }}
  endpoints:
  - port: metrics
    metricRelabelings:
    - sourceLabels: [ host ]
      targetLabel: instance
      regex: (.*)
      replacement: ${1}
{{ end }}
