apiVersion: apps/v1
kind: Deployment
metadata:
  name: scylla-manager-controller
  namespace: scylla-manager
  labels:
    {{- include "scylla-manager.controllerLabels" . | nindent 4 }}
spec:
  replicas: 2
  selector:
    matchLabels:
      {{- include "scylla-manager.controllerSelectorLabels" . | nindent 6 }}
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        {{- include "scylla-manager.controllerLabels" . | nindent 8 }}
    spec:
      serviceAccountName: {{ include "scylla-manager.controllerServiceAccountName" . }}
      {{- with .Values.controllerSecurityContext }}
      securityContext: {{ toYaml . | nindent 8 }}
      {{- end }}
      containers:
      - name: scylla-manager-controller
        image: {{ .Values.controllerImage.repository }}/scylla-operator:{{ .Values.controllerImage.tag | default .Chart.AppVersion }}
        imagePullPolicy: {{ .Values.controllerImage.pullPolicy }}
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        args:
        - manager-controller
        - --loglevel=2
        resources:
          {{- toYaml .Values.controllerResources | nindent 10 }}
      terminationGracePeriodSeconds: 10
      {{- with .Values.controllerNodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controllerAffinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.controllerTolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
