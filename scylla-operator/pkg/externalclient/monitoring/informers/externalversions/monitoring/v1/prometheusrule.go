// Code generated by informer-gen. DO NOT EDIT.

package v1

import (
	"context"
	time "time"

	monitoringv1 "github.com/scylladb/scylla-operator/pkg/externalapi/monitoring/v1"
	versioned "github.com/scylladb/scylla-operator/pkg/externalclient/monitoring/clientset/versioned"
	internalinterfaces "github.com/scylladb/scylla-operator/pkg/externalclient/monitoring/informers/externalversions/internalinterfaces"
	v1 "github.com/scylladb/scylla-operator/pkg/externalclient/monitoring/listers/monitoring/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
	watch "k8s.io/apimachinery/pkg/watch"
	cache "k8s.io/client-go/tools/cache"
)

// PrometheusRuleInformer provides access to a shared informer and lister for
// PrometheusRules.
type PrometheusRuleInformer interface {
	Informer() cache.SharedIndexInformer
	Lister() v1.PrometheusRuleLister
}

type prometheusRuleInformer struct {
	factory          internalinterfaces.SharedInformerFactory
	tweakListOptions internalinterfaces.TweakListOptionsFunc
	namespace        string
}

// NewPrometheusRuleInformer constructs a new informer for PrometheusRule type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewPrometheusRuleInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers) cache.SharedIndexInformer {
	return NewFilteredPrometheusRuleInformer(client, namespace, resyncPeriod, indexers, nil)
}

// NewFilteredPrometheusRuleInformer constructs a new informer for PrometheusRule type.
// Always prefer using an informer factory to get a shared informer instead of getting an independent
// one. This reduces memory footprint and number of connections to the server.
func NewFilteredPrometheusRuleInformer(client versioned.Interface, namespace string, resyncPeriod time.Duration, indexers cache.Indexers, tweakListOptions internalinterfaces.TweakListOptionsFunc) cache.SharedIndexInformer {
	return cache.NewSharedIndexInformer(
		&cache.ListWatch{
			ListFunc: func(options metav1.ListOptions) (runtime.Object, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.MonitoringV1().PrometheusRules(namespace).List(context.TODO(), options)
			},
			WatchFunc: func(options metav1.ListOptions) (watch.Interface, error) {
				if tweakListOptions != nil {
					tweakListOptions(&options)
				}
				return client.MonitoringV1().PrometheusRules(namespace).Watch(context.TODO(), options)
			},
		},
		&monitoringv1.PrometheusRule{},
		resyncPeriod,
		indexers,
	)
}

func (f *prometheusRuleInformer) defaultInformer(client versioned.Interface, resyncPeriod time.Duration) cache.SharedIndexInformer {
	return NewFilteredPrometheusRuleInformer(client, f.namespace, resyncPeriod, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc}, f.tweakListOptions)
}

func (f *prometheusRuleInformer) Informer() cache.SharedIndexInformer {
	return f.factory.InformerFor(&monitoringv1.PrometheusRule{}, f.defaultInformer)
}

func (f *prometheusRuleInformer) Lister() v1.PrometheusRuleLister {
	return v1.NewPrometheusRuleLister(f.Informer().GetIndexer())
}
