// Code generated by lister-gen. DO NOT EDIT.

package v1

// AlertmanagerListerExpansion allows custom methods to be added to
// AlertmanagerLister.
type AlertmanagerListerExpansion interface{}

// AlertmanagerNamespaceListerExpansion allows custom methods to be added to
// AlertmanagerNamespaceLister.
type AlertmanagerNamespaceListerExpansion interface{}

// PodMonitorListerExpansion allows custom methods to be added to
// PodMonitorLister.
type PodMonitorListerExpansion interface{}

// PodMonitorNamespaceListerExpansion allows custom methods to be added to
// PodMonitorNamespaceLister.
type PodMonitorNamespaceListerExpansion interface{}

// ProbeListerExpansion allows custom methods to be added to
// ProbeLister.
type ProbeListerExpansion interface{}

// ProbeNamespaceListerExpansion allows custom methods to be added to
// ProbeNamespaceLister.
type ProbeNamespaceListerExpansion interface{}

// PrometheusListerExpansion allows custom methods to be added to
// PrometheusLister.
type PrometheusListerExpansion interface{}

// PrometheusNamespaceListerExpansion allows custom methods to be added to
// PrometheusNamespaceLister.
type PrometheusNamespaceListerExpansion interface{}

// PrometheusRuleListerExpansion allows custom methods to be added to
// PrometheusRuleLister.
type PrometheusRuleListerExpansion interface{}

// PrometheusRuleNamespaceListerExpansion allows custom methods to be added to
// PrometheusRuleNamespaceLister.
type PrometheusRuleNamespaceListerExpansion interface{}

// ServiceMonitorListerExpansion allows custom methods to be added to
// ServiceMonitorLister.
type ServiceMonitorListerExpansion interface{}

// ServiceMonitorNamespaceListerExpansion allows custom methods to be added to
// ServiceMonitorNamespaceLister.
type ServiceMonitorNamespaceListerExpansion interface{}

// ThanosRulerListerExpansion allows custom methods to be added to
// ThanosRulerLister.
type ThanosRulerListerExpansion interface{}

// ThanosRulerNamespaceListerExpansion allows custom methods to be added to
// ThanosRulerNamespaceLister.
type ThanosRulerNamespaceListerExpansion interface{}
