// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	v1 "github.com/scylladb/scylla-operator/pkg/externalapi/monitoring/v1"
	scheme "github.com/scylladb/scylla-operator/pkg/externalclient/monitoring/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// PrometheusRulesGetter has a method to return a PrometheusRuleInterface.
// A group's client should implement this interface.
type PrometheusRulesGetter interface {
	PrometheusRules(namespace string) PrometheusRuleInterface
}

// PrometheusRuleInterface has methods to work with PrometheusRule resources.
type PrometheusRuleInterface interface {
	Create(ctx context.Context, prometheusRule *v1.PrometheusRule, opts metav1.CreateOptions) (*v1.PrometheusRule, error)
	Update(ctx context.Context, prometheusRule *v1.PrometheusRule, opts metav1.UpdateOptions) (*v1.PrometheusRule, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.PrometheusRule, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.PrometheusRuleList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.PrometheusRule, err error)
	PrometheusRuleExpansion
}

// prometheusRules implements PrometheusRuleInterface
type prometheusRules struct {
	client rest.Interface
	ns     string
}

// newPrometheusRules returns a PrometheusRules
func newPrometheusRules(c *MonitoringV1Client, namespace string) *prometheusRules {
	return &prometheusRules{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the prometheusRule, and returns the corresponding prometheusRule object, and an error if there is any.
func (c *prometheusRules) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.PrometheusRule, err error) {
	result = &v1.PrometheusRule{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("prometheusrules").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of PrometheusRules that match those selectors.
func (c *prometheusRules) List(ctx context.Context, opts metav1.ListOptions) (result *v1.PrometheusRuleList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.PrometheusRuleList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("prometheusrules").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested prometheusRules.
func (c *prometheusRules) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("prometheusrules").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a prometheusRule and creates it.  Returns the server's representation of the prometheusRule, and an error, if there is any.
func (c *prometheusRules) Create(ctx context.Context, prometheusRule *v1.PrometheusRule, opts metav1.CreateOptions) (result *v1.PrometheusRule, err error) {
	result = &v1.PrometheusRule{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("prometheusrules").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(prometheusRule).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a prometheusRule and updates it. Returns the server's representation of the prometheusRule, and an error, if there is any.
func (c *prometheusRules) Update(ctx context.Context, prometheusRule *v1.PrometheusRule, opts metav1.UpdateOptions) (result *v1.PrometheusRule, err error) {
	result = &v1.PrometheusRule{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("prometheusrules").
		Name(prometheusRule.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(prometheusRule).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the prometheusRule and deletes it. Returns an error if one occurs.
func (c *prometheusRules) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("prometheusrules").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *prometheusRules) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("prometheusrules").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched prometheusRule.
func (c *prometheusRules) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.PrometheusRule, err error) {
	result = &v1.PrometheusRule{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("prometheusrules").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
