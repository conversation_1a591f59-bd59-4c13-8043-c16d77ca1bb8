// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	v1 "github.com/scylladb/scylla-operator/pkg/externalapi/monitoring/v1"
	scheme "github.com/scylladb/scylla-operator/pkg/externalclient/monitoring/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// PodMonitorsGetter has a method to return a PodMonitorInterface.
// A group's client should implement this interface.
type PodMonitorsGetter interface {
	PodMonitors(namespace string) PodMonitorInterface
}

// PodMonitorInterface has methods to work with PodMonitor resources.
type PodMonitorInterface interface {
	Create(ctx context.Context, podMonitor *v1.PodMonitor, opts metav1.CreateOptions) (*v1.PodMonitor, error)
	Update(ctx context.Context, podMonitor *v1.PodMonitor, opts metav1.UpdateOptions) (*v1.PodMonitor, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.PodMonitor, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.PodMonitorList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.PodMonitor, err error)
	PodMonitorExpansion
}

// podMonitors implements PodMonitorInterface
type podMonitors struct {
	client rest.Interface
	ns     string
}

// newPodMonitors returns a PodMonitors
func newPodMonitors(c *MonitoringV1Client, namespace string) *podMonitors {
	return &podMonitors{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the podMonitor, and returns the corresponding podMonitor object, and an error if there is any.
func (c *podMonitors) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.PodMonitor, err error) {
	result = &v1.PodMonitor{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("podmonitors").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of PodMonitors that match those selectors.
func (c *podMonitors) List(ctx context.Context, opts metav1.ListOptions) (result *v1.PodMonitorList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.PodMonitorList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("podmonitors").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested podMonitors.
func (c *podMonitors) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("podmonitors").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a podMonitor and creates it.  Returns the server's representation of the podMonitor, and an error, if there is any.
func (c *podMonitors) Create(ctx context.Context, podMonitor *v1.PodMonitor, opts metav1.CreateOptions) (result *v1.PodMonitor, err error) {
	result = &v1.PodMonitor{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("podmonitors").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(podMonitor).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a podMonitor and updates it. Returns the server's representation of the podMonitor, and an error, if there is any.
func (c *podMonitors) Update(ctx context.Context, podMonitor *v1.PodMonitor, opts metav1.UpdateOptions) (result *v1.PodMonitor, err error) {
	result = &v1.PodMonitor{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("podmonitors").
		Name(podMonitor.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(podMonitor).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the podMonitor and deletes it. Returns an error if one occurs.
func (c *podMonitors) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("podmonitors").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *podMonitors) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("podmonitors").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched podMonitor.
func (c *podMonitors) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.PodMonitor, err error) {
	result = &v1.PodMonitor{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("podmonitors").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
