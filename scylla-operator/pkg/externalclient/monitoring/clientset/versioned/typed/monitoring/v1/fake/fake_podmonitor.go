// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1 "github.com/scylladb/scylla-operator/pkg/externalapi/monitoring/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakePodMonitors implements PodMonitorInterface
type FakePodMonitors struct {
	Fake *FakeMonitoringV1
	ns   string
}

var podmonitorsResource = v1.SchemeGroupVersion.WithResource("podmonitors")

var podmonitorsKind = v1.SchemeGroupVersion.WithKind("PodMonitor")

// Get takes name of the podMonitor, and returns the corresponding podMonitor object, and an error if there is any.
func (c *FakePodMonitors) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.PodMonitor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(podmonitorsResource, c.ns, name), &v1.PodMonitor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.PodMonitor), err
}

// List takes label and field selectors, and returns the list of PodMonitors that match those selectors.
func (c *FakePodMonitors) List(ctx context.Context, opts metav1.ListOptions) (result *v1.PodMonitorList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(podmonitorsResource, podmonitorsKind, c.ns, opts), &v1.PodMonitorList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1.PodMonitorList{ListMeta: obj.(*v1.PodMonitorList).ListMeta}
	for _, item := range obj.(*v1.PodMonitorList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested podMonitors.
func (c *FakePodMonitors) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(podmonitorsResource, c.ns, opts))

}

// Create takes the representation of a podMonitor and creates it.  Returns the server's representation of the podMonitor, and an error, if there is any.
func (c *FakePodMonitors) Create(ctx context.Context, podMonitor *v1.PodMonitor, opts metav1.CreateOptions) (result *v1.PodMonitor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(podmonitorsResource, c.ns, podMonitor), &v1.PodMonitor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.PodMonitor), err
}

// Update takes the representation of a podMonitor and updates it. Returns the server's representation of the podMonitor, and an error, if there is any.
func (c *FakePodMonitors) Update(ctx context.Context, podMonitor *v1.PodMonitor, opts metav1.UpdateOptions) (result *v1.PodMonitor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(podmonitorsResource, c.ns, podMonitor), &v1.PodMonitor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.PodMonitor), err
}

// Delete takes name of the podMonitor and deletes it. Returns an error if one occurs.
func (c *FakePodMonitors) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(podmonitorsResource, c.ns, name, opts), &v1.PodMonitor{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakePodMonitors) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(podmonitorsResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1.PodMonitorList{})
	return err
}

// Patch applies the patch and returns the patched podMonitor.
func (c *FakePodMonitors) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.PodMonitor, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(podmonitorsResource, c.ns, name, pt, data, subresources...), &v1.PodMonitor{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1.PodMonitor), err
}
