// Code generated by client-gen. DO NOT EDIT.

package v1

import (
	"context"
	"time"

	v1 "github.com/scylladb/scylla-operator/pkg/externalapi/monitoring/v1"
	scheme "github.com/scylladb/scylla-operator/pkg/externalclient/monitoring/clientset/versioned/scheme"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// ThanosRulersGetter has a method to return a ThanosRulerInterface.
// A group's client should implement this interface.
type ThanosRulersGetter interface {
	ThanosRulers(namespace string) ThanosRulerInterface
}

// ThanosRulerInterface has methods to work with ThanosRuler resources.
type ThanosRulerInterface interface {
	Create(ctx context.Context, thanosRuler *v1.<PERSON>os<PERSON><PERSON><PERSON>, opts metav1.CreateOptions) (*v1.ThanosRuler, error)
	Update(ctx context.Context, thanosRuler *v1.ThanosRuler, opts metav1.UpdateOptions) (*v1.ThanosRuler, error)
	UpdateStatus(ctx context.Context, thanosRuler *v1.ThanosRuler, opts metav1.UpdateOptions) (*v1.ThanosRuler, error)
	Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error
	Get(ctx context.Context, name string, opts metav1.GetOptions) (*v1.ThanosRuler, error)
	List(ctx context.Context, opts metav1.ListOptions) (*v1.ThanosRulerList, error)
	Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ThanosRuler, err error)
	ThanosRulerExpansion
}

// thanosRulers implements ThanosRulerInterface
type thanosRulers struct {
	client rest.Interface
	ns     string
}

// newThanosRulers returns a ThanosRulers
func newThanosRulers(c *MonitoringV1Client, namespace string) *thanosRulers {
	return &thanosRulers{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the thanosRuler, and returns the corresponding thanosRuler object, and an error if there is any.
func (c *thanosRulers) Get(ctx context.Context, name string, options metav1.GetOptions) (result *v1.ThanosRuler, err error) {
	result = &v1.ThanosRuler{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("thanosrulers").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ThanosRulers that match those selectors.
func (c *thanosRulers) List(ctx context.Context, opts metav1.ListOptions) (result *v1.ThanosRulerList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1.ThanosRulerList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("thanosrulers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested thanosRulers.
func (c *thanosRulers) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("thanosrulers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a thanosRuler and creates it.  Returns the server's representation of the thanosRuler, and an error, if there is any.
func (c *thanosRulers) Create(ctx context.Context, thanosRuler *v1.ThanosRuler, opts metav1.CreateOptions) (result *v1.ThanosRuler, err error) {
	result = &v1.ThanosRuler{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("thanosrulers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(thanosRuler).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a thanosRuler and updates it. Returns the server's representation of the thanosRuler, and an error, if there is any.
func (c *thanosRulers) Update(ctx context.Context, thanosRuler *v1.ThanosRuler, opts metav1.UpdateOptions) (result *v1.ThanosRuler, err error) {
	result = &v1.ThanosRuler{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("thanosrulers").
		Name(thanosRuler.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(thanosRuler).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *thanosRulers) UpdateStatus(ctx context.Context, thanosRuler *v1.ThanosRuler, opts metav1.UpdateOptions) (result *v1.ThanosRuler, err error) {
	result = &v1.ThanosRuler{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("thanosrulers").
		Name(thanosRuler.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(thanosRuler).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the thanosRuler and deletes it. Returns an error if one occurs.
func (c *thanosRulers) Delete(ctx context.Context, name string, opts metav1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("thanosrulers").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *thanosRulers) DeleteCollection(ctx context.Context, opts metav1.DeleteOptions, listOpts metav1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("thanosrulers").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched thanosRuler.
func (c *thanosRulers) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts metav1.PatchOptions, subresources ...string) (result *v1.ThanosRuler, err error) {
	result = &v1.ThanosRuler{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("thanosrulers").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
