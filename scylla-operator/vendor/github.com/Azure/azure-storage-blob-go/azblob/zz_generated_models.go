package azblob

// Code generated by Microsoft (R) AutoRest Code Generator.
// Changes may cause incorrect behavior and will be lost if the code is regenerated.

import (
	"encoding/base64"
	"encoding/xml"
	"errors"
	"io"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"
	"unsafe"
)

// ETag is an entity tag.
type ETag string

const (
	// ETagNone represents an empty entity tag.
	ETagNone ETag = ""

	// ETagAny matches any entity tag.
	ETagAny ETag = "*"
)

// Metadata contains metadata key/value pairs.
type Metadata map[string]string

const mdPrefix = "x-ms-meta-"

const mdPrefixLen = len(mdPrefix)

// UnmarshalXML implements the xml.Unmarshaler interface for Metadata.
func (md *Metadata) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	tokName := ""
	for t, err := d.Token(); err == nil; t, err = d.To<PERSON>() {
		switch tt := t.(type) {
		case xml.StartElement:
			tokName = strings.ToLower(tt.Name.Local)
			break
		case xml.CharData:
			if *md == nil {
				*md = Metadata{}
			}
			(*md)[tokName] = string(tt)
			break
		}
	}
	return nil
}

// Marker represents an opaque value used in paged responses.
type Marker struct {
	Val *string
}

// NotDone returns true if the list enumeration should be started or is not yet complete. Specifically, NotDone returns true
// for a just-initialized (zero value) Marker indicating that you should make an initial request to get a result portion from
// the service. NotDone also returns true whenever the service returns an interim result portion. NotDone returns false only
// after the service has returned the final result portion.
func (m Marker) NotDone() bool {
	return m.Val == nil || *m.Val != ""
}

// UnmarshalXML implements the xml.Unmarshaler interface for Marker.
func (m *Marker) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	var out string
	err := d.DecodeElement(&out, &start)
	m.Val = &out
	return err
}

// concatenates a slice of const values with the specified separator between each item
func joinConst(s interface{}, sep string) string {
	v := reflect.ValueOf(s)
	if v.Kind() != reflect.Slice && v.Kind() != reflect.Array {
		panic("s wasn't a slice or array")
	}
	ss := make([]string, 0, v.Len())
	for i := 0; i < v.Len(); i++ {
		ss = append(ss, v.Index(i).String())
	}
	return strings.Join(ss, sep)
}

func validateError(err error) {
	if err != nil {
		panic(err)
	}
}

// AccessTierType enumerates the values for access tier type.
type AccessTierType string

const (
	// AccessTierArchive ...
	AccessTierArchive AccessTierType = "Archive"
	// AccessTierCool ...
	AccessTierCool AccessTierType = "Cool"
	// AccessTierHot ...
	AccessTierHot AccessTierType = "Hot"
	// AccessTierNone represents an empty AccessTierType.
	AccessTierNone AccessTierType = ""
	// AccessTierP10 ...
	AccessTierP10 AccessTierType = "P10"
	// AccessTierP15 ...
	AccessTierP15 AccessTierType = "P15"
	// AccessTierP20 ...
	AccessTierP20 AccessTierType = "P20"
	// AccessTierP30 ...
	AccessTierP30 AccessTierType = "P30"
	// AccessTierP4 ...
	AccessTierP4 AccessTierType = "P4"
	// AccessTierP40 ...
	AccessTierP40 AccessTierType = "P40"
	// AccessTierP50 ...
	AccessTierP50 AccessTierType = "P50"
	// AccessTierP6 ...
	AccessTierP6 AccessTierType = "P6"
	// AccessTierP60 ...
	AccessTierP60 AccessTierType = "P60"
	// AccessTierP70 ...
	AccessTierP70 AccessTierType = "P70"
	// AccessTierP80 ...
	AccessTierP80 AccessTierType = "P80"
)

// PossibleAccessTierTypeValues returns an array of possible values for the AccessTierType const type.
func PossibleAccessTierTypeValues() []AccessTierType {
	return []AccessTierType{AccessTierArchive, AccessTierCool, AccessTierHot, AccessTierNone, AccessTierP10, AccessTierP15, AccessTierP20, AccessTierP30, AccessTierP4, AccessTierP40, AccessTierP50, AccessTierP6, AccessTierP60, AccessTierP70, AccessTierP80}
}

// AccountKindType enumerates the values for account kind type.
type AccountKindType string

const (
	// AccountKindBlobStorage ...
	AccountKindBlobStorage AccountKindType = "BlobStorage"
	// AccountKindBlockBlobStorage ...
	AccountKindBlockBlobStorage AccountKindType = "BlockBlobStorage"
	// AccountKindFileStorage ...
	AccountKindFileStorage AccountKindType = "FileStorage"
	// AccountKindNone represents an empty AccountKindType.
	AccountKindNone AccountKindType = ""
	// AccountKindStorage ...
	AccountKindStorage AccountKindType = "Storage"
	// AccountKindStorageV2 ...
	AccountKindStorageV2 AccountKindType = "StorageV2"
)

// PossibleAccountKindTypeValues returns an array of possible values for the AccountKindType const type.
func PossibleAccountKindTypeValues() []AccountKindType {
	return []AccountKindType{AccountKindBlobStorage, AccountKindBlockBlobStorage, AccountKindFileStorage, AccountKindNone, AccountKindStorage, AccountKindStorageV2}
}

// ArchiveStatusType enumerates the values for archive status type.
type ArchiveStatusType string

const (
	// ArchiveStatusNone represents an empty ArchiveStatusType.
	ArchiveStatusNone ArchiveStatusType = ""
	// ArchiveStatusRehydratePendingToCool ...
	ArchiveStatusRehydratePendingToCool ArchiveStatusType = "rehydrate-pending-to-cool"
	// ArchiveStatusRehydratePendingToHot ...
	ArchiveStatusRehydratePendingToHot ArchiveStatusType = "rehydrate-pending-to-hot"
)

// PossibleArchiveStatusTypeValues returns an array of possible values for the ArchiveStatusType const type.
func PossibleArchiveStatusTypeValues() []ArchiveStatusType {
	return []ArchiveStatusType{ArchiveStatusNone, ArchiveStatusRehydratePendingToCool, ArchiveStatusRehydratePendingToHot}
}

// BlobExpiryOptionsType enumerates the values for blob expiry options type.
type BlobExpiryOptionsType string

const (
	// BlobExpiryOptionsAbsolute ...
	BlobExpiryOptionsAbsolute BlobExpiryOptionsType = "Absolute"
	// BlobExpiryOptionsNeverExpire ...
	BlobExpiryOptionsNeverExpire BlobExpiryOptionsType = "NeverExpire"
	// BlobExpiryOptionsNone represents an empty BlobExpiryOptionsType.
	BlobExpiryOptionsNone BlobExpiryOptionsType = ""
	// BlobExpiryOptionsRelativeToCreation ...
	BlobExpiryOptionsRelativeToCreation BlobExpiryOptionsType = "RelativeToCreation"
	// BlobExpiryOptionsRelativeToNow ...
	BlobExpiryOptionsRelativeToNow BlobExpiryOptionsType = "RelativeToNow"
)

// PossibleBlobExpiryOptionsTypeValues returns an array of possible values for the BlobExpiryOptionsType const type.
func PossibleBlobExpiryOptionsTypeValues() []BlobExpiryOptionsType {
	return []BlobExpiryOptionsType{BlobExpiryOptionsAbsolute, BlobExpiryOptionsNeverExpire, BlobExpiryOptionsNone, BlobExpiryOptionsRelativeToCreation, BlobExpiryOptionsRelativeToNow}
}

// BlobType enumerates the values for blob type.
type BlobType string

const (
	// BlobAppendBlob ...
	BlobAppendBlob BlobType = "AppendBlob"
	// BlobBlockBlob ...
	BlobBlockBlob BlobType = "BlockBlob"
	// BlobNone represents an empty BlobType.
	BlobNone BlobType = ""
	// BlobPageBlob ...
	BlobPageBlob BlobType = "PageBlob"
)

// PossibleBlobTypeValues returns an array of possible values for the BlobType const type.
func PossibleBlobTypeValues() []BlobType {
	return []BlobType{BlobAppendBlob, BlobBlockBlob, BlobNone, BlobPageBlob}
}

// BlockListType enumerates the values for block list type.
type BlockListType string

const (
	// BlockListAll ...
	BlockListAll BlockListType = "all"
	// BlockListCommitted ...
	BlockListCommitted BlockListType = "committed"
	// BlockListNone represents an empty BlockListType.
	BlockListNone BlockListType = ""
	// BlockListUncommitted ...
	BlockListUncommitted BlockListType = "uncommitted"
)

// PossibleBlockListTypeValues returns an array of possible values for the BlockListType const type.
func PossibleBlockListTypeValues() []BlockListType {
	return []BlockListType{BlockListAll, BlockListCommitted, BlockListNone, BlockListUncommitted}
}

// CopyStatusType enumerates the values for copy status type.
type CopyStatusType string

const (
	// CopyStatusAborted ...
	CopyStatusAborted CopyStatusType = "aborted"
	// CopyStatusFailed ...
	CopyStatusFailed CopyStatusType = "failed"
	// CopyStatusNone represents an empty CopyStatusType.
	CopyStatusNone CopyStatusType = ""
	// CopyStatusPending ...
	CopyStatusPending CopyStatusType = "pending"
	// CopyStatusSuccess ...
	CopyStatusSuccess CopyStatusType = "success"
)

// PossibleCopyStatusTypeValues returns an array of possible values for the CopyStatusType const type.
func PossibleCopyStatusTypeValues() []CopyStatusType {
	return []CopyStatusType{CopyStatusAborted, CopyStatusFailed, CopyStatusNone, CopyStatusPending, CopyStatusSuccess}
}

// DeleteSnapshotsOptionType enumerates the values for delete snapshots option type.
type DeleteSnapshotsOptionType string

const (
	// DeleteSnapshotsOptionInclude ...
	DeleteSnapshotsOptionInclude DeleteSnapshotsOptionType = "include"
	// DeleteSnapshotsOptionNone represents an empty DeleteSnapshotsOptionType.
	DeleteSnapshotsOptionNone DeleteSnapshotsOptionType = ""
	// DeleteSnapshotsOptionOnly ...
	DeleteSnapshotsOptionOnly DeleteSnapshotsOptionType = "only"
)

// PossibleDeleteSnapshotsOptionTypeValues returns an array of possible values for the DeleteSnapshotsOptionType const type.
func PossibleDeleteSnapshotsOptionTypeValues() []DeleteSnapshotsOptionType {
	return []DeleteSnapshotsOptionType{DeleteSnapshotsOptionInclude, DeleteSnapshotsOptionNone, DeleteSnapshotsOptionOnly}
}

// EncryptionAlgorithmType enumerates the values for encryption algorithm type.
type EncryptionAlgorithmType string

const (
	// EncryptionAlgorithmAES256 ...
	EncryptionAlgorithmAES256 EncryptionAlgorithmType = "AES256"
	// EncryptionAlgorithmNone represents an empty EncryptionAlgorithmType.
	EncryptionAlgorithmNone EncryptionAlgorithmType = ""
)

// PossibleEncryptionAlgorithmTypeValues returns an array of possible values for the EncryptionAlgorithmType const type.
func PossibleEncryptionAlgorithmTypeValues() []EncryptionAlgorithmType {
	return []EncryptionAlgorithmType{EncryptionAlgorithmAES256, EncryptionAlgorithmNone}
}

// GeoReplicationStatusType enumerates the values for geo replication status type.
type GeoReplicationStatusType string

const (
	// GeoReplicationStatusBootstrap ...
	GeoReplicationStatusBootstrap GeoReplicationStatusType = "bootstrap"
	// GeoReplicationStatusLive ...
	GeoReplicationStatusLive GeoReplicationStatusType = "live"
	// GeoReplicationStatusNone represents an empty GeoReplicationStatusType.
	GeoReplicationStatusNone GeoReplicationStatusType = ""
	// GeoReplicationStatusUnavailable ...
	GeoReplicationStatusUnavailable GeoReplicationStatusType = "unavailable"
)

// PossibleGeoReplicationStatusTypeValues returns an array of possible values for the GeoReplicationStatusType const type.
func PossibleGeoReplicationStatusTypeValues() []GeoReplicationStatusType {
	return []GeoReplicationStatusType{GeoReplicationStatusBootstrap, GeoReplicationStatusLive, GeoReplicationStatusNone, GeoReplicationStatusUnavailable}
}

// LeaseDurationType enumerates the values for lease duration type.
type LeaseDurationType string

const (
	// LeaseDurationFixed ...
	LeaseDurationFixed LeaseDurationType = "fixed"
	// LeaseDurationInfinite ...
	LeaseDurationInfinite LeaseDurationType = "infinite"
	// LeaseDurationNone represents an empty LeaseDurationType.
	LeaseDurationNone LeaseDurationType = ""
)

// PossibleLeaseDurationTypeValues returns an array of possible values for the LeaseDurationType const type.
func PossibleLeaseDurationTypeValues() []LeaseDurationType {
	return []LeaseDurationType{LeaseDurationFixed, LeaseDurationInfinite, LeaseDurationNone}
}

// LeaseStateType enumerates the values for lease state type.
type LeaseStateType string

const (
	// LeaseStateAvailable ...
	LeaseStateAvailable LeaseStateType = "available"
	// LeaseStateBreaking ...
	LeaseStateBreaking LeaseStateType = "breaking"
	// LeaseStateBroken ...
	LeaseStateBroken LeaseStateType = "broken"
	// LeaseStateExpired ...
	LeaseStateExpired LeaseStateType = "expired"
	// LeaseStateLeased ...
	LeaseStateLeased LeaseStateType = "leased"
	// LeaseStateNone represents an empty LeaseStateType.
	LeaseStateNone LeaseStateType = ""
)

// PossibleLeaseStateTypeValues returns an array of possible values for the LeaseStateType const type.
func PossibleLeaseStateTypeValues() []LeaseStateType {
	return []LeaseStateType{LeaseStateAvailable, LeaseStateBreaking, LeaseStateBroken, LeaseStateExpired, LeaseStateLeased, LeaseStateNone}
}

// LeaseStatusType enumerates the values for lease status type.
type LeaseStatusType string

const (
	// LeaseStatusLocked ...
	LeaseStatusLocked LeaseStatusType = "locked"
	// LeaseStatusNone represents an empty LeaseStatusType.
	LeaseStatusNone LeaseStatusType = ""
	// LeaseStatusUnlocked ...
	LeaseStatusUnlocked LeaseStatusType = "unlocked"
)

// PossibleLeaseStatusTypeValues returns an array of possible values for the LeaseStatusType const type.
func PossibleLeaseStatusTypeValues() []LeaseStatusType {
	return []LeaseStatusType{LeaseStatusLocked, LeaseStatusNone, LeaseStatusUnlocked}
}

// ListBlobsIncludeItemType enumerates the values for list blobs include item type.
type ListBlobsIncludeItemType string

const (
	// ListBlobsIncludeItemCopy ...
	ListBlobsIncludeItemCopy ListBlobsIncludeItemType = "copy"
	// ListBlobsIncludeItemDeleted ...
	ListBlobsIncludeItemDeleted ListBlobsIncludeItemType = "deleted"
	// ListBlobsIncludeItemMetadata ...
	ListBlobsIncludeItemMetadata ListBlobsIncludeItemType = "metadata"
	// ListBlobsIncludeItemNone represents an empty ListBlobsIncludeItemType.
	ListBlobsIncludeItemNone ListBlobsIncludeItemType = ""
	// ListBlobsIncludeItemSnapshots ...
	ListBlobsIncludeItemSnapshots ListBlobsIncludeItemType = "snapshots"
	// ListBlobsIncludeItemTags ...
	ListBlobsIncludeItemTags ListBlobsIncludeItemType = "tags"
	// ListBlobsIncludeItemUncommittedblobs ...
	ListBlobsIncludeItemUncommittedblobs ListBlobsIncludeItemType = "uncommittedblobs"
	// ListBlobsIncludeItemVersions ...
	ListBlobsIncludeItemVersions ListBlobsIncludeItemType = "versions"
)

// PossibleListBlobsIncludeItemTypeValues returns an array of possible values for the ListBlobsIncludeItemType const type.
func PossibleListBlobsIncludeItemTypeValues() []ListBlobsIncludeItemType {
	return []ListBlobsIncludeItemType{ListBlobsIncludeItemCopy, ListBlobsIncludeItemDeleted, ListBlobsIncludeItemMetadata, ListBlobsIncludeItemNone, ListBlobsIncludeItemSnapshots, ListBlobsIncludeItemTags, ListBlobsIncludeItemUncommittedblobs, ListBlobsIncludeItemVersions}
}

// ListContainersIncludeType enumerates the values for list containers include type.
type ListContainersIncludeType string

const (
	// ListContainersIncludeDeleted ...
	ListContainersIncludeDeleted ListContainersIncludeType = "deleted"
	// ListContainersIncludeMetadata ...
	ListContainersIncludeMetadata ListContainersIncludeType = "metadata"
	// ListContainersIncludeNone represents an empty ListContainersIncludeType.
	ListContainersIncludeNone ListContainersIncludeType = ""
)

// PossibleListContainersIncludeTypeValues returns an array of possible values for the ListContainersIncludeType const type.
func PossibleListContainersIncludeTypeValues() []ListContainersIncludeType {
	return []ListContainersIncludeType{ListContainersIncludeDeleted, ListContainersIncludeMetadata, ListContainersIncludeNone}
}

// PathRenameModeType enumerates the values for path rename mode type.
type PathRenameModeType string

const (
	// PathRenameModeLegacy ...
	PathRenameModeLegacy PathRenameModeType = "legacy"
	// PathRenameModeNone represents an empty PathRenameModeType.
	PathRenameModeNone PathRenameModeType = ""
	// PathRenameModePosix ...
	PathRenameModePosix PathRenameModeType = "posix"
)

// PossiblePathRenameModeTypeValues returns an array of possible values for the PathRenameModeType const type.
func PossiblePathRenameModeTypeValues() []PathRenameModeType {
	return []PathRenameModeType{PathRenameModeLegacy, PathRenameModeNone, PathRenameModePosix}
}

// PremiumPageBlobAccessTierType enumerates the values for premium page blob access tier type.
type PremiumPageBlobAccessTierType string

const (
	// PremiumPageBlobAccessTierNone represents an empty PremiumPageBlobAccessTierType.
	PremiumPageBlobAccessTierNone PremiumPageBlobAccessTierType = ""
	// PremiumPageBlobAccessTierP10 ...
	PremiumPageBlobAccessTierP10 PremiumPageBlobAccessTierType = "P10"
	// PremiumPageBlobAccessTierP15 ...
	PremiumPageBlobAccessTierP15 PremiumPageBlobAccessTierType = "P15"
	// PremiumPageBlobAccessTierP20 ...
	PremiumPageBlobAccessTierP20 PremiumPageBlobAccessTierType = "P20"
	// PremiumPageBlobAccessTierP30 ...
	PremiumPageBlobAccessTierP30 PremiumPageBlobAccessTierType = "P30"
	// PremiumPageBlobAccessTierP4 ...
	PremiumPageBlobAccessTierP4 PremiumPageBlobAccessTierType = "P4"
	// PremiumPageBlobAccessTierP40 ...
	PremiumPageBlobAccessTierP40 PremiumPageBlobAccessTierType = "P40"
	// PremiumPageBlobAccessTierP50 ...
	PremiumPageBlobAccessTierP50 PremiumPageBlobAccessTierType = "P50"
	// PremiumPageBlobAccessTierP6 ...
	PremiumPageBlobAccessTierP6 PremiumPageBlobAccessTierType = "P6"
	// PremiumPageBlobAccessTierP60 ...
	PremiumPageBlobAccessTierP60 PremiumPageBlobAccessTierType = "P60"
	// PremiumPageBlobAccessTierP70 ...
	PremiumPageBlobAccessTierP70 PremiumPageBlobAccessTierType = "P70"
	// PremiumPageBlobAccessTierP80 ...
	PremiumPageBlobAccessTierP80 PremiumPageBlobAccessTierType = "P80"
)

// PossiblePremiumPageBlobAccessTierTypeValues returns an array of possible values for the PremiumPageBlobAccessTierType const type.
func PossiblePremiumPageBlobAccessTierTypeValues() []PremiumPageBlobAccessTierType {
	return []PremiumPageBlobAccessTierType{PremiumPageBlobAccessTierNone, PremiumPageBlobAccessTierP10, PremiumPageBlobAccessTierP15, PremiumPageBlobAccessTierP20, PremiumPageBlobAccessTierP30, PremiumPageBlobAccessTierP4, PremiumPageBlobAccessTierP40, PremiumPageBlobAccessTierP50, PremiumPageBlobAccessTierP6, PremiumPageBlobAccessTierP60, PremiumPageBlobAccessTierP70, PremiumPageBlobAccessTierP80}
}

// PublicAccessType enumerates the values for public access type.
type PublicAccessType string

const (
	// PublicAccessBlob ...
	PublicAccessBlob PublicAccessType = "blob"
	// PublicAccessContainer ...
	PublicAccessContainer PublicAccessType = "container"
	// PublicAccessNone represents an empty PublicAccessType.
	PublicAccessNone PublicAccessType = ""
)

// PossiblePublicAccessTypeValues returns an array of possible values for the PublicAccessType const type.
func PossiblePublicAccessTypeValues() []PublicAccessType {
	return []PublicAccessType{PublicAccessBlob, PublicAccessContainer, PublicAccessNone}
}

// QueryFormatType enumerates the values for query format type.
type QueryFormatType string

const (
	// QueryFormatDelimited ...
	QueryFormatDelimited QueryFormatType = "delimited"
	// QueryFormatJSON ...
	QueryFormatJSON QueryFormatType = "json"
	// QueryFormatNone represents an empty QueryFormatType.
	QueryFormatNone QueryFormatType = ""
)

// PossibleQueryFormatTypeValues returns an array of possible values for the QueryFormatType const type.
func PossibleQueryFormatTypeValues() []QueryFormatType {
	return []QueryFormatType{QueryFormatDelimited, QueryFormatJSON, QueryFormatNone}
}

// RehydratePriorityType enumerates the values for rehydrate priority type.
type RehydratePriorityType string

const (
	// RehydratePriorityHigh ...
	RehydratePriorityHigh RehydratePriorityType = "High"
	// RehydratePriorityNone represents an empty RehydratePriorityType.
	RehydratePriorityNone RehydratePriorityType = ""
	// RehydratePriorityStandard ...
	RehydratePriorityStandard RehydratePriorityType = "Standard"
)

// PossibleRehydratePriorityTypeValues returns an array of possible values for the RehydratePriorityType const type.
func PossibleRehydratePriorityTypeValues() []RehydratePriorityType {
	return []RehydratePriorityType{RehydratePriorityHigh, RehydratePriorityNone, RehydratePriorityStandard}
}

// SequenceNumberActionType enumerates the values for sequence number action type.
type SequenceNumberActionType string

const (
	// SequenceNumberActionIncrement ...
	SequenceNumberActionIncrement SequenceNumberActionType = "increment"
	// SequenceNumberActionMax ...
	SequenceNumberActionMax SequenceNumberActionType = "max"
	// SequenceNumberActionNone represents an empty SequenceNumberActionType.
	SequenceNumberActionNone SequenceNumberActionType = ""
	// SequenceNumberActionUpdate ...
	SequenceNumberActionUpdate SequenceNumberActionType = "update"
)

// PossibleSequenceNumberActionTypeValues returns an array of possible values for the SequenceNumberActionType const type.
func PossibleSequenceNumberActionTypeValues() []SequenceNumberActionType {
	return []SequenceNumberActionType{SequenceNumberActionIncrement, SequenceNumberActionMax, SequenceNumberActionNone, SequenceNumberActionUpdate}
}

// SkuNameType enumerates the values for sku name type.
type SkuNameType string

const (
	// SkuNameNone represents an empty SkuNameType.
	SkuNameNone SkuNameType = ""
	// SkuNamePremiumLRS ...
	SkuNamePremiumLRS SkuNameType = "Premium_LRS"
	// SkuNameStandardGRS ...
	SkuNameStandardGRS SkuNameType = "Standard_GRS"
	// SkuNameStandardLRS ...
	SkuNameStandardLRS SkuNameType = "Standard_LRS"
	// SkuNameStandardRAGRS ...
	SkuNameStandardRAGRS SkuNameType = "Standard_RAGRS"
	// SkuNameStandardZRS ...
	SkuNameStandardZRS SkuNameType = "Standard_ZRS"
)

// PossibleSkuNameTypeValues returns an array of possible values for the SkuNameType const type.
func PossibleSkuNameTypeValues() []SkuNameType {
	return []SkuNameType{SkuNameNone, SkuNamePremiumLRS, SkuNameStandardGRS, SkuNameStandardLRS, SkuNameStandardRAGRS, SkuNameStandardZRS}
}

// StorageErrorCodeType enumerates the values for storage error code type.
type StorageErrorCodeType string

const (
	// StorageErrorCodeAccountAlreadyExists ...
	StorageErrorCodeAccountAlreadyExists StorageErrorCodeType = "AccountAlreadyExists"
	// StorageErrorCodeAccountBeingCreated ...
	StorageErrorCodeAccountBeingCreated StorageErrorCodeType = "AccountBeingCreated"
	// StorageErrorCodeAccountIsDisabled ...
	StorageErrorCodeAccountIsDisabled StorageErrorCodeType = "AccountIsDisabled"
	// StorageErrorCodeAppendPositionConditionNotMet ...
	StorageErrorCodeAppendPositionConditionNotMet StorageErrorCodeType = "AppendPositionConditionNotMet"
	// StorageErrorCodeAuthenticationFailed ...
	StorageErrorCodeAuthenticationFailed StorageErrorCodeType = "AuthenticationFailed"
	// StorageErrorCodeAuthorizationFailure ...
	StorageErrorCodeAuthorizationFailure StorageErrorCodeType = "AuthorizationFailure"
	// StorageErrorCodeAuthorizationPermissionMismatch ...
	StorageErrorCodeAuthorizationPermissionMismatch StorageErrorCodeType = "AuthorizationPermissionMismatch"
	// StorageErrorCodeAuthorizationProtocolMismatch ...
	StorageErrorCodeAuthorizationProtocolMismatch StorageErrorCodeType = "AuthorizationProtocolMismatch"
	// StorageErrorCodeAuthorizationResourceTypeMismatch ...
	StorageErrorCodeAuthorizationResourceTypeMismatch StorageErrorCodeType = "AuthorizationResourceTypeMismatch"
	// StorageErrorCodeAuthorizationServiceMismatch ...
	StorageErrorCodeAuthorizationServiceMismatch StorageErrorCodeType = "AuthorizationServiceMismatch"
	// StorageErrorCodeAuthorizationSourceIPMismatch ...
	StorageErrorCodeAuthorizationSourceIPMismatch StorageErrorCodeType = "AuthorizationSourceIPMismatch"
	// StorageErrorCodeBlobAlreadyExists ...
	StorageErrorCodeBlobAlreadyExists StorageErrorCodeType = "BlobAlreadyExists"
	// StorageErrorCodeBlobArchived ...
	StorageErrorCodeBlobArchived StorageErrorCodeType = "BlobArchived"
	// StorageErrorCodeBlobBeingRehydrated ...
	StorageErrorCodeBlobBeingRehydrated StorageErrorCodeType = "BlobBeingRehydrated"
	// StorageErrorCodeBlobNotArchived ...
	StorageErrorCodeBlobNotArchived StorageErrorCodeType = "BlobNotArchived"
	// StorageErrorCodeBlobNotFound ...
	StorageErrorCodeBlobNotFound StorageErrorCodeType = "BlobNotFound"
	// StorageErrorCodeBlobOverwritten ...
	StorageErrorCodeBlobOverwritten StorageErrorCodeType = "BlobOverwritten"
	// StorageErrorCodeBlobTierInadequateForContentLength ...
	StorageErrorCodeBlobTierInadequateForContentLength StorageErrorCodeType = "BlobTierInadequateForContentLength"
	// StorageErrorCodeBlockCountExceedsLimit ...
	StorageErrorCodeBlockCountExceedsLimit StorageErrorCodeType = "BlockCountExceedsLimit"
	// StorageErrorCodeBlockListTooLong ...
	StorageErrorCodeBlockListTooLong StorageErrorCodeType = "BlockListTooLong"
	// StorageErrorCodeCannotChangeToLowerTier ...
	StorageErrorCodeCannotChangeToLowerTier StorageErrorCodeType = "CannotChangeToLowerTier"
	// StorageErrorCodeCannotVerifyCopySource ...
	StorageErrorCodeCannotVerifyCopySource StorageErrorCodeType = "CannotVerifyCopySource"
	// StorageErrorCodeConditionHeadersNotSupported ...
	StorageErrorCodeConditionHeadersNotSupported StorageErrorCodeType = "ConditionHeadersNotSupported"
	// StorageErrorCodeConditionNotMet ...
	StorageErrorCodeConditionNotMet StorageErrorCodeType = "ConditionNotMet"
	// StorageErrorCodeContainerAlreadyExists ...
	StorageErrorCodeContainerAlreadyExists StorageErrorCodeType = "ContainerAlreadyExists"
	// StorageErrorCodeContainerBeingDeleted ...
	StorageErrorCodeContainerBeingDeleted StorageErrorCodeType = "ContainerBeingDeleted"
	// StorageErrorCodeContainerDisabled ...
	StorageErrorCodeContainerDisabled StorageErrorCodeType = "ContainerDisabled"
	// StorageErrorCodeContainerNotFound ...
	StorageErrorCodeContainerNotFound StorageErrorCodeType = "ContainerNotFound"
	// StorageErrorCodeContentLengthLargerThanTierLimit ...
	StorageErrorCodeContentLengthLargerThanTierLimit StorageErrorCodeType = "ContentLengthLargerThanTierLimit"
	// StorageErrorCodeCopyAcrossAccountsNotSupported ...
	StorageErrorCodeCopyAcrossAccountsNotSupported StorageErrorCodeType = "CopyAcrossAccountsNotSupported"
	// StorageErrorCodeCopyIDMismatch ...
	StorageErrorCodeCopyIDMismatch StorageErrorCodeType = "CopyIdMismatch"
	// StorageErrorCodeEmptyMetadataKey ...
	StorageErrorCodeEmptyMetadataKey StorageErrorCodeType = "EmptyMetadataKey"
	// StorageErrorCodeFeatureVersionMismatch ...
	StorageErrorCodeFeatureVersionMismatch StorageErrorCodeType = "FeatureVersionMismatch"
	// StorageErrorCodeIncrementalCopyBlobMismatch ...
	StorageErrorCodeIncrementalCopyBlobMismatch StorageErrorCodeType = "IncrementalCopyBlobMismatch"
	// StorageErrorCodeIncrementalCopyOfEralierVersionSnapshotNotAllowed ...
	StorageErrorCodeIncrementalCopyOfEralierVersionSnapshotNotAllowed StorageErrorCodeType = "IncrementalCopyOfEralierVersionSnapshotNotAllowed"
	// StorageErrorCodeIncrementalCopySourceMustBeSnapshot ...
	StorageErrorCodeIncrementalCopySourceMustBeSnapshot StorageErrorCodeType = "IncrementalCopySourceMustBeSnapshot"
	// StorageErrorCodeInfiniteLeaseDurationRequired ...
	StorageErrorCodeInfiniteLeaseDurationRequired StorageErrorCodeType = "InfiniteLeaseDurationRequired"
	// StorageErrorCodeInsufficientAccountPermissions ...
	StorageErrorCodeInsufficientAccountPermissions StorageErrorCodeType = "InsufficientAccountPermissions"
	// StorageErrorCodeInternalError ...
	StorageErrorCodeInternalError StorageErrorCodeType = "InternalError"
	// StorageErrorCodeInvalidAuthenticationInfo ...
	StorageErrorCodeInvalidAuthenticationInfo StorageErrorCodeType = "InvalidAuthenticationInfo"
	// StorageErrorCodeInvalidBlobOrBlock ...
	StorageErrorCodeInvalidBlobOrBlock StorageErrorCodeType = "InvalidBlobOrBlock"
	// StorageErrorCodeInvalidBlobTier ...
	StorageErrorCodeInvalidBlobTier StorageErrorCodeType = "InvalidBlobTier"
	// StorageErrorCodeInvalidBlobType ...
	StorageErrorCodeInvalidBlobType StorageErrorCodeType = "InvalidBlobType"
	// StorageErrorCodeInvalidBlockID ...
	StorageErrorCodeInvalidBlockID StorageErrorCodeType = "InvalidBlockId"
	// StorageErrorCodeInvalidBlockList ...
	StorageErrorCodeInvalidBlockList StorageErrorCodeType = "InvalidBlockList"
	// StorageErrorCodeInvalidHeaderValue ...
	StorageErrorCodeInvalidHeaderValue StorageErrorCodeType = "InvalidHeaderValue"
	// StorageErrorCodeInvalidHTTPVerb ...
	StorageErrorCodeInvalidHTTPVerb StorageErrorCodeType = "InvalidHttpVerb"
	// StorageErrorCodeInvalidInput ...
	StorageErrorCodeInvalidInput StorageErrorCodeType = "InvalidInput"
	// StorageErrorCodeInvalidMd5 ...
	StorageErrorCodeInvalidMd5 StorageErrorCodeType = "InvalidMd5"
	// StorageErrorCodeInvalidMetadata ...
	StorageErrorCodeInvalidMetadata StorageErrorCodeType = "InvalidMetadata"
	// StorageErrorCodeInvalidOperation ...
	StorageErrorCodeInvalidOperation StorageErrorCodeType = "InvalidOperation"
	// StorageErrorCodeInvalidPageRange ...
	StorageErrorCodeInvalidPageRange StorageErrorCodeType = "InvalidPageRange"
	// StorageErrorCodeInvalidQueryParameterValue ...
	StorageErrorCodeInvalidQueryParameterValue StorageErrorCodeType = "InvalidQueryParameterValue"
	// StorageErrorCodeInvalidRange ...
	StorageErrorCodeInvalidRange StorageErrorCodeType = "InvalidRange"
	// StorageErrorCodeInvalidResourceName ...
	StorageErrorCodeInvalidResourceName StorageErrorCodeType = "InvalidResourceName"
	// StorageErrorCodeInvalidSourceBlobType ...
	StorageErrorCodeInvalidSourceBlobType StorageErrorCodeType = "InvalidSourceBlobType"
	// StorageErrorCodeInvalidSourceBlobURL ...
	StorageErrorCodeInvalidSourceBlobURL StorageErrorCodeType = "InvalidSourceBlobUrl"
	// StorageErrorCodeInvalidURI ...
	StorageErrorCodeInvalidURI StorageErrorCodeType = "InvalidUri"
	// StorageErrorCodeInvalidVersionForPageBlobOperation ...
	StorageErrorCodeInvalidVersionForPageBlobOperation StorageErrorCodeType = "InvalidVersionForPageBlobOperation"
	// StorageErrorCodeInvalidXMLDocument ...
	StorageErrorCodeInvalidXMLDocument StorageErrorCodeType = "InvalidXmlDocument"
	// StorageErrorCodeInvalidXMLNodeValue ...
	StorageErrorCodeInvalidXMLNodeValue StorageErrorCodeType = "InvalidXmlNodeValue"
	// StorageErrorCodeLeaseAlreadyBroken ...
	StorageErrorCodeLeaseAlreadyBroken StorageErrorCodeType = "LeaseAlreadyBroken"
	// StorageErrorCodeLeaseAlreadyPresent ...
	StorageErrorCodeLeaseAlreadyPresent StorageErrorCodeType = "LeaseAlreadyPresent"
	// StorageErrorCodeLeaseIDMismatchWithBlobOperation ...
	StorageErrorCodeLeaseIDMismatchWithBlobOperation StorageErrorCodeType = "LeaseIdMismatchWithBlobOperation"
	// StorageErrorCodeLeaseIDMismatchWithContainerOperation ...
	StorageErrorCodeLeaseIDMismatchWithContainerOperation StorageErrorCodeType = "LeaseIdMismatchWithContainerOperation"
	// StorageErrorCodeLeaseIDMismatchWithLeaseOperation ...
	StorageErrorCodeLeaseIDMismatchWithLeaseOperation StorageErrorCodeType = "LeaseIdMismatchWithLeaseOperation"
	// StorageErrorCodeLeaseIDMissing ...
	StorageErrorCodeLeaseIDMissing StorageErrorCodeType = "LeaseIdMissing"
	// StorageErrorCodeLeaseIsBreakingAndCannotBeAcquired ...
	StorageErrorCodeLeaseIsBreakingAndCannotBeAcquired StorageErrorCodeType = "LeaseIsBreakingAndCannotBeAcquired"
	// StorageErrorCodeLeaseIsBreakingAndCannotBeChanged ...
	StorageErrorCodeLeaseIsBreakingAndCannotBeChanged StorageErrorCodeType = "LeaseIsBreakingAndCannotBeChanged"
	// StorageErrorCodeLeaseIsBrokenAndCannotBeRenewed ...
	StorageErrorCodeLeaseIsBrokenAndCannotBeRenewed StorageErrorCodeType = "LeaseIsBrokenAndCannotBeRenewed"
	// StorageErrorCodeLeaseLost ...
	StorageErrorCodeLeaseLost StorageErrorCodeType = "LeaseLost"
	// StorageErrorCodeLeaseNotPresentWithBlobOperation ...
	StorageErrorCodeLeaseNotPresentWithBlobOperation StorageErrorCodeType = "LeaseNotPresentWithBlobOperation"
	// StorageErrorCodeLeaseNotPresentWithContainerOperation ...
	StorageErrorCodeLeaseNotPresentWithContainerOperation StorageErrorCodeType = "LeaseNotPresentWithContainerOperation"
	// StorageErrorCodeLeaseNotPresentWithLeaseOperation ...
	StorageErrorCodeLeaseNotPresentWithLeaseOperation StorageErrorCodeType = "LeaseNotPresentWithLeaseOperation"
	// StorageErrorCodeMaxBlobSizeConditionNotMet ...
	StorageErrorCodeMaxBlobSizeConditionNotMet StorageErrorCodeType = "MaxBlobSizeConditionNotMet"
	// StorageErrorCodeMd5Mismatch ...
	StorageErrorCodeMd5Mismatch StorageErrorCodeType = "Md5Mismatch"
	// StorageErrorCodeMetadataTooLarge ...
	StorageErrorCodeMetadataTooLarge StorageErrorCodeType = "MetadataTooLarge"
	// StorageErrorCodeMissingContentLengthHeader ...
	StorageErrorCodeMissingContentLengthHeader StorageErrorCodeType = "MissingContentLengthHeader"
	// StorageErrorCodeMissingRequiredHeader ...
	StorageErrorCodeMissingRequiredHeader StorageErrorCodeType = "MissingRequiredHeader"
	// StorageErrorCodeMissingRequiredQueryParameter ...
	StorageErrorCodeMissingRequiredQueryParameter StorageErrorCodeType = "MissingRequiredQueryParameter"
	// StorageErrorCodeMissingRequiredXMLNode ...
	StorageErrorCodeMissingRequiredXMLNode StorageErrorCodeType = "MissingRequiredXmlNode"
	// StorageErrorCodeMultipleConditionHeadersNotSupported ...
	StorageErrorCodeMultipleConditionHeadersNotSupported StorageErrorCodeType = "MultipleConditionHeadersNotSupported"
	// StorageErrorCodeNoAuthenticationInformation ...
	StorageErrorCodeNoAuthenticationInformation StorageErrorCodeType = "NoAuthenticationInformation"
	// StorageErrorCodeNone represents an empty StorageErrorCodeType.
	StorageErrorCodeNone StorageErrorCodeType = ""
	// StorageErrorCodeNoPendingCopyOperation ...
	StorageErrorCodeNoPendingCopyOperation StorageErrorCodeType = "NoPendingCopyOperation"
	// StorageErrorCodeOperationNotAllowedOnIncrementalCopyBlob ...
	StorageErrorCodeOperationNotAllowedOnIncrementalCopyBlob StorageErrorCodeType = "OperationNotAllowedOnIncrementalCopyBlob"
	// StorageErrorCodeOperationTimedOut ...
	StorageErrorCodeOperationTimedOut StorageErrorCodeType = "OperationTimedOut"
	// StorageErrorCodeOutOfRangeInput ...
	StorageErrorCodeOutOfRangeInput StorageErrorCodeType = "OutOfRangeInput"
	// StorageErrorCodeOutOfRangeQueryParameterValue ...
	StorageErrorCodeOutOfRangeQueryParameterValue StorageErrorCodeType = "OutOfRangeQueryParameterValue"
	// StorageErrorCodePendingCopyOperation ...
	StorageErrorCodePendingCopyOperation StorageErrorCodeType = "PendingCopyOperation"
	// StorageErrorCodePreviousSnapshotCannotBeNewer ...
	StorageErrorCodePreviousSnapshotCannotBeNewer StorageErrorCodeType = "PreviousSnapshotCannotBeNewer"
	// StorageErrorCodePreviousSnapshotNotFound ...
	StorageErrorCodePreviousSnapshotNotFound StorageErrorCodeType = "PreviousSnapshotNotFound"
	// StorageErrorCodePreviousSnapshotOperationNotSupported ...
	StorageErrorCodePreviousSnapshotOperationNotSupported StorageErrorCodeType = "PreviousSnapshotOperationNotSupported"
	// StorageErrorCodeRequestBodyTooLarge ...
	StorageErrorCodeRequestBodyTooLarge StorageErrorCodeType = "RequestBodyTooLarge"
	// StorageErrorCodeRequestURLFailedToParse ...
	StorageErrorCodeRequestURLFailedToParse StorageErrorCodeType = "RequestUrlFailedToParse"
	// StorageErrorCodeResourceAlreadyExists ...
	StorageErrorCodeResourceAlreadyExists StorageErrorCodeType = "ResourceAlreadyExists"
	// StorageErrorCodeResourceNotFound ...
	StorageErrorCodeResourceNotFound StorageErrorCodeType = "ResourceNotFound"
	// StorageErrorCodeResourceTypeMismatch ...
	StorageErrorCodeResourceTypeMismatch StorageErrorCodeType = "ResourceTypeMismatch"
	// StorageErrorCodeSequenceNumberConditionNotMet ...
	StorageErrorCodeSequenceNumberConditionNotMet StorageErrorCodeType = "SequenceNumberConditionNotMet"
	// StorageErrorCodeSequenceNumberIncrementTooLarge ...
	StorageErrorCodeSequenceNumberIncrementTooLarge StorageErrorCodeType = "SequenceNumberIncrementTooLarge"
	// StorageErrorCodeServerBusy ...
	StorageErrorCodeServerBusy StorageErrorCodeType = "ServerBusy"
	// StorageErrorCodeSnaphotOperationRateExceeded ...
	StorageErrorCodeSnaphotOperationRateExceeded StorageErrorCodeType = "SnaphotOperationRateExceeded"
	// StorageErrorCodeSnapshotCountExceeded ...
	StorageErrorCodeSnapshotCountExceeded StorageErrorCodeType = "SnapshotCountExceeded"
	// StorageErrorCodeSnapshotsPresent ...
	StorageErrorCodeSnapshotsPresent StorageErrorCodeType = "SnapshotsPresent"
	// StorageErrorCodeSourceConditionNotMet ...
	StorageErrorCodeSourceConditionNotMet StorageErrorCodeType = "SourceConditionNotMet"
	// StorageErrorCodeSystemInUse ...
	StorageErrorCodeSystemInUse StorageErrorCodeType = "SystemInUse"
	// StorageErrorCodeTargetConditionNotMet ...
	StorageErrorCodeTargetConditionNotMet StorageErrorCodeType = "TargetConditionNotMet"
	// StorageErrorCodeUnauthorizedBlobOverwrite ...
	StorageErrorCodeUnauthorizedBlobOverwrite StorageErrorCodeType = "UnauthorizedBlobOverwrite"
	// StorageErrorCodeUnsupportedHeader ...
	StorageErrorCodeUnsupportedHeader StorageErrorCodeType = "UnsupportedHeader"
	// StorageErrorCodeUnsupportedHTTPVerb ...
	StorageErrorCodeUnsupportedHTTPVerb StorageErrorCodeType = "UnsupportedHttpVerb"
	// StorageErrorCodeUnsupportedQueryParameter ...
	StorageErrorCodeUnsupportedQueryParameter StorageErrorCodeType = "UnsupportedQueryParameter"
	// StorageErrorCodeUnsupportedXMLNode ...
	StorageErrorCodeUnsupportedXMLNode StorageErrorCodeType = "UnsupportedXmlNode"
)

// PossibleStorageErrorCodeTypeValues returns an array of possible values for the StorageErrorCodeType const type.
func PossibleStorageErrorCodeTypeValues() []StorageErrorCodeType {
	return []StorageErrorCodeType{StorageErrorCodeAccountAlreadyExists, StorageErrorCodeAccountBeingCreated, StorageErrorCodeAccountIsDisabled, StorageErrorCodeAppendPositionConditionNotMet, StorageErrorCodeAuthenticationFailed, StorageErrorCodeAuthorizationFailure, StorageErrorCodeAuthorizationPermissionMismatch, StorageErrorCodeAuthorizationProtocolMismatch, StorageErrorCodeAuthorizationResourceTypeMismatch, StorageErrorCodeAuthorizationServiceMismatch, StorageErrorCodeAuthorizationSourceIPMismatch, StorageErrorCodeBlobAlreadyExists, StorageErrorCodeBlobArchived, StorageErrorCodeBlobBeingRehydrated, StorageErrorCodeBlobNotArchived, StorageErrorCodeBlobNotFound, StorageErrorCodeBlobOverwritten, StorageErrorCodeBlobTierInadequateForContentLength, StorageErrorCodeBlockCountExceedsLimit, StorageErrorCodeBlockListTooLong, StorageErrorCodeCannotChangeToLowerTier, StorageErrorCodeCannotVerifyCopySource, StorageErrorCodeConditionHeadersNotSupported, StorageErrorCodeConditionNotMet, StorageErrorCodeContainerAlreadyExists, StorageErrorCodeContainerBeingDeleted, StorageErrorCodeContainerDisabled, StorageErrorCodeContainerNotFound, StorageErrorCodeContentLengthLargerThanTierLimit, StorageErrorCodeCopyAcrossAccountsNotSupported, StorageErrorCodeCopyIDMismatch, StorageErrorCodeEmptyMetadataKey, StorageErrorCodeFeatureVersionMismatch, StorageErrorCodeIncrementalCopyBlobMismatch, StorageErrorCodeIncrementalCopyOfEralierVersionSnapshotNotAllowed, StorageErrorCodeIncrementalCopySourceMustBeSnapshot, StorageErrorCodeInfiniteLeaseDurationRequired, StorageErrorCodeInsufficientAccountPermissions, StorageErrorCodeInternalError, StorageErrorCodeInvalidAuthenticationInfo, StorageErrorCodeInvalidBlobOrBlock, StorageErrorCodeInvalidBlobTier, StorageErrorCodeInvalidBlobType, StorageErrorCodeInvalidBlockID, StorageErrorCodeInvalidBlockList, StorageErrorCodeInvalidHeaderValue, StorageErrorCodeInvalidHTTPVerb, StorageErrorCodeInvalidInput, StorageErrorCodeInvalidMd5, StorageErrorCodeInvalidMetadata, StorageErrorCodeInvalidOperation, StorageErrorCodeInvalidPageRange, StorageErrorCodeInvalidQueryParameterValue, StorageErrorCodeInvalidRange, StorageErrorCodeInvalidResourceName, StorageErrorCodeInvalidSourceBlobType, StorageErrorCodeInvalidSourceBlobURL, StorageErrorCodeInvalidURI, StorageErrorCodeInvalidVersionForPageBlobOperation, StorageErrorCodeInvalidXMLDocument, StorageErrorCodeInvalidXMLNodeValue, StorageErrorCodeLeaseAlreadyBroken, StorageErrorCodeLeaseAlreadyPresent, StorageErrorCodeLeaseIDMismatchWithBlobOperation, StorageErrorCodeLeaseIDMismatchWithContainerOperation, StorageErrorCodeLeaseIDMismatchWithLeaseOperation, StorageErrorCodeLeaseIDMissing, StorageErrorCodeLeaseIsBreakingAndCannotBeAcquired, StorageErrorCodeLeaseIsBreakingAndCannotBeChanged, StorageErrorCodeLeaseIsBrokenAndCannotBeRenewed, StorageErrorCodeLeaseLost, StorageErrorCodeLeaseNotPresentWithBlobOperation, StorageErrorCodeLeaseNotPresentWithContainerOperation, StorageErrorCodeLeaseNotPresentWithLeaseOperation, StorageErrorCodeMaxBlobSizeConditionNotMet, StorageErrorCodeMd5Mismatch, StorageErrorCodeMetadataTooLarge, StorageErrorCodeMissingContentLengthHeader, StorageErrorCodeMissingRequiredHeader, StorageErrorCodeMissingRequiredQueryParameter, StorageErrorCodeMissingRequiredXMLNode, StorageErrorCodeMultipleConditionHeadersNotSupported, StorageErrorCodeNoAuthenticationInformation, StorageErrorCodeNone, StorageErrorCodeNoPendingCopyOperation, StorageErrorCodeOperationNotAllowedOnIncrementalCopyBlob, StorageErrorCodeOperationTimedOut, StorageErrorCodeOutOfRangeInput, StorageErrorCodeOutOfRangeQueryParameterValue, StorageErrorCodePendingCopyOperation, StorageErrorCodePreviousSnapshotCannotBeNewer, StorageErrorCodePreviousSnapshotNotFound, StorageErrorCodePreviousSnapshotOperationNotSupported, StorageErrorCodeRequestBodyTooLarge, StorageErrorCodeRequestURLFailedToParse, StorageErrorCodeResourceAlreadyExists, StorageErrorCodeResourceNotFound, StorageErrorCodeResourceTypeMismatch, StorageErrorCodeSequenceNumberConditionNotMet, StorageErrorCodeSequenceNumberIncrementTooLarge, StorageErrorCodeServerBusy, StorageErrorCodeSnaphotOperationRateExceeded, StorageErrorCodeSnapshotCountExceeded, StorageErrorCodeSnapshotsPresent, StorageErrorCodeSourceConditionNotMet, StorageErrorCodeSystemInUse, StorageErrorCodeTargetConditionNotMet, StorageErrorCodeUnauthorizedBlobOverwrite, StorageErrorCodeUnsupportedHeader, StorageErrorCodeUnsupportedHTTPVerb, StorageErrorCodeUnsupportedQueryParameter, StorageErrorCodeUnsupportedXMLNode}
}

// SyncCopyStatusType enumerates the values for sync copy status type.
type SyncCopyStatusType string

const (
	// SyncCopyStatusNone represents an empty SyncCopyStatusType.
	SyncCopyStatusNone SyncCopyStatusType = ""
	// SyncCopyStatusSuccess ...
	SyncCopyStatusSuccess SyncCopyStatusType = "success"
)

// PossibleSyncCopyStatusTypeValues returns an array of possible values for the SyncCopyStatusType const type.
func PossibleSyncCopyStatusTypeValues() []SyncCopyStatusType {
	return []SyncCopyStatusType{SyncCopyStatusNone, SyncCopyStatusSuccess}
}

// AccessPolicy - An Access policy
type AccessPolicy struct {
	// Start - the date-time the policy is active
	Start *time.Time `xml:"Start"`
	// Expiry - the date-time the policy expires
	Expiry *time.Time `xml:"Expiry"`
	// Permission - the permissions for the acl policy
	Permission *string `xml:"Permission"`
}

// MarshalXML implements the xml.Marshaler interface for AccessPolicy.
func (ap AccessPolicy) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	ap2 := (*accessPolicy)(unsafe.Pointer(&ap))
	return e.EncodeElement(*ap2, start)
}

// UnmarshalXML implements the xml.Unmarshaler interface for AccessPolicy.
func (ap *AccessPolicy) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	ap2 := (*accessPolicy)(unsafe.Pointer(ap))
	return d.DecodeElement(ap2, &start)
}

// AppendBlobAppendBlockFromURLResponse ...
type AppendBlobAppendBlockFromURLResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (ababfur AppendBlobAppendBlockFromURLResponse) Response() *http.Response {
	return ababfur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (ababfur AppendBlobAppendBlockFromURLResponse) StatusCode() int {
	return ababfur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (ababfur AppendBlobAppendBlockFromURLResponse) Status() string {
	return ababfur.rawResponse.Status
}

// BlobAppendOffset returns the value for header x-ms-blob-append-offset.
func (ababfur AppendBlobAppendBlockFromURLResponse) BlobAppendOffset() string {
	return ababfur.rawResponse.Header.Get("x-ms-blob-append-offset")
}

// BlobCommittedBlockCount returns the value for header x-ms-blob-committed-block-count.
func (ababfur AppendBlobAppendBlockFromURLResponse) BlobCommittedBlockCount() int32 {
	s := ababfur.rawResponse.Header.Get("x-ms-blob-committed-block-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// ContentMD5 returns the value for header Content-MD5.
func (ababfur AppendBlobAppendBlockFromURLResponse) ContentMD5() []byte {
	s := ababfur.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (ababfur AppendBlobAppendBlockFromURLResponse) Date() time.Time {
	s := ababfur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (ababfur AppendBlobAppendBlockFromURLResponse) EncryptionKeySha256() string {
	return ababfur.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (ababfur AppendBlobAppendBlockFromURLResponse) EncryptionScope() string {
	return ababfur.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (ababfur AppendBlobAppendBlockFromURLResponse) ErrorCode() string {
	return ababfur.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (ababfur AppendBlobAppendBlockFromURLResponse) ETag() ETag {
	return ETag(ababfur.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (ababfur AppendBlobAppendBlockFromURLResponse) IsServerEncrypted() string {
	return ababfur.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (ababfur AppendBlobAppendBlockFromURLResponse) LastModified() time.Time {
	s := ababfur.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (ababfur AppendBlobAppendBlockFromURLResponse) RequestID() string {
	return ababfur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (ababfur AppendBlobAppendBlockFromURLResponse) Version() string {
	return ababfur.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (ababfur AppendBlobAppendBlockFromURLResponse) XMsContentCrc64() []byte {
	s := ababfur.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// AppendBlobAppendBlockResponse ...
type AppendBlobAppendBlockResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (ababr AppendBlobAppendBlockResponse) Response() *http.Response {
	return ababr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (ababr AppendBlobAppendBlockResponse) StatusCode() int {
	return ababr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (ababr AppendBlobAppendBlockResponse) Status() string {
	return ababr.rawResponse.Status
}

// BlobAppendOffset returns the value for header x-ms-blob-append-offset.
func (ababr AppendBlobAppendBlockResponse) BlobAppendOffset() string {
	return ababr.rawResponse.Header.Get("x-ms-blob-append-offset")
}

// BlobCommittedBlockCount returns the value for header x-ms-blob-committed-block-count.
func (ababr AppendBlobAppendBlockResponse) BlobCommittedBlockCount() int32 {
	s := ababr.rawResponse.Header.Get("x-ms-blob-committed-block-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (ababr AppendBlobAppendBlockResponse) ClientRequestID() string {
	return ababr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (ababr AppendBlobAppendBlockResponse) ContentMD5() []byte {
	s := ababr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (ababr AppendBlobAppendBlockResponse) Date() time.Time {
	s := ababr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (ababr AppendBlobAppendBlockResponse) EncryptionKeySha256() string {
	return ababr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (ababr AppendBlobAppendBlockResponse) EncryptionScope() string {
	return ababr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (ababr AppendBlobAppendBlockResponse) ErrorCode() string {
	return ababr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (ababr AppendBlobAppendBlockResponse) ETag() ETag {
	return ETag(ababr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (ababr AppendBlobAppendBlockResponse) IsServerEncrypted() string {
	return ababr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (ababr AppendBlobAppendBlockResponse) LastModified() time.Time {
	s := ababr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (ababr AppendBlobAppendBlockResponse) RequestID() string {
	return ababr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (ababr AppendBlobAppendBlockResponse) Version() string {
	return ababr.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (ababr AppendBlobAppendBlockResponse) XMsContentCrc64() []byte {
	s := ababr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// AppendBlobCreateResponse ...
type AppendBlobCreateResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (abcr AppendBlobCreateResponse) Response() *http.Response {
	return abcr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (abcr AppendBlobCreateResponse) StatusCode() int {
	return abcr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (abcr AppendBlobCreateResponse) Status() string {
	return abcr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (abcr AppendBlobCreateResponse) ClientRequestID() string {
	return abcr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (abcr AppendBlobCreateResponse) ContentMD5() []byte {
	s := abcr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (abcr AppendBlobCreateResponse) Date() time.Time {
	s := abcr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (abcr AppendBlobCreateResponse) EncryptionKeySha256() string {
	return abcr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (abcr AppendBlobCreateResponse) EncryptionScope() string {
	return abcr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (abcr AppendBlobCreateResponse) ErrorCode() string {
	return abcr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (abcr AppendBlobCreateResponse) ETag() ETag {
	return ETag(abcr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (abcr AppendBlobCreateResponse) IsServerEncrypted() string {
	return abcr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (abcr AppendBlobCreateResponse) LastModified() time.Time {
	s := abcr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (abcr AppendBlobCreateResponse) RequestID() string {
	return abcr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (abcr AppendBlobCreateResponse) Version() string {
	return abcr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (abcr AppendBlobCreateResponse) VersionID() string {
	return abcr.rawResponse.Header.Get("x-ms-version-id")
}

// AppendBlobSealResponse ...
type AppendBlobSealResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (absr AppendBlobSealResponse) Response() *http.Response {
	return absr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (absr AppendBlobSealResponse) StatusCode() int {
	return absr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (absr AppendBlobSealResponse) Status() string {
	return absr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (absr AppendBlobSealResponse) ClientRequestID() string {
	return absr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (absr AppendBlobSealResponse) Date() time.Time {
	s := absr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (absr AppendBlobSealResponse) ErrorCode() string {
	return absr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (absr AppendBlobSealResponse) ETag() ETag {
	return ETag(absr.rawResponse.Header.Get("ETag"))
}

// IsSealed returns the value for header x-ms-blob-sealed.
func (absr AppendBlobSealResponse) IsSealed() string {
	return absr.rawResponse.Header.Get("x-ms-blob-sealed")
}

// LastModified returns the value for header Last-Modified.
func (absr AppendBlobSealResponse) LastModified() time.Time {
	s := absr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (absr AppendBlobSealResponse) RequestID() string {
	return absr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (absr AppendBlobSealResponse) Version() string {
	return absr.rawResponse.Header.Get("x-ms-version")
}

// BlobAbortCopyFromURLResponse ...
type BlobAbortCopyFromURLResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bacfur BlobAbortCopyFromURLResponse) Response() *http.Response {
	return bacfur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bacfur BlobAbortCopyFromURLResponse) StatusCode() int {
	return bacfur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bacfur BlobAbortCopyFromURLResponse) Status() string {
	return bacfur.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bacfur BlobAbortCopyFromURLResponse) ClientRequestID() string {
	return bacfur.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bacfur BlobAbortCopyFromURLResponse) Date() time.Time {
	s := bacfur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bacfur BlobAbortCopyFromURLResponse) ErrorCode() string {
	return bacfur.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bacfur BlobAbortCopyFromURLResponse) RequestID() string {
	return bacfur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bacfur BlobAbortCopyFromURLResponse) Version() string {
	return bacfur.rawResponse.Header.Get("x-ms-version")
}

// BlobAcquireLeaseResponse ...
type BlobAcquireLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (balr BlobAcquireLeaseResponse) Response() *http.Response {
	return balr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (balr BlobAcquireLeaseResponse) StatusCode() int {
	return balr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (balr BlobAcquireLeaseResponse) Status() string {
	return balr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (balr BlobAcquireLeaseResponse) ClientRequestID() string {
	return balr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (balr BlobAcquireLeaseResponse) Date() time.Time {
	s := balr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (balr BlobAcquireLeaseResponse) ErrorCode() string {
	return balr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (balr BlobAcquireLeaseResponse) ETag() ETag {
	return ETag(balr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (balr BlobAcquireLeaseResponse) LastModified() time.Time {
	s := balr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseID returns the value for header x-ms-lease-id.
func (balr BlobAcquireLeaseResponse) LeaseID() string {
	return balr.rawResponse.Header.Get("x-ms-lease-id")
}

// RequestID returns the value for header x-ms-request-id.
func (balr BlobAcquireLeaseResponse) RequestID() string {
	return balr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (balr BlobAcquireLeaseResponse) Version() string {
	return balr.rawResponse.Header.Get("x-ms-version")
}

// BlobBreakLeaseResponse ...
type BlobBreakLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bblr BlobBreakLeaseResponse) Response() *http.Response {
	return bblr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bblr BlobBreakLeaseResponse) StatusCode() int {
	return bblr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bblr BlobBreakLeaseResponse) Status() string {
	return bblr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bblr BlobBreakLeaseResponse) ClientRequestID() string {
	return bblr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bblr BlobBreakLeaseResponse) Date() time.Time {
	s := bblr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bblr BlobBreakLeaseResponse) ErrorCode() string {
	return bblr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bblr BlobBreakLeaseResponse) ETag() ETag {
	return ETag(bblr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bblr BlobBreakLeaseResponse) LastModified() time.Time {
	s := bblr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseTime returns the value for header x-ms-lease-time.
func (bblr BlobBreakLeaseResponse) LeaseTime() int32 {
	s := bblr.rawResponse.Header.Get("x-ms-lease-time")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// RequestID returns the value for header x-ms-request-id.
func (bblr BlobBreakLeaseResponse) RequestID() string {
	return bblr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bblr BlobBreakLeaseResponse) Version() string {
	return bblr.rawResponse.Header.Get("x-ms-version")
}

// BlobChangeLeaseResponse ...
type BlobChangeLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bclr BlobChangeLeaseResponse) Response() *http.Response {
	return bclr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bclr BlobChangeLeaseResponse) StatusCode() int {
	return bclr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bclr BlobChangeLeaseResponse) Status() string {
	return bclr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bclr BlobChangeLeaseResponse) ClientRequestID() string {
	return bclr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bclr BlobChangeLeaseResponse) Date() time.Time {
	s := bclr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bclr BlobChangeLeaseResponse) ErrorCode() string {
	return bclr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bclr BlobChangeLeaseResponse) ETag() ETag {
	return ETag(bclr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bclr BlobChangeLeaseResponse) LastModified() time.Time {
	s := bclr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseID returns the value for header x-ms-lease-id.
func (bclr BlobChangeLeaseResponse) LeaseID() string {
	return bclr.rawResponse.Header.Get("x-ms-lease-id")
}

// RequestID returns the value for header x-ms-request-id.
func (bclr BlobChangeLeaseResponse) RequestID() string {
	return bclr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bclr BlobChangeLeaseResponse) Version() string {
	return bclr.rawResponse.Header.Get("x-ms-version")
}

// BlobCopyFromURLResponse ...
type BlobCopyFromURLResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bcfur BlobCopyFromURLResponse) Response() *http.Response {
	return bcfur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bcfur BlobCopyFromURLResponse) StatusCode() int {
	return bcfur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bcfur BlobCopyFromURLResponse) Status() string {
	return bcfur.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bcfur BlobCopyFromURLResponse) ClientRequestID() string {
	return bcfur.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (bcfur BlobCopyFromURLResponse) ContentMD5() []byte {
	s := bcfur.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// CopyID returns the value for header x-ms-copy-id.
func (bcfur BlobCopyFromURLResponse) CopyID() string {
	return bcfur.rawResponse.Header.Get("x-ms-copy-id")
}

// CopyStatus returns the value for header x-ms-copy-status.
func (bcfur BlobCopyFromURLResponse) CopyStatus() SyncCopyStatusType {
	return SyncCopyStatusType(bcfur.rawResponse.Header.Get("x-ms-copy-status"))
}

// Date returns the value for header Date.
func (bcfur BlobCopyFromURLResponse) Date() time.Time {
	s := bcfur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bcfur BlobCopyFromURLResponse) ErrorCode() string {
	return bcfur.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bcfur BlobCopyFromURLResponse) ETag() ETag {
	return ETag(bcfur.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bcfur BlobCopyFromURLResponse) LastModified() time.Time {
	s := bcfur.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bcfur BlobCopyFromURLResponse) RequestID() string {
	return bcfur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bcfur BlobCopyFromURLResponse) Version() string {
	return bcfur.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bcfur BlobCopyFromURLResponse) VersionID() string {
	return bcfur.rawResponse.Header.Get("x-ms-version-id")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (bcfur BlobCopyFromURLResponse) XMsContentCrc64() []byte {
	s := bcfur.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// BlobCreateSnapshotResponse ...
type BlobCreateSnapshotResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bcsr BlobCreateSnapshotResponse) Response() *http.Response {
	return bcsr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bcsr BlobCreateSnapshotResponse) StatusCode() int {
	return bcsr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bcsr BlobCreateSnapshotResponse) Status() string {
	return bcsr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bcsr BlobCreateSnapshotResponse) ClientRequestID() string {
	return bcsr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bcsr BlobCreateSnapshotResponse) Date() time.Time {
	s := bcsr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bcsr BlobCreateSnapshotResponse) ErrorCode() string {
	return bcsr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bcsr BlobCreateSnapshotResponse) ETag() ETag {
	return ETag(bcsr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (bcsr BlobCreateSnapshotResponse) IsServerEncrypted() string {
	return bcsr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (bcsr BlobCreateSnapshotResponse) LastModified() time.Time {
	s := bcsr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bcsr BlobCreateSnapshotResponse) RequestID() string {
	return bcsr.rawResponse.Header.Get("x-ms-request-id")
}

// Snapshot returns the value for header x-ms-snapshot.
func (bcsr BlobCreateSnapshotResponse) Snapshot() string {
	return bcsr.rawResponse.Header.Get("x-ms-snapshot")
}

// Version returns the value for header x-ms-version.
func (bcsr BlobCreateSnapshotResponse) Version() string {
	return bcsr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bcsr BlobCreateSnapshotResponse) VersionID() string {
	return bcsr.rawResponse.Header.Get("x-ms-version-id")
}

// BlobDeleteResponse ...
type BlobDeleteResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bdr BlobDeleteResponse) Response() *http.Response {
	return bdr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bdr BlobDeleteResponse) StatusCode() int {
	return bdr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bdr BlobDeleteResponse) Status() string {
	return bdr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bdr BlobDeleteResponse) ClientRequestID() string {
	return bdr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bdr BlobDeleteResponse) Date() time.Time {
	s := bdr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bdr BlobDeleteResponse) ErrorCode() string {
	return bdr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bdr BlobDeleteResponse) RequestID() string {
	return bdr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bdr BlobDeleteResponse) Version() string {
	return bdr.rawResponse.Header.Get("x-ms-version")
}

// BlobFlatListSegment ...
type BlobFlatListSegment struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName   xml.Name           `xml:"Blobs"`
	BlobItems []BlobItemInternal `xml:"Blob"`
}

// BlobGetAccessControlResponse ...
type BlobGetAccessControlResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bgacr BlobGetAccessControlResponse) Response() *http.Response {
	return bgacr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bgacr BlobGetAccessControlResponse) StatusCode() int {
	return bgacr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bgacr BlobGetAccessControlResponse) Status() string {
	return bgacr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bgacr BlobGetAccessControlResponse) ClientRequestID() string {
	return bgacr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bgacr BlobGetAccessControlResponse) Date() time.Time {
	s := bgacr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (bgacr BlobGetAccessControlResponse) ETag() ETag {
	return ETag(bgacr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bgacr BlobGetAccessControlResponse) LastModified() time.Time {
	s := bgacr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bgacr BlobGetAccessControlResponse) RequestID() string {
	return bgacr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bgacr BlobGetAccessControlResponse) Version() string {
	return bgacr.rawResponse.Header.Get("x-ms-version")
}

// XMsACL returns the value for header x-ms-acl.
func (bgacr BlobGetAccessControlResponse) XMsACL() string {
	return bgacr.rawResponse.Header.Get("x-ms-acl")
}

// XMsGroup returns the value for header x-ms-group.
func (bgacr BlobGetAccessControlResponse) XMsGroup() string {
	return bgacr.rawResponse.Header.Get("x-ms-group")
}

// XMsOwner returns the value for header x-ms-owner.
func (bgacr BlobGetAccessControlResponse) XMsOwner() string {
	return bgacr.rawResponse.Header.Get("x-ms-owner")
}

// XMsPermissions returns the value for header x-ms-permissions.
func (bgacr BlobGetAccessControlResponse) XMsPermissions() string {
	return bgacr.rawResponse.Header.Get("x-ms-permissions")
}

// BlobGetAccountInfoResponse ...
type BlobGetAccountInfoResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bgair BlobGetAccountInfoResponse) Response() *http.Response {
	return bgair.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bgair BlobGetAccountInfoResponse) StatusCode() int {
	return bgair.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bgair BlobGetAccountInfoResponse) Status() string {
	return bgair.rawResponse.Status
}

// AccountKind returns the value for header x-ms-account-kind.
func (bgair BlobGetAccountInfoResponse) AccountKind() AccountKindType {
	return AccountKindType(bgair.rawResponse.Header.Get("x-ms-account-kind"))
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bgair BlobGetAccountInfoResponse) ClientRequestID() string {
	return bgair.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bgair BlobGetAccountInfoResponse) Date() time.Time {
	s := bgair.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bgair BlobGetAccountInfoResponse) ErrorCode() string {
	return bgair.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bgair BlobGetAccountInfoResponse) RequestID() string {
	return bgair.rawResponse.Header.Get("x-ms-request-id")
}

// SkuName returns the value for header x-ms-sku-name.
func (bgair BlobGetAccountInfoResponse) SkuName() SkuNameType {
	return SkuNameType(bgair.rawResponse.Header.Get("x-ms-sku-name"))
}

// Version returns the value for header x-ms-version.
func (bgair BlobGetAccountInfoResponse) Version() string {
	return bgair.rawResponse.Header.Get("x-ms-version")
}

// BlobGetPropertiesResponse ...
type BlobGetPropertiesResponse struct {
	rawResponse *http.Response
}

// NewMetadata returns user-defined key/value pairs.
func (bgpr BlobGetPropertiesResponse) NewMetadata() Metadata {
	md := Metadata{}
	for k, v := range bgpr.rawResponse.Header {
		if len(k) > mdPrefixLen {
			if prefix := k[0:mdPrefixLen]; strings.EqualFold(prefix, mdPrefix) {
				md[strings.ToLower(k[mdPrefixLen:])] = v[0]
			}
		}
	}
	return md
}

// Response returns the raw HTTP response object.
func (bgpr BlobGetPropertiesResponse) Response() *http.Response {
	return bgpr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bgpr BlobGetPropertiesResponse) StatusCode() int {
	return bgpr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bgpr BlobGetPropertiesResponse) Status() string {
	return bgpr.rawResponse.Status
}

// AcceptRanges returns the value for header Accept-Ranges.
func (bgpr BlobGetPropertiesResponse) AcceptRanges() string {
	return bgpr.rawResponse.Header.Get("Accept-Ranges")
}

// AccessTier returns the value for header x-ms-access-tier.
func (bgpr BlobGetPropertiesResponse) AccessTier() string {
	return bgpr.rawResponse.Header.Get("x-ms-access-tier")
}

// AccessTierChangeTime returns the value for header x-ms-access-tier-change-time.
func (bgpr BlobGetPropertiesResponse) AccessTierChangeTime() time.Time {
	s := bgpr.rawResponse.Header.Get("x-ms-access-tier-change-time")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// AccessTierInferred returns the value for header x-ms-access-tier-inferred.
func (bgpr BlobGetPropertiesResponse) AccessTierInferred() string {
	return bgpr.rawResponse.Header.Get("x-ms-access-tier-inferred")
}

// ArchiveStatus returns the value for header x-ms-archive-status.
func (bgpr BlobGetPropertiesResponse) ArchiveStatus() string {
	return bgpr.rawResponse.Header.Get("x-ms-archive-status")
}

// BlobCommittedBlockCount returns the value for header x-ms-blob-committed-block-count.
func (bgpr BlobGetPropertiesResponse) BlobCommittedBlockCount() int32 {
	s := bgpr.rawResponse.Header.Get("x-ms-blob-committed-block-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (bgpr BlobGetPropertiesResponse) BlobSequenceNumber() int64 {
	s := bgpr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// BlobType returns the value for header x-ms-blob-type.
func (bgpr BlobGetPropertiesResponse) BlobType() BlobType {
	return BlobType(bgpr.rawResponse.Header.Get("x-ms-blob-type"))
}

// CacheControl returns the value for header Cache-Control.
func (bgpr BlobGetPropertiesResponse) CacheControl() string {
	return bgpr.rawResponse.Header.Get("Cache-Control")
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bgpr BlobGetPropertiesResponse) ClientRequestID() string {
	return bgpr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentDisposition returns the value for header Content-Disposition.
func (bgpr BlobGetPropertiesResponse) ContentDisposition() string {
	return bgpr.rawResponse.Header.Get("Content-Disposition")
}

// ContentEncoding returns the value for header Content-Encoding.
func (bgpr BlobGetPropertiesResponse) ContentEncoding() string {
	return bgpr.rawResponse.Header.Get("Content-Encoding")
}

// ContentLanguage returns the value for header Content-Language.
func (bgpr BlobGetPropertiesResponse) ContentLanguage() string {
	return bgpr.rawResponse.Header.Get("Content-Language")
}

// ContentLength returns the value for header Content-Length.
func (bgpr BlobGetPropertiesResponse) ContentLength() int64 {
	s := bgpr.rawResponse.Header.Get("Content-Length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ContentMD5 returns the value for header Content-MD5.
func (bgpr BlobGetPropertiesResponse) ContentMD5() []byte {
	s := bgpr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// ContentType returns the value for header Content-Type.
func (bgpr BlobGetPropertiesResponse) ContentType() string {
	return bgpr.rawResponse.Header.Get("Content-Type")
}

// CopyCompletionTime returns the value for header x-ms-copy-completion-time.
func (bgpr BlobGetPropertiesResponse) CopyCompletionTime() time.Time {
	s := bgpr.rawResponse.Header.Get("x-ms-copy-completion-time")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// CopyID returns the value for header x-ms-copy-id.
func (bgpr BlobGetPropertiesResponse) CopyID() string {
	return bgpr.rawResponse.Header.Get("x-ms-copy-id")
}

// CopyProgress returns the value for header x-ms-copy-progress.
func (bgpr BlobGetPropertiesResponse) CopyProgress() string {
	return bgpr.rawResponse.Header.Get("x-ms-copy-progress")
}

// CopySource returns the value for header x-ms-copy-source.
func (bgpr BlobGetPropertiesResponse) CopySource() string {
	return bgpr.rawResponse.Header.Get("x-ms-copy-source")
}

// CopyStatus returns the value for header x-ms-copy-status.
func (bgpr BlobGetPropertiesResponse) CopyStatus() CopyStatusType {
	return CopyStatusType(bgpr.rawResponse.Header.Get("x-ms-copy-status"))
}

// CopyStatusDescription returns the value for header x-ms-copy-status-description.
func (bgpr BlobGetPropertiesResponse) CopyStatusDescription() string {
	return bgpr.rawResponse.Header.Get("x-ms-copy-status-description")
}

// CreationTime returns the value for header x-ms-creation-time.
func (bgpr BlobGetPropertiesResponse) CreationTime() time.Time {
	s := bgpr.rawResponse.Header.Get("x-ms-creation-time")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// Date returns the value for header Date.
func (bgpr BlobGetPropertiesResponse) Date() time.Time {
	s := bgpr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// DestinationSnapshot returns the value for header x-ms-copy-destination-snapshot.
func (bgpr BlobGetPropertiesResponse) DestinationSnapshot() string {
	return bgpr.rawResponse.Header.Get("x-ms-copy-destination-snapshot")
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (bgpr BlobGetPropertiesResponse) EncryptionKeySha256() string {
	return bgpr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (bgpr BlobGetPropertiesResponse) EncryptionScope() string {
	return bgpr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bgpr BlobGetPropertiesResponse) ErrorCode() string {
	return bgpr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bgpr BlobGetPropertiesResponse) ETag() ETag {
	return ETag(bgpr.rawResponse.Header.Get("ETag"))
}

// ExpiresOn returns the value for header x-ms-expiry-time.
func (bgpr BlobGetPropertiesResponse) ExpiresOn() time.Time {
	s := bgpr.rawResponse.Header.Get("x-ms-expiry-time")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// IsCurrentVersion returns the value for header x-ms-is-current-version.
func (bgpr BlobGetPropertiesResponse) IsCurrentVersion() string {
	return bgpr.rawResponse.Header.Get("x-ms-is-current-version")
}

// IsIncrementalCopy returns the value for header x-ms-incremental-copy.
func (bgpr BlobGetPropertiesResponse) IsIncrementalCopy() string {
	return bgpr.rawResponse.Header.Get("x-ms-incremental-copy")
}

// IsSealed returns the value for header x-ms-blob-sealed.
func (bgpr BlobGetPropertiesResponse) IsSealed() string {
	return bgpr.rawResponse.Header.Get("x-ms-blob-sealed")
}

// IsServerEncrypted returns the value for header x-ms-server-encrypted.
func (bgpr BlobGetPropertiesResponse) IsServerEncrypted() string {
	return bgpr.rawResponse.Header.Get("x-ms-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (bgpr BlobGetPropertiesResponse) LastModified() time.Time {
	s := bgpr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseDuration returns the value for header x-ms-lease-duration.
func (bgpr BlobGetPropertiesResponse) LeaseDuration() LeaseDurationType {
	return LeaseDurationType(bgpr.rawResponse.Header.Get("x-ms-lease-duration"))
}

// LeaseState returns the value for header x-ms-lease-state.
func (bgpr BlobGetPropertiesResponse) LeaseState() LeaseStateType {
	return LeaseStateType(bgpr.rawResponse.Header.Get("x-ms-lease-state"))
}

// LeaseStatus returns the value for header x-ms-lease-status.
func (bgpr BlobGetPropertiesResponse) LeaseStatus() LeaseStatusType {
	return LeaseStatusType(bgpr.rawResponse.Header.Get("x-ms-lease-status"))
}

// ObjectReplicationPolicyID returns the value for header x-ms-or-policy-id.
func (bgpr BlobGetPropertiesResponse) ObjectReplicationPolicyID() string {
	return bgpr.rawResponse.Header.Get("x-ms-or-policy-id")
}

// ObjectReplicationRules returns the value for header x-ms-or.
func (bgpr BlobGetPropertiesResponse) ObjectReplicationRules() string {
	return bgpr.rawResponse.Header.Get("x-ms-or")
}

// RehydratePriority returns the value for header x-ms-rehydrate-priority.
func (bgpr BlobGetPropertiesResponse) RehydratePriority() string {
	return bgpr.rawResponse.Header.Get("x-ms-rehydrate-priority")
}

// RequestID returns the value for header x-ms-request-id.
func (bgpr BlobGetPropertiesResponse) RequestID() string {
	return bgpr.rawResponse.Header.Get("x-ms-request-id")
}

// TagCount returns the value for header x-ms-tag-count.
func (bgpr BlobGetPropertiesResponse) TagCount() int64 {
	s := bgpr.rawResponse.Header.Get("x-ms-tag-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// Version returns the value for header x-ms-version.
func (bgpr BlobGetPropertiesResponse) Version() string {
	return bgpr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bgpr BlobGetPropertiesResponse) VersionID() string {
	return bgpr.rawResponse.Header.Get("x-ms-version-id")
}

// BlobHierarchyListSegment ...
type BlobHierarchyListSegment struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName      xml.Name           `xml:"Blobs"`
	BlobPrefixes []BlobPrefix       `xml:"BlobPrefix"`
	BlobItems    []BlobItemInternal `xml:"Blob"`
}

// BlobItemInternal - An Azure Storage blob
type BlobItemInternal struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName          xml.Name       `xml:"Blob"`
	Name             string         `xml:"Name"`
	Deleted          bool           `xml:"Deleted"`
	Snapshot         string         `xml:"Snapshot"`
	VersionID        *string        `xml:"VersionId"`
	IsCurrentVersion *bool          `xml:"IsCurrentVersion"`
	Properties       BlobProperties `xml:"Properties"`

	// TODO funky generator type -> *BlobMetadata
	Metadata                  Metadata          `xml:"Metadata"`
	BlobTags                  *BlobTags         `xml:"Tags"`
	ObjectReplicationMetadata map[string]string `xml:"ObjectReplicationMetadata"`
}

// BlobMetadata ...
type BlobMetadata struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName xml.Name `xml:"Metadata"`
	// AdditionalProperties - Unmatched properties from the message are deserialized this collection
	AdditionalProperties map[string]string `xml:"AdditionalProperties"`
	Encrypted            *string           `xml:"Encrypted,attr"`
}

// BlobPrefix ...
type BlobPrefix struct {
	Name string `xml:"Name"`
}

// BlobProperties - Properties of a blob
type BlobProperties struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName      xml.Name   `xml:"Properties"`
	CreationTime *time.Time `xml:"Creation-Time"`
	LastModified time.Time  `xml:"Last-Modified"`
	Etag         ETag       `xml:"Etag"`
	// ContentLength - Size in bytes
	ContentLength      *int64  `xml:"Content-Length"`
	ContentType        *string `xml:"Content-Type"`
	ContentEncoding    *string `xml:"Content-Encoding"`
	ContentLanguage    *string `xml:"Content-Language"`
	ContentMD5         []byte  `xml:"Content-MD5"`
	ContentDisposition *string `xml:"Content-Disposition"`
	CacheControl       *string `xml:"Cache-Control"`
	BlobSequenceNumber *int64  `xml:"x-ms-blob-sequence-number"`
	// BlobType - Possible values include: 'BlobBlockBlob', 'BlobPageBlob', 'BlobAppendBlob', 'BlobNone'
	BlobType BlobType `xml:"BlobType"`
	// LeaseStatus - Possible values include: 'LeaseStatusLocked', 'LeaseStatusUnlocked', 'LeaseStatusNone'
	LeaseStatus LeaseStatusType `xml:"LeaseStatus"`
	// LeaseState - Possible values include: 'LeaseStateAvailable', 'LeaseStateLeased', 'LeaseStateExpired', 'LeaseStateBreaking', 'LeaseStateBroken', 'LeaseStateNone'
	LeaseState LeaseStateType `xml:"LeaseState"`
	// LeaseDuration - Possible values include: 'LeaseDurationInfinite', 'LeaseDurationFixed', 'LeaseDurationNone'
	LeaseDuration LeaseDurationType `xml:"LeaseDuration"`
	CopyID        *string           `xml:"CopyId"`
	// CopyStatus - Possible values include: 'CopyStatusPending', 'CopyStatusSuccess', 'CopyStatusAborted', 'CopyStatusFailed', 'CopyStatusNone'
	CopyStatus             CopyStatusType `xml:"CopyStatus"`
	CopySource             *string        `xml:"CopySource"`
	CopyProgress           *string        `xml:"CopyProgress"`
	CopyCompletionTime     *time.Time     `xml:"CopyCompletionTime"`
	CopyStatusDescription  *string        `xml:"CopyStatusDescription"`
	ServerEncrypted        *bool          `xml:"ServerEncrypted"`
	IncrementalCopy        *bool          `xml:"IncrementalCopy"`
	DestinationSnapshot    *string        `xml:"DestinationSnapshot"`
	DeletedTime            *time.Time     `xml:"DeletedTime"`
	RemainingRetentionDays *int32         `xml:"RemainingRetentionDays"`
	// AccessTier - Possible values include: 'AccessTierP4', 'AccessTierP6', 'AccessTierP10', 'AccessTierP15', 'AccessTierP20', 'AccessTierP30', 'AccessTierP40', 'AccessTierP50', 'AccessTierP60', 'AccessTierP70', 'AccessTierP80', 'AccessTierHot', 'AccessTierCool', 'AccessTierArchive', 'AccessTierNone'
	AccessTier         AccessTierType `xml:"AccessTier"`
	AccessTierInferred *bool          `xml:"AccessTierInferred"`
	// ArchiveStatus - Possible values include: 'ArchiveStatusRehydratePendingToHot', 'ArchiveStatusRehydratePendingToCool', 'ArchiveStatusNone'
	ArchiveStatus             ArchiveStatusType `xml:"ArchiveStatus"`
	CustomerProvidedKeySha256 *string           `xml:"CustomerProvidedKeySha256"`
	// EncryptionScope - The name of the encryption scope under which the blob is encrypted.
	EncryptionScope      *string    `xml:"EncryptionScope"`
	AccessTierChangeTime *time.Time `xml:"AccessTierChangeTime"`
	TagCount             *int32     `xml:"TagCount"`
	ExpiresOn            *time.Time `xml:"Expiry-Time"`
	IsSealed             *bool      `xml:"IsSealed"`
	// RehydratePriority - Possible values include: 'RehydratePriorityHigh', 'RehydratePriorityStandard', 'RehydratePriorityNone'
	RehydratePriority RehydratePriorityType `xml:"RehydratePriority"`
}

// MarshalXML implements the xml.Marshaler interface for BlobProperties.
func (bpi BlobProperties) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	bpi2 := (*blobProperties)(unsafe.Pointer(&bpi))
	return e.EncodeElement(*bpi2, start)
}

// UnmarshalXML implements the xml.Unmarshaler interface for BlobProperties.
func (bpi *BlobProperties) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	bpi2 := (*blobProperties)(unsafe.Pointer(bpi))
	return d.DecodeElement(bpi2, &start)
}

// BlobReleaseLeaseResponse ...
type BlobReleaseLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (brlr BlobReleaseLeaseResponse) Response() *http.Response {
	return brlr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (brlr BlobReleaseLeaseResponse) StatusCode() int {
	return brlr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (brlr BlobReleaseLeaseResponse) Status() string {
	return brlr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (brlr BlobReleaseLeaseResponse) ClientRequestID() string {
	return brlr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (brlr BlobReleaseLeaseResponse) Date() time.Time {
	s := brlr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (brlr BlobReleaseLeaseResponse) ErrorCode() string {
	return brlr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (brlr BlobReleaseLeaseResponse) ETag() ETag {
	return ETag(brlr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (brlr BlobReleaseLeaseResponse) LastModified() time.Time {
	s := brlr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (brlr BlobReleaseLeaseResponse) RequestID() string {
	return brlr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (brlr BlobReleaseLeaseResponse) Version() string {
	return brlr.rawResponse.Header.Get("x-ms-version")
}

// BlobRenameResponse ...
type BlobRenameResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (brr BlobRenameResponse) Response() *http.Response {
	return brr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (brr BlobRenameResponse) StatusCode() int {
	return brr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (brr BlobRenameResponse) Status() string {
	return brr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (brr BlobRenameResponse) ClientRequestID() string {
	return brr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentLength returns the value for header Content-Length.
func (brr BlobRenameResponse) ContentLength() int64 {
	s := brr.rawResponse.Header.Get("Content-Length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// Date returns the value for header Date.
func (brr BlobRenameResponse) Date() time.Time {
	s := brr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (brr BlobRenameResponse) ETag() ETag {
	return ETag(brr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (brr BlobRenameResponse) LastModified() time.Time {
	s := brr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (brr BlobRenameResponse) RequestID() string {
	return brr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (brr BlobRenameResponse) Version() string {
	return brr.rawResponse.Header.Get("x-ms-version")
}

// BlobRenewLeaseResponse ...
type BlobRenewLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (brlr BlobRenewLeaseResponse) Response() *http.Response {
	return brlr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (brlr BlobRenewLeaseResponse) StatusCode() int {
	return brlr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (brlr BlobRenewLeaseResponse) Status() string {
	return brlr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (brlr BlobRenewLeaseResponse) ClientRequestID() string {
	return brlr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (brlr BlobRenewLeaseResponse) Date() time.Time {
	s := brlr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (brlr BlobRenewLeaseResponse) ErrorCode() string {
	return brlr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (brlr BlobRenewLeaseResponse) ETag() ETag {
	return ETag(brlr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (brlr BlobRenewLeaseResponse) LastModified() time.Time {
	s := brlr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseID returns the value for header x-ms-lease-id.
func (brlr BlobRenewLeaseResponse) LeaseID() string {
	return brlr.rawResponse.Header.Get("x-ms-lease-id")
}

// RequestID returns the value for header x-ms-request-id.
func (brlr BlobRenewLeaseResponse) RequestID() string {
	return brlr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (brlr BlobRenewLeaseResponse) Version() string {
	return brlr.rawResponse.Header.Get("x-ms-version")
}

// BlobSetAccessControlResponse ...
type BlobSetAccessControlResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bsacr BlobSetAccessControlResponse) Response() *http.Response {
	return bsacr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bsacr BlobSetAccessControlResponse) StatusCode() int {
	return bsacr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bsacr BlobSetAccessControlResponse) Status() string {
	return bsacr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bsacr BlobSetAccessControlResponse) ClientRequestID() string {
	return bsacr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bsacr BlobSetAccessControlResponse) Date() time.Time {
	s := bsacr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (bsacr BlobSetAccessControlResponse) ETag() ETag {
	return ETag(bsacr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bsacr BlobSetAccessControlResponse) LastModified() time.Time {
	s := bsacr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bsacr BlobSetAccessControlResponse) RequestID() string {
	return bsacr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bsacr BlobSetAccessControlResponse) Version() string {
	return bsacr.rawResponse.Header.Get("x-ms-version")
}

// BlobSetExpiryResponse ...
type BlobSetExpiryResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bser BlobSetExpiryResponse) Response() *http.Response {
	return bser.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bser BlobSetExpiryResponse) StatusCode() int {
	return bser.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bser BlobSetExpiryResponse) Status() string {
	return bser.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bser BlobSetExpiryResponse) ClientRequestID() string {
	return bser.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bser BlobSetExpiryResponse) Date() time.Time {
	s := bser.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bser BlobSetExpiryResponse) ErrorCode() string {
	return bser.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bser BlobSetExpiryResponse) ETag() ETag {
	return ETag(bser.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bser BlobSetExpiryResponse) LastModified() time.Time {
	s := bser.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bser BlobSetExpiryResponse) RequestID() string {
	return bser.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bser BlobSetExpiryResponse) Version() string {
	return bser.rawResponse.Header.Get("x-ms-version")
}

// BlobSetHTTPHeadersResponse ...
type BlobSetHTTPHeadersResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bshhr BlobSetHTTPHeadersResponse) Response() *http.Response {
	return bshhr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bshhr BlobSetHTTPHeadersResponse) StatusCode() int {
	return bshhr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bshhr BlobSetHTTPHeadersResponse) Status() string {
	return bshhr.rawResponse.Status
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (bshhr BlobSetHTTPHeadersResponse) BlobSequenceNumber() int64 {
	s := bshhr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bshhr BlobSetHTTPHeadersResponse) ClientRequestID() string {
	return bshhr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bshhr BlobSetHTTPHeadersResponse) Date() time.Time {
	s := bshhr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bshhr BlobSetHTTPHeadersResponse) ErrorCode() string {
	return bshhr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bshhr BlobSetHTTPHeadersResponse) ETag() ETag {
	return ETag(bshhr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bshhr BlobSetHTTPHeadersResponse) LastModified() time.Time {
	s := bshhr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bshhr BlobSetHTTPHeadersResponse) RequestID() string {
	return bshhr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bshhr BlobSetHTTPHeadersResponse) Version() string {
	return bshhr.rawResponse.Header.Get("x-ms-version")
}

// BlobSetMetadataResponse ...
type BlobSetMetadataResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bsmr BlobSetMetadataResponse) Response() *http.Response {
	return bsmr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bsmr BlobSetMetadataResponse) StatusCode() int {
	return bsmr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bsmr BlobSetMetadataResponse) Status() string {
	return bsmr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bsmr BlobSetMetadataResponse) ClientRequestID() string {
	return bsmr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bsmr BlobSetMetadataResponse) Date() time.Time {
	s := bsmr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (bsmr BlobSetMetadataResponse) EncryptionKeySha256() string {
	return bsmr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (bsmr BlobSetMetadataResponse) EncryptionScope() string {
	return bsmr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bsmr BlobSetMetadataResponse) ErrorCode() string {
	return bsmr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bsmr BlobSetMetadataResponse) ETag() ETag {
	return ETag(bsmr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (bsmr BlobSetMetadataResponse) IsServerEncrypted() string {
	return bsmr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (bsmr BlobSetMetadataResponse) LastModified() time.Time {
	s := bsmr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bsmr BlobSetMetadataResponse) RequestID() string {
	return bsmr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bsmr BlobSetMetadataResponse) Version() string {
	return bsmr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bsmr BlobSetMetadataResponse) VersionID() string {
	return bsmr.rawResponse.Header.Get("x-ms-version-id")
}

// BlobSetTagsResponse ...
type BlobSetTagsResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bstr BlobSetTagsResponse) Response() *http.Response {
	return bstr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bstr BlobSetTagsResponse) StatusCode() int {
	return bstr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bstr BlobSetTagsResponse) Status() string {
	return bstr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bstr BlobSetTagsResponse) ClientRequestID() string {
	return bstr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bstr BlobSetTagsResponse) Date() time.Time {
	s := bstr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bstr BlobSetTagsResponse) ErrorCode() string {
	return bstr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bstr BlobSetTagsResponse) RequestID() string {
	return bstr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bstr BlobSetTagsResponse) Version() string {
	return bstr.rawResponse.Header.Get("x-ms-version")
}

// BlobSetTierResponse ...
type BlobSetTierResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bstr BlobSetTierResponse) Response() *http.Response {
	return bstr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bstr BlobSetTierResponse) StatusCode() int {
	return bstr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bstr BlobSetTierResponse) Status() string {
	return bstr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bstr BlobSetTierResponse) ClientRequestID() string {
	return bstr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bstr BlobSetTierResponse) ErrorCode() string {
	return bstr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bstr BlobSetTierResponse) RequestID() string {
	return bstr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bstr BlobSetTierResponse) Version() string {
	return bstr.rawResponse.Header.Get("x-ms-version")
}

// BlobStartCopyFromURLResponse ...
type BlobStartCopyFromURLResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bscfur BlobStartCopyFromURLResponse) Response() *http.Response {
	return bscfur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bscfur BlobStartCopyFromURLResponse) StatusCode() int {
	return bscfur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bscfur BlobStartCopyFromURLResponse) Status() string {
	return bscfur.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bscfur BlobStartCopyFromURLResponse) ClientRequestID() string {
	return bscfur.rawResponse.Header.Get("x-ms-client-request-id")
}

// CopyID returns the value for header x-ms-copy-id.
func (bscfur BlobStartCopyFromURLResponse) CopyID() string {
	return bscfur.rawResponse.Header.Get("x-ms-copy-id")
}

// CopyStatus returns the value for header x-ms-copy-status.
func (bscfur BlobStartCopyFromURLResponse) CopyStatus() CopyStatusType {
	return CopyStatusType(bscfur.rawResponse.Header.Get("x-ms-copy-status"))
}

// Date returns the value for header Date.
func (bscfur BlobStartCopyFromURLResponse) Date() time.Time {
	s := bscfur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bscfur BlobStartCopyFromURLResponse) ErrorCode() string {
	return bscfur.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bscfur BlobStartCopyFromURLResponse) ETag() ETag {
	return ETag(bscfur.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bscfur BlobStartCopyFromURLResponse) LastModified() time.Time {
	s := bscfur.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bscfur BlobStartCopyFromURLResponse) RequestID() string {
	return bscfur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bscfur BlobStartCopyFromURLResponse) Version() string {
	return bscfur.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bscfur BlobStartCopyFromURLResponse) VersionID() string {
	return bscfur.rawResponse.Header.Get("x-ms-version-id")
}

// BlobTag ...
type BlobTag struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName xml.Name `xml:"Tag"`
	Key     string   `xml:"Key"`
	Value   string   `xml:"Value"`
}

// BlobTags - Blob tags
type BlobTags struct {
	rawResponse *http.Response
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName    xml.Name  `xml:"Tags"`
	BlobTagSet []BlobTag `xml:"TagSet>Tag"`
}

// Response returns the raw HTTP response object.
func (bt BlobTags) Response() *http.Response {
	return bt.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bt BlobTags) StatusCode() int {
	return bt.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bt BlobTags) Status() string {
	return bt.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bt BlobTags) ClientRequestID() string {
	return bt.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bt BlobTags) Date() time.Time {
	s := bt.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bt BlobTags) ErrorCode() string {
	return bt.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bt BlobTags) RequestID() string {
	return bt.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bt BlobTags) Version() string {
	return bt.rawResponse.Header.Get("x-ms-version")
}

// BlobUndeleteResponse ...
type BlobUndeleteResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bur BlobUndeleteResponse) Response() *http.Response {
	return bur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bur BlobUndeleteResponse) StatusCode() int {
	return bur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bur BlobUndeleteResponse) Status() string {
	return bur.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bur BlobUndeleteResponse) ClientRequestID() string {
	return bur.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (bur BlobUndeleteResponse) Date() time.Time {
	s := bur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bur BlobUndeleteResponse) ErrorCode() string {
	return bur.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (bur BlobUndeleteResponse) RequestID() string {
	return bur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bur BlobUndeleteResponse) Version() string {
	return bur.rawResponse.Header.Get("x-ms-version")
}

// Block - Represents a single block in a block blob.  It describes the block's ID and size.
type Block struct {
	// Name - The base64 encoded block ID.
	Name string `xml:"Name"`
	// Size - The block size in bytes.
	Size int64 `xml:"Size"`
}

// BlockBlobCommitBlockListResponse ...
type BlockBlobCommitBlockListResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bbcblr BlockBlobCommitBlockListResponse) Response() *http.Response {
	return bbcblr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bbcblr BlockBlobCommitBlockListResponse) StatusCode() int {
	return bbcblr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bbcblr BlockBlobCommitBlockListResponse) Status() string {
	return bbcblr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bbcblr BlockBlobCommitBlockListResponse) ClientRequestID() string {
	return bbcblr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (bbcblr BlockBlobCommitBlockListResponse) ContentMD5() []byte {
	s := bbcblr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (bbcblr BlockBlobCommitBlockListResponse) Date() time.Time {
	s := bbcblr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (bbcblr BlockBlobCommitBlockListResponse) EncryptionKeySha256() string {
	return bbcblr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (bbcblr BlockBlobCommitBlockListResponse) EncryptionScope() string {
	return bbcblr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bbcblr BlockBlobCommitBlockListResponse) ErrorCode() string {
	return bbcblr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bbcblr BlockBlobCommitBlockListResponse) ETag() ETag {
	return ETag(bbcblr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (bbcblr BlockBlobCommitBlockListResponse) IsServerEncrypted() string {
	return bbcblr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (bbcblr BlockBlobCommitBlockListResponse) LastModified() time.Time {
	s := bbcblr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bbcblr BlockBlobCommitBlockListResponse) RequestID() string {
	return bbcblr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bbcblr BlockBlobCommitBlockListResponse) Version() string {
	return bbcblr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bbcblr BlockBlobCommitBlockListResponse) VersionID() string {
	return bbcblr.rawResponse.Header.Get("x-ms-version-id")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (bbcblr BlockBlobCommitBlockListResponse) XMsContentCrc64() []byte {
	s := bbcblr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// BlockBlobStageBlockFromURLResponse ...
type BlockBlobStageBlockFromURLResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bbsbfur BlockBlobStageBlockFromURLResponse) Response() *http.Response {
	return bbsbfur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bbsbfur BlockBlobStageBlockFromURLResponse) StatusCode() int {
	return bbsbfur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bbsbfur BlockBlobStageBlockFromURLResponse) Status() string {
	return bbsbfur.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bbsbfur BlockBlobStageBlockFromURLResponse) ClientRequestID() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (bbsbfur BlockBlobStageBlockFromURLResponse) ContentMD5() []byte {
	s := bbsbfur.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (bbsbfur BlockBlobStageBlockFromURLResponse) Date() time.Time {
	s := bbsbfur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (bbsbfur BlockBlobStageBlockFromURLResponse) EncryptionKeySha256() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (bbsbfur BlockBlobStageBlockFromURLResponse) EncryptionScope() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bbsbfur BlockBlobStageBlockFromURLResponse) ErrorCode() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-error-code")
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (bbsbfur BlockBlobStageBlockFromURLResponse) IsServerEncrypted() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// RequestID returns the value for header x-ms-request-id.
func (bbsbfur BlockBlobStageBlockFromURLResponse) RequestID() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bbsbfur BlockBlobStageBlockFromURLResponse) Version() string {
	return bbsbfur.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (bbsbfur BlockBlobStageBlockFromURLResponse) XMsContentCrc64() []byte {
	s := bbsbfur.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// BlockBlobStageBlockResponse ...
type BlockBlobStageBlockResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bbsbr BlockBlobStageBlockResponse) Response() *http.Response {
	return bbsbr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bbsbr BlockBlobStageBlockResponse) StatusCode() int {
	return bbsbr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bbsbr BlockBlobStageBlockResponse) Status() string {
	return bbsbr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bbsbr BlockBlobStageBlockResponse) ClientRequestID() string {
	return bbsbr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (bbsbr BlockBlobStageBlockResponse) ContentMD5() []byte {
	s := bbsbr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (bbsbr BlockBlobStageBlockResponse) Date() time.Time {
	s := bbsbr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (bbsbr BlockBlobStageBlockResponse) EncryptionKeySha256() string {
	return bbsbr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (bbsbr BlockBlobStageBlockResponse) EncryptionScope() string {
	return bbsbr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bbsbr BlockBlobStageBlockResponse) ErrorCode() string {
	return bbsbr.rawResponse.Header.Get("x-ms-error-code")
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (bbsbr BlockBlobStageBlockResponse) IsServerEncrypted() string {
	return bbsbr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// RequestID returns the value for header x-ms-request-id.
func (bbsbr BlockBlobStageBlockResponse) RequestID() string {
	return bbsbr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bbsbr BlockBlobStageBlockResponse) Version() string {
	return bbsbr.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (bbsbr BlockBlobStageBlockResponse) XMsContentCrc64() []byte {
	s := bbsbr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// BlockBlobUploadResponse ...
type BlockBlobUploadResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (bbur BlockBlobUploadResponse) Response() *http.Response {
	return bbur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bbur BlockBlobUploadResponse) StatusCode() int {
	return bbur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bbur BlockBlobUploadResponse) Status() string {
	return bbur.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bbur BlockBlobUploadResponse) ClientRequestID() string {
	return bbur.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (bbur BlockBlobUploadResponse) ContentMD5() []byte {
	s := bbur.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (bbur BlockBlobUploadResponse) Date() time.Time {
	s := bbur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (bbur BlockBlobUploadResponse) EncryptionKeySha256() string {
	return bbur.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (bbur BlockBlobUploadResponse) EncryptionScope() string {
	return bbur.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (bbur BlockBlobUploadResponse) ErrorCode() string {
	return bbur.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bbur BlockBlobUploadResponse) ETag() ETag {
	return ETag(bbur.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (bbur BlockBlobUploadResponse) IsServerEncrypted() string {
	return bbur.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (bbur BlockBlobUploadResponse) LastModified() time.Time {
	s := bbur.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bbur BlockBlobUploadResponse) RequestID() string {
	return bbur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bbur BlockBlobUploadResponse) Version() string {
	return bbur.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (bbur BlockBlobUploadResponse) VersionID() string {
	return bbur.rawResponse.Header.Get("x-ms-version-id")
}

// BlockList ...
type BlockList struct {
	rawResponse       *http.Response
	CommittedBlocks   []Block `xml:"CommittedBlocks>Block"`
	UncommittedBlocks []Block `xml:"UncommittedBlocks>Block"`
}

// Response returns the raw HTTP response object.
func (bl BlockList) Response() *http.Response {
	return bl.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (bl BlockList) StatusCode() int {
	return bl.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (bl BlockList) Status() string {
	return bl.rawResponse.Status
}

// BlobContentLength returns the value for header x-ms-blob-content-length.
func (bl BlockList) BlobContentLength() int64 {
	s := bl.rawResponse.Header.Get("x-ms-blob-content-length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (bl BlockList) ClientRequestID() string {
	return bl.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentType returns the value for header Content-Type.
func (bl BlockList) ContentType() string {
	return bl.rawResponse.Header.Get("Content-Type")
}

// Date returns the value for header Date.
func (bl BlockList) Date() time.Time {
	s := bl.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (bl BlockList) ErrorCode() string {
	return bl.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (bl BlockList) ETag() ETag {
	return ETag(bl.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (bl BlockList) LastModified() time.Time {
	s := bl.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (bl BlockList) RequestID() string {
	return bl.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (bl BlockList) Version() string {
	return bl.rawResponse.Header.Get("x-ms-version")
}

// BlockLookupList ...
type BlockLookupList struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName     xml.Name `xml:"BlockList"`
	Committed   []string `xml:"Committed"`
	Uncommitted []string `xml:"Uncommitted"`
	Latest      []string `xml:"Latest"`
}

// ClearRange ...
type ClearRange struct {
	Start int64 `xml:"Start"`
	End   int64 `xml:"End"`
}

// ContainerAcquireLeaseResponse ...
type ContainerAcquireLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (calr ContainerAcquireLeaseResponse) Response() *http.Response {
	return calr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (calr ContainerAcquireLeaseResponse) StatusCode() int {
	return calr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (calr ContainerAcquireLeaseResponse) Status() string {
	return calr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (calr ContainerAcquireLeaseResponse) ClientRequestID() string {
	return calr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (calr ContainerAcquireLeaseResponse) Date() time.Time {
	s := calr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (calr ContainerAcquireLeaseResponse) ErrorCode() string {
	return calr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (calr ContainerAcquireLeaseResponse) ETag() ETag {
	return ETag(calr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (calr ContainerAcquireLeaseResponse) LastModified() time.Time {
	s := calr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseID returns the value for header x-ms-lease-id.
func (calr ContainerAcquireLeaseResponse) LeaseID() string {
	return calr.rawResponse.Header.Get("x-ms-lease-id")
}

// RequestID returns the value for header x-ms-request-id.
func (calr ContainerAcquireLeaseResponse) RequestID() string {
	return calr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (calr ContainerAcquireLeaseResponse) Version() string {
	return calr.rawResponse.Header.Get("x-ms-version")
}

// ContainerBreakLeaseResponse ...
type ContainerBreakLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (cblr ContainerBreakLeaseResponse) Response() *http.Response {
	return cblr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (cblr ContainerBreakLeaseResponse) StatusCode() int {
	return cblr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (cblr ContainerBreakLeaseResponse) Status() string {
	return cblr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (cblr ContainerBreakLeaseResponse) ClientRequestID() string {
	return cblr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (cblr ContainerBreakLeaseResponse) Date() time.Time {
	s := cblr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (cblr ContainerBreakLeaseResponse) ErrorCode() string {
	return cblr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (cblr ContainerBreakLeaseResponse) ETag() ETag {
	return ETag(cblr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (cblr ContainerBreakLeaseResponse) LastModified() time.Time {
	s := cblr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseTime returns the value for header x-ms-lease-time.
func (cblr ContainerBreakLeaseResponse) LeaseTime() int32 {
	s := cblr.rawResponse.Header.Get("x-ms-lease-time")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// RequestID returns the value for header x-ms-request-id.
func (cblr ContainerBreakLeaseResponse) RequestID() string {
	return cblr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (cblr ContainerBreakLeaseResponse) Version() string {
	return cblr.rawResponse.Header.Get("x-ms-version")
}

// ContainerChangeLeaseResponse ...
type ContainerChangeLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (cclr ContainerChangeLeaseResponse) Response() *http.Response {
	return cclr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (cclr ContainerChangeLeaseResponse) StatusCode() int {
	return cclr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (cclr ContainerChangeLeaseResponse) Status() string {
	return cclr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (cclr ContainerChangeLeaseResponse) ClientRequestID() string {
	return cclr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (cclr ContainerChangeLeaseResponse) Date() time.Time {
	s := cclr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (cclr ContainerChangeLeaseResponse) ErrorCode() string {
	return cclr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (cclr ContainerChangeLeaseResponse) ETag() ETag {
	return ETag(cclr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (cclr ContainerChangeLeaseResponse) LastModified() time.Time {
	s := cclr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseID returns the value for header x-ms-lease-id.
func (cclr ContainerChangeLeaseResponse) LeaseID() string {
	return cclr.rawResponse.Header.Get("x-ms-lease-id")
}

// RequestID returns the value for header x-ms-request-id.
func (cclr ContainerChangeLeaseResponse) RequestID() string {
	return cclr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (cclr ContainerChangeLeaseResponse) Version() string {
	return cclr.rawResponse.Header.Get("x-ms-version")
}

// ContainerCreateResponse ...
type ContainerCreateResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (ccr ContainerCreateResponse) Response() *http.Response {
	return ccr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (ccr ContainerCreateResponse) StatusCode() int {
	return ccr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (ccr ContainerCreateResponse) Status() string {
	return ccr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (ccr ContainerCreateResponse) ClientRequestID() string {
	return ccr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (ccr ContainerCreateResponse) Date() time.Time {
	s := ccr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (ccr ContainerCreateResponse) ErrorCode() string {
	return ccr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (ccr ContainerCreateResponse) ETag() ETag {
	return ETag(ccr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (ccr ContainerCreateResponse) LastModified() time.Time {
	s := ccr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (ccr ContainerCreateResponse) RequestID() string {
	return ccr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (ccr ContainerCreateResponse) Version() string {
	return ccr.rawResponse.Header.Get("x-ms-version")
}

// ContainerDeleteResponse ...
type ContainerDeleteResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (cdr ContainerDeleteResponse) Response() *http.Response {
	return cdr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (cdr ContainerDeleteResponse) StatusCode() int {
	return cdr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (cdr ContainerDeleteResponse) Status() string {
	return cdr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (cdr ContainerDeleteResponse) ClientRequestID() string {
	return cdr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (cdr ContainerDeleteResponse) Date() time.Time {
	s := cdr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (cdr ContainerDeleteResponse) ErrorCode() string {
	return cdr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (cdr ContainerDeleteResponse) RequestID() string {
	return cdr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (cdr ContainerDeleteResponse) Version() string {
	return cdr.rawResponse.Header.Get("x-ms-version")
}

// ContainerGetAccountInfoResponse ...
type ContainerGetAccountInfoResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (cgair ContainerGetAccountInfoResponse) Response() *http.Response {
	return cgair.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (cgair ContainerGetAccountInfoResponse) StatusCode() int {
	return cgair.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (cgair ContainerGetAccountInfoResponse) Status() string {
	return cgair.rawResponse.Status
}

// AccountKind returns the value for header x-ms-account-kind.
func (cgair ContainerGetAccountInfoResponse) AccountKind() AccountKindType {
	return AccountKindType(cgair.rawResponse.Header.Get("x-ms-account-kind"))
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (cgair ContainerGetAccountInfoResponse) ClientRequestID() string {
	return cgair.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (cgair ContainerGetAccountInfoResponse) Date() time.Time {
	s := cgair.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (cgair ContainerGetAccountInfoResponse) ErrorCode() string {
	return cgair.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (cgair ContainerGetAccountInfoResponse) RequestID() string {
	return cgair.rawResponse.Header.Get("x-ms-request-id")
}

// SkuName returns the value for header x-ms-sku-name.
func (cgair ContainerGetAccountInfoResponse) SkuName() SkuNameType {
	return SkuNameType(cgair.rawResponse.Header.Get("x-ms-sku-name"))
}

// Version returns the value for header x-ms-version.
func (cgair ContainerGetAccountInfoResponse) Version() string {
	return cgair.rawResponse.Header.Get("x-ms-version")
}

// ContainerGetPropertiesResponse ...
type ContainerGetPropertiesResponse struct {
	rawResponse *http.Response
}

// NewMetadata returns user-defined key/value pairs.
func (cgpr ContainerGetPropertiesResponse) NewMetadata() Metadata {
	md := Metadata{}
	for k, v := range cgpr.rawResponse.Header {
		if len(k) > mdPrefixLen {
			if prefix := k[0:mdPrefixLen]; strings.EqualFold(prefix, mdPrefix) {
				md[strings.ToLower(k[mdPrefixLen:])] = v[0]
			}
		}
	}
	return md
}

// Response returns the raw HTTP response object.
func (cgpr ContainerGetPropertiesResponse) Response() *http.Response {
	return cgpr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (cgpr ContainerGetPropertiesResponse) StatusCode() int {
	return cgpr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (cgpr ContainerGetPropertiesResponse) Status() string {
	return cgpr.rawResponse.Status
}

// BlobPublicAccess returns the value for header x-ms-blob-public-access.
func (cgpr ContainerGetPropertiesResponse) BlobPublicAccess() PublicAccessType {
	return PublicAccessType(cgpr.rawResponse.Header.Get("x-ms-blob-public-access"))
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (cgpr ContainerGetPropertiesResponse) ClientRequestID() string {
	return cgpr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (cgpr ContainerGetPropertiesResponse) Date() time.Time {
	s := cgpr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// DefaultEncryptionScope returns the value for header x-ms-default-encryption-scope.
func (cgpr ContainerGetPropertiesResponse) DefaultEncryptionScope() string {
	return cgpr.rawResponse.Header.Get("x-ms-default-encryption-scope")
}

// DenyEncryptionScopeOverride returns the value for header x-ms-deny-encryption-scope-override.
func (cgpr ContainerGetPropertiesResponse) DenyEncryptionScopeOverride() string {
	return cgpr.rawResponse.Header.Get("x-ms-deny-encryption-scope-override")
}

// ErrorCode returns the value for header x-ms-error-code.
func (cgpr ContainerGetPropertiesResponse) ErrorCode() string {
	return cgpr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (cgpr ContainerGetPropertiesResponse) ETag() ETag {
	return ETag(cgpr.rawResponse.Header.Get("ETag"))
}

// HasImmutabilityPolicy returns the value for header x-ms-has-immutability-policy.
func (cgpr ContainerGetPropertiesResponse) HasImmutabilityPolicy() string {
	return cgpr.rawResponse.Header.Get("x-ms-has-immutability-policy")
}

// HasLegalHold returns the value for header x-ms-has-legal-hold.
func (cgpr ContainerGetPropertiesResponse) HasLegalHold() string {
	return cgpr.rawResponse.Header.Get("x-ms-has-legal-hold")
}

// LastModified returns the value for header Last-Modified.
func (cgpr ContainerGetPropertiesResponse) LastModified() time.Time {
	s := cgpr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseDuration returns the value for header x-ms-lease-duration.
func (cgpr ContainerGetPropertiesResponse) LeaseDuration() LeaseDurationType {
	return LeaseDurationType(cgpr.rawResponse.Header.Get("x-ms-lease-duration"))
}

// LeaseState returns the value for header x-ms-lease-state.
func (cgpr ContainerGetPropertiesResponse) LeaseState() LeaseStateType {
	return LeaseStateType(cgpr.rawResponse.Header.Get("x-ms-lease-state"))
}

// LeaseStatus returns the value for header x-ms-lease-status.
func (cgpr ContainerGetPropertiesResponse) LeaseStatus() LeaseStatusType {
	return LeaseStatusType(cgpr.rawResponse.Header.Get("x-ms-lease-status"))
}

// RequestID returns the value for header x-ms-request-id.
func (cgpr ContainerGetPropertiesResponse) RequestID() string {
	return cgpr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (cgpr ContainerGetPropertiesResponse) Version() string {
	return cgpr.rawResponse.Header.Get("x-ms-version")
}

// ContainerItem - An Azure Storage container
type ContainerItem struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName    xml.Name            `xml:"Container"`
	Name       string              `xml:"Name"`
	Deleted    *bool               `xml:"Deleted"`
	Version    *string             `xml:"Version"`
	Properties ContainerProperties `xml:"Properties"`
	Metadata   Metadata            `xml:"Metadata"`
}

// ContainerProperties - Properties of a container
type ContainerProperties struct {
	LastModified time.Time `xml:"Last-Modified"`
	Etag         ETag      `xml:"Etag"`
	// LeaseStatus - Possible values include: 'LeaseStatusLocked', 'LeaseStatusUnlocked', 'LeaseStatusNone'
	LeaseStatus LeaseStatusType `xml:"LeaseStatus"`
	// LeaseState - Possible values include: 'LeaseStateAvailable', 'LeaseStateLeased', 'LeaseStateExpired', 'LeaseStateBreaking', 'LeaseStateBroken', 'LeaseStateNone'
	LeaseState LeaseStateType `xml:"LeaseState"`
	// LeaseDuration - Possible values include: 'LeaseDurationInfinite', 'LeaseDurationFixed', 'LeaseDurationNone'
	LeaseDuration LeaseDurationType `xml:"LeaseDuration"`
	// PublicAccess - Possible values include: 'PublicAccessContainer', 'PublicAccessBlob', 'PublicAccessNone'
	PublicAccess                   PublicAccessType `xml:"PublicAccess"`
	HasImmutabilityPolicy          *bool            `xml:"HasImmutabilityPolicy"`
	HasLegalHold                   *bool            `xml:"HasLegalHold"`
	DefaultEncryptionScope         *string          `xml:"DefaultEncryptionScope"`
	PreventEncryptionScopeOverride *bool            `xml:"DenyEncryptionScopeOverride"`
	DeletedTime                    *time.Time       `xml:"DeletedTime"`
	RemainingRetentionDays         *int32           `xml:"RemainingRetentionDays"`
}

// MarshalXML implements the xml.Marshaler interface for ContainerProperties.
func (cp ContainerProperties) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	cp2 := (*containerProperties)(unsafe.Pointer(&cp))
	return e.EncodeElement(*cp2, start)
}

// UnmarshalXML implements the xml.Unmarshaler interface for ContainerProperties.
func (cp *ContainerProperties) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	cp2 := (*containerProperties)(unsafe.Pointer(cp))
	return d.DecodeElement(cp2, &start)
}

// ContainerReleaseLeaseResponse ...
type ContainerReleaseLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (crlr ContainerReleaseLeaseResponse) Response() *http.Response {
	return crlr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (crlr ContainerReleaseLeaseResponse) StatusCode() int {
	return crlr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (crlr ContainerReleaseLeaseResponse) Status() string {
	return crlr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (crlr ContainerReleaseLeaseResponse) ClientRequestID() string {
	return crlr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (crlr ContainerReleaseLeaseResponse) Date() time.Time {
	s := crlr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (crlr ContainerReleaseLeaseResponse) ErrorCode() string {
	return crlr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (crlr ContainerReleaseLeaseResponse) ETag() ETag {
	return ETag(crlr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (crlr ContainerReleaseLeaseResponse) LastModified() time.Time {
	s := crlr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (crlr ContainerReleaseLeaseResponse) RequestID() string {
	return crlr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (crlr ContainerReleaseLeaseResponse) Version() string {
	return crlr.rawResponse.Header.Get("x-ms-version")
}

// ContainerRenewLeaseResponse ...
type ContainerRenewLeaseResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (crlr ContainerRenewLeaseResponse) Response() *http.Response {
	return crlr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (crlr ContainerRenewLeaseResponse) StatusCode() int {
	return crlr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (crlr ContainerRenewLeaseResponse) Status() string {
	return crlr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (crlr ContainerRenewLeaseResponse) ClientRequestID() string {
	return crlr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (crlr ContainerRenewLeaseResponse) Date() time.Time {
	s := crlr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (crlr ContainerRenewLeaseResponse) ErrorCode() string {
	return crlr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (crlr ContainerRenewLeaseResponse) ETag() ETag {
	return ETag(crlr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (crlr ContainerRenewLeaseResponse) LastModified() time.Time {
	s := crlr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseID returns the value for header x-ms-lease-id.
func (crlr ContainerRenewLeaseResponse) LeaseID() string {
	return crlr.rawResponse.Header.Get("x-ms-lease-id")
}

// RequestID returns the value for header x-ms-request-id.
func (crlr ContainerRenewLeaseResponse) RequestID() string {
	return crlr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (crlr ContainerRenewLeaseResponse) Version() string {
	return crlr.rawResponse.Header.Get("x-ms-version")
}

// ContainerRestoreResponse ...
type ContainerRestoreResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (crr ContainerRestoreResponse) Response() *http.Response {
	return crr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (crr ContainerRestoreResponse) StatusCode() int {
	return crr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (crr ContainerRestoreResponse) Status() string {
	return crr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (crr ContainerRestoreResponse) ClientRequestID() string {
	return crr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (crr ContainerRestoreResponse) Date() time.Time {
	s := crr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (crr ContainerRestoreResponse) ErrorCode() string {
	return crr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (crr ContainerRestoreResponse) RequestID() string {
	return crr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (crr ContainerRestoreResponse) Version() string {
	return crr.rawResponse.Header.Get("x-ms-version")
}

// ContainerSetAccessPolicyResponse ...
type ContainerSetAccessPolicyResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (csapr ContainerSetAccessPolicyResponse) Response() *http.Response {
	return csapr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (csapr ContainerSetAccessPolicyResponse) StatusCode() int {
	return csapr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (csapr ContainerSetAccessPolicyResponse) Status() string {
	return csapr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (csapr ContainerSetAccessPolicyResponse) ClientRequestID() string {
	return csapr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (csapr ContainerSetAccessPolicyResponse) Date() time.Time {
	s := csapr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (csapr ContainerSetAccessPolicyResponse) ErrorCode() string {
	return csapr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (csapr ContainerSetAccessPolicyResponse) ETag() ETag {
	return ETag(csapr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (csapr ContainerSetAccessPolicyResponse) LastModified() time.Time {
	s := csapr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (csapr ContainerSetAccessPolicyResponse) RequestID() string {
	return csapr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (csapr ContainerSetAccessPolicyResponse) Version() string {
	return csapr.rawResponse.Header.Get("x-ms-version")
}

// ContainerSetMetadataResponse ...
type ContainerSetMetadataResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (csmr ContainerSetMetadataResponse) Response() *http.Response {
	return csmr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (csmr ContainerSetMetadataResponse) StatusCode() int {
	return csmr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (csmr ContainerSetMetadataResponse) Status() string {
	return csmr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (csmr ContainerSetMetadataResponse) ClientRequestID() string {
	return csmr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (csmr ContainerSetMetadataResponse) Date() time.Time {
	s := csmr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (csmr ContainerSetMetadataResponse) ErrorCode() string {
	return csmr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (csmr ContainerSetMetadataResponse) ETag() ETag {
	return ETag(csmr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (csmr ContainerSetMetadataResponse) LastModified() time.Time {
	s := csmr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (csmr ContainerSetMetadataResponse) RequestID() string {
	return csmr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (csmr ContainerSetMetadataResponse) Version() string {
	return csmr.rawResponse.Header.Get("x-ms-version")
}

// CorsRule - CORS is an HTTP feature that enables a web application running under one domain to access
// resources in another domain. Web browsers implement a security restriction known as same-origin policy that
// prevents a web page from calling APIs in a different domain; CORS provides a secure way to allow one domain
// (the origin domain) to call APIs in another domain
type CorsRule struct {
	// AllowedOrigins - The origin domains that are permitted to make a request against the storage service via CORS. The origin domain is the domain from which the request originates. Note that the origin must be an exact case-sensitive match with the origin that the user age sends to the service. You can also use the wildcard character '*' to allow all origin domains to make requests via CORS.
	AllowedOrigins string `xml:"AllowedOrigins"`
	// AllowedMethods - The methods (HTTP request verbs) that the origin domain may use for a CORS request. (comma separated)
	AllowedMethods string `xml:"AllowedMethods"`
	// AllowedHeaders - the request headers that the origin domain may specify on the CORS request.
	AllowedHeaders string `xml:"AllowedHeaders"`
	// ExposedHeaders - The response headers that may be sent in the response to the CORS request and exposed by the browser to the request issuer
	ExposedHeaders string `xml:"ExposedHeaders"`
	// MaxAgeInSeconds - The maximum amount time that a browser should cache the preflight OPTIONS request.
	MaxAgeInSeconds int32 `xml:"MaxAgeInSeconds"`
}

// DataLakeStorageError ...
type DataLakeStorageError struct {
	// DataLakeStorageErrorDetails - The service error response object.
	DataLakeStorageErrorDetails *DataLakeStorageErrorError `xml:"error"`
}

// DataLakeStorageErrorError - The service error response object.
type DataLakeStorageErrorError struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName xml.Name `xml:"DataLakeStorageError_error"`
	// Code - The service error code.
	Code *string `xml:"Code"`
	// Message - The service error message.
	Message *string `xml:"Message"`
}

// DelimitedTextConfiguration - delimited text configuration
type DelimitedTextConfiguration struct {
	// ColumnSeparator - column separator
	ColumnSeparator string `xml:"ColumnSeparator"`
	// FieldQuote - field quote
	FieldQuote string `xml:"FieldQuote"`
	// RecordSeparator - record separator
	RecordSeparator string `xml:"RecordSeparator"`
	// EscapeChar - escape char
	EscapeChar string `xml:"EscapeChar"`
	// HeadersPresent - has headers
	HeadersPresent bool `xml:"HasHeaders"`
}

// DirectoryCreateResponse ...
type DirectoryCreateResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (dcr DirectoryCreateResponse) Response() *http.Response {
	return dcr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (dcr DirectoryCreateResponse) StatusCode() int {
	return dcr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (dcr DirectoryCreateResponse) Status() string {
	return dcr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (dcr DirectoryCreateResponse) ClientRequestID() string {
	return dcr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentLength returns the value for header Content-Length.
func (dcr DirectoryCreateResponse) ContentLength() int64 {
	s := dcr.rawResponse.Header.Get("Content-Length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// Date returns the value for header Date.
func (dcr DirectoryCreateResponse) Date() time.Time {
	s := dcr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (dcr DirectoryCreateResponse) ETag() ETag {
	return ETag(dcr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (dcr DirectoryCreateResponse) LastModified() time.Time {
	s := dcr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (dcr DirectoryCreateResponse) RequestID() string {
	return dcr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (dcr DirectoryCreateResponse) Version() string {
	return dcr.rawResponse.Header.Get("x-ms-version")
}

// DirectoryDeleteResponse ...
type DirectoryDeleteResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (ddr DirectoryDeleteResponse) Response() *http.Response {
	return ddr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (ddr DirectoryDeleteResponse) StatusCode() int {
	return ddr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (ddr DirectoryDeleteResponse) Status() string {
	return ddr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (ddr DirectoryDeleteResponse) ClientRequestID() string {
	return ddr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (ddr DirectoryDeleteResponse) Date() time.Time {
	s := ddr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// Marker returns the value for header x-ms-continuation.
func (ddr DirectoryDeleteResponse) Marker() string {
	return ddr.rawResponse.Header.Get("x-ms-continuation")
}

// RequestID returns the value for header x-ms-request-id.
func (ddr DirectoryDeleteResponse) RequestID() string {
	return ddr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (ddr DirectoryDeleteResponse) Version() string {
	return ddr.rawResponse.Header.Get("x-ms-version")
}

// DirectoryGetAccessControlResponse ...
type DirectoryGetAccessControlResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (dgacr DirectoryGetAccessControlResponse) Response() *http.Response {
	return dgacr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (dgacr DirectoryGetAccessControlResponse) StatusCode() int {
	return dgacr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (dgacr DirectoryGetAccessControlResponse) Status() string {
	return dgacr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (dgacr DirectoryGetAccessControlResponse) ClientRequestID() string {
	return dgacr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (dgacr DirectoryGetAccessControlResponse) Date() time.Time {
	s := dgacr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (dgacr DirectoryGetAccessControlResponse) ETag() ETag {
	return ETag(dgacr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (dgacr DirectoryGetAccessControlResponse) LastModified() time.Time {
	s := dgacr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (dgacr DirectoryGetAccessControlResponse) RequestID() string {
	return dgacr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (dgacr DirectoryGetAccessControlResponse) Version() string {
	return dgacr.rawResponse.Header.Get("x-ms-version")
}

// XMsACL returns the value for header x-ms-acl.
func (dgacr DirectoryGetAccessControlResponse) XMsACL() string {
	return dgacr.rawResponse.Header.Get("x-ms-acl")
}

// XMsGroup returns the value for header x-ms-group.
func (dgacr DirectoryGetAccessControlResponse) XMsGroup() string {
	return dgacr.rawResponse.Header.Get("x-ms-group")
}

// XMsOwner returns the value for header x-ms-owner.
func (dgacr DirectoryGetAccessControlResponse) XMsOwner() string {
	return dgacr.rawResponse.Header.Get("x-ms-owner")
}

// XMsPermissions returns the value for header x-ms-permissions.
func (dgacr DirectoryGetAccessControlResponse) XMsPermissions() string {
	return dgacr.rawResponse.Header.Get("x-ms-permissions")
}

// DirectoryRenameResponse ...
type DirectoryRenameResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (drr DirectoryRenameResponse) Response() *http.Response {
	return drr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (drr DirectoryRenameResponse) StatusCode() int {
	return drr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (drr DirectoryRenameResponse) Status() string {
	return drr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (drr DirectoryRenameResponse) ClientRequestID() string {
	return drr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentLength returns the value for header Content-Length.
func (drr DirectoryRenameResponse) ContentLength() int64 {
	s := drr.rawResponse.Header.Get("Content-Length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// Date returns the value for header Date.
func (drr DirectoryRenameResponse) Date() time.Time {
	s := drr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (drr DirectoryRenameResponse) ETag() ETag {
	return ETag(drr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (drr DirectoryRenameResponse) LastModified() time.Time {
	s := drr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// Marker returns the value for header x-ms-continuation.
func (drr DirectoryRenameResponse) Marker() string {
	return drr.rawResponse.Header.Get("x-ms-continuation")
}

// RequestID returns the value for header x-ms-request-id.
func (drr DirectoryRenameResponse) RequestID() string {
	return drr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (drr DirectoryRenameResponse) Version() string {
	return drr.rawResponse.Header.Get("x-ms-version")
}

// DirectorySetAccessControlResponse ...
type DirectorySetAccessControlResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (dsacr DirectorySetAccessControlResponse) Response() *http.Response {
	return dsacr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (dsacr DirectorySetAccessControlResponse) StatusCode() int {
	return dsacr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (dsacr DirectorySetAccessControlResponse) Status() string {
	return dsacr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (dsacr DirectorySetAccessControlResponse) ClientRequestID() string {
	return dsacr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (dsacr DirectorySetAccessControlResponse) Date() time.Time {
	s := dsacr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ETag returns the value for header ETag.
func (dsacr DirectorySetAccessControlResponse) ETag() ETag {
	return ETag(dsacr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (dsacr DirectorySetAccessControlResponse) LastModified() time.Time {
	s := dsacr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (dsacr DirectorySetAccessControlResponse) RequestID() string {
	return dsacr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (dsacr DirectorySetAccessControlResponse) Version() string {
	return dsacr.rawResponse.Header.Get("x-ms-version")
}

// downloadResponse - Wraps the response from the blobClient.Download method.
type downloadResponse struct {
	rawResponse *http.Response
}

// NewMetadata returns user-defined key/value pairs.
func (dr downloadResponse) NewMetadata() Metadata {
	md := Metadata{}
	for k, v := range dr.rawResponse.Header {
		if len(k) > mdPrefixLen {
			if prefix := k[0:mdPrefixLen]; strings.EqualFold(prefix, mdPrefix) {
				md[strings.ToLower(k[mdPrefixLen:])] = v[0]
			}
		}
	}
	return md
}

// Response returns the raw HTTP response object.
func (dr downloadResponse) Response() *http.Response {
	return dr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (dr downloadResponse) StatusCode() int {
	return dr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (dr downloadResponse) Status() string {
	return dr.rawResponse.Status
}

// Body returns the raw HTTP response object's Body.
func (dr downloadResponse) Body() io.ReadCloser {
	return dr.rawResponse.Body
}

// AcceptRanges returns the value for header Accept-Ranges.
func (dr downloadResponse) AcceptRanges() string {
	return dr.rawResponse.Header.Get("Accept-Ranges")
}

// BlobCommittedBlockCount returns the value for header x-ms-blob-committed-block-count.
func (dr downloadResponse) BlobCommittedBlockCount() int32 {
	s := dr.rawResponse.Header.Get("x-ms-blob-committed-block-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// BlobContentMD5 returns the value for header x-ms-blob-content-md5.
func (dr downloadResponse) BlobContentMD5() []byte {
	s := dr.rawResponse.Header.Get("x-ms-blob-content-md5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (dr downloadResponse) BlobSequenceNumber() int64 {
	s := dr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// BlobType returns the value for header x-ms-blob-type.
func (dr downloadResponse) BlobType() BlobType {
	return BlobType(dr.rawResponse.Header.Get("x-ms-blob-type"))
}

// CacheControl returns the value for header Cache-Control.
func (dr downloadResponse) CacheControl() string {
	return dr.rawResponse.Header.Get("Cache-Control")
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (dr downloadResponse) ClientRequestID() string {
	return dr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentCrc64 returns the value for header x-ms-content-crc64.
func (dr downloadResponse) ContentCrc64() []byte {
	s := dr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// ContentDisposition returns the value for header Content-Disposition.
func (dr downloadResponse) ContentDisposition() string {
	return dr.rawResponse.Header.Get("Content-Disposition")
}

// ContentEncoding returns the value for header Content-Encoding.
func (dr downloadResponse) ContentEncoding() string {
	return dr.rawResponse.Header.Get("Content-Encoding")
}

// ContentLanguage returns the value for header Content-Language.
func (dr downloadResponse) ContentLanguage() string {
	return dr.rawResponse.Header.Get("Content-Language")
}

// ContentLength returns the value for header Content-Length.
func (dr downloadResponse) ContentLength() int64 {
	s := dr.rawResponse.Header.Get("Content-Length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ContentMD5 returns the value for header Content-MD5.
func (dr downloadResponse) ContentMD5() []byte {
	s := dr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// ContentRange returns the value for header Content-Range.
func (dr downloadResponse) ContentRange() string {
	return dr.rawResponse.Header.Get("Content-Range")
}

// ContentType returns the value for header Content-Type.
func (dr downloadResponse) ContentType() string {
	return dr.rawResponse.Header.Get("Content-Type")
}

// CopyCompletionTime returns the value for header x-ms-copy-completion-time.
func (dr downloadResponse) CopyCompletionTime() time.Time {
	s := dr.rawResponse.Header.Get("x-ms-copy-completion-time")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// CopyID returns the value for header x-ms-copy-id.
func (dr downloadResponse) CopyID() string {
	return dr.rawResponse.Header.Get("x-ms-copy-id")
}

// CopyProgress returns the value for header x-ms-copy-progress.
func (dr downloadResponse) CopyProgress() string {
	return dr.rawResponse.Header.Get("x-ms-copy-progress")
}

// CopySource returns the value for header x-ms-copy-source.
func (dr downloadResponse) CopySource() string {
	return dr.rawResponse.Header.Get("x-ms-copy-source")
}

// CopyStatus returns the value for header x-ms-copy-status.
func (dr downloadResponse) CopyStatus() CopyStatusType {
	return CopyStatusType(dr.rawResponse.Header.Get("x-ms-copy-status"))
}

// CopyStatusDescription returns the value for header x-ms-copy-status-description.
func (dr downloadResponse) CopyStatusDescription() string {
	return dr.rawResponse.Header.Get("x-ms-copy-status-description")
}

// Date returns the value for header Date.
func (dr downloadResponse) Date() time.Time {
	s := dr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (dr downloadResponse) EncryptionKeySha256() string {
	return dr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (dr downloadResponse) EncryptionScope() string {
	return dr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (dr downloadResponse) ErrorCode() string {
	return dr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (dr downloadResponse) ETag() ETag {
	return ETag(dr.rawResponse.Header.Get("ETag"))
}

// IsSealed returns the value for header x-ms-blob-sealed.
func (dr downloadResponse) IsSealed() string {
	return dr.rawResponse.Header.Get("x-ms-blob-sealed")
}

// IsServerEncrypted returns the value for header x-ms-server-encrypted.
func (dr downloadResponse) IsServerEncrypted() string {
	return dr.rawResponse.Header.Get("x-ms-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (dr downloadResponse) LastModified() time.Time {
	s := dr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseDuration returns the value for header x-ms-lease-duration.
func (dr downloadResponse) LeaseDuration() LeaseDurationType {
	return LeaseDurationType(dr.rawResponse.Header.Get("x-ms-lease-duration"))
}

// LeaseState returns the value for header x-ms-lease-state.
func (dr downloadResponse) LeaseState() LeaseStateType {
	return LeaseStateType(dr.rawResponse.Header.Get("x-ms-lease-state"))
}

// LeaseStatus returns the value for header x-ms-lease-status.
func (dr downloadResponse) LeaseStatus() LeaseStatusType {
	return LeaseStatusType(dr.rawResponse.Header.Get("x-ms-lease-status"))
}

// ObjectReplicationPolicyID returns the value for header x-ms-or-policy-id.
func (dr downloadResponse) ObjectReplicationPolicyID() string {
	return dr.rawResponse.Header.Get("x-ms-or-policy-id")
}

// ObjectReplicationRules returns the value for header x-ms-or.
func (dr downloadResponse) ObjectReplicationRules() string {
	return dr.rawResponse.Header.Get("x-ms-or")
}

// RequestID returns the value for header x-ms-request-id.
func (dr downloadResponse) RequestID() string {
	return dr.rawResponse.Header.Get("x-ms-request-id")
}

// TagCount returns the value for header x-ms-tag-count.
func (dr downloadResponse) TagCount() int64 {
	s := dr.rawResponse.Header.Get("x-ms-tag-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// Version returns the value for header x-ms-version.
func (dr downloadResponse) Version() string {
	return dr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (dr downloadResponse) VersionID() string {
	return dr.rawResponse.Header.Get("x-ms-version-id")
}

// FilterBlobItem - Blob info from a Filter Blobs API call
type FilterBlobItem struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName       xml.Name `xml:"Blob"`
	Name          string   `xml:"Name"`
	ContainerName string   `xml:"ContainerName"`
	TagValue      string   `xml:"TagValue"`
}

// FilterBlobSegment - The result of a Filter Blobs API call
type FilterBlobSegment struct {
	rawResponse *http.Response
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName         xml.Name         `xml:"EnumerationResults"`
	ServiceEndpoint string           `xml:"ServiceEndpoint,attr"`
	Where           string           `xml:"Where"`
	Blobs           []FilterBlobItem `xml:"Blobs>Blob"`
	NextMarker      *string          `xml:"NextMarker"`
}

// Response returns the raw HTTP response object.
func (fbs FilterBlobSegment) Response() *http.Response {
	return fbs.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (fbs FilterBlobSegment) StatusCode() int {
	return fbs.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (fbs FilterBlobSegment) Status() string {
	return fbs.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (fbs FilterBlobSegment) ClientRequestID() string {
	return fbs.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (fbs FilterBlobSegment) Date() time.Time {
	s := fbs.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (fbs FilterBlobSegment) ErrorCode() string {
	return fbs.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (fbs FilterBlobSegment) RequestID() string {
	return fbs.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (fbs FilterBlobSegment) Version() string {
	return fbs.rawResponse.Header.Get("x-ms-version")
}

// GeoReplication - Geo-Replication information for the Secondary Storage Service
type GeoReplication struct {
	// Status - The status of the secondary location. Possible values include: 'GeoReplicationStatusLive', 'GeoReplicationStatusBootstrap', 'GeoReplicationStatusUnavailable', 'GeoReplicationStatusNone'
	Status GeoReplicationStatusType `xml:"Status"`
	// LastSyncTime - A GMT date/time value, to the second. All primary writes preceding this value are guaranteed to be available for read operations at the secondary. Primary writes after this point in time may or may not be available for reads.
	LastSyncTime time.Time `xml:"LastSyncTime"`
}

// MarshalXML implements the xml.Marshaler interface for GeoReplication.
func (gr GeoReplication) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	gr2 := (*geoReplication)(unsafe.Pointer(&gr))
	return e.EncodeElement(*gr2, start)
}

// UnmarshalXML implements the xml.Unmarshaler interface for GeoReplication.
func (gr *GeoReplication) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	gr2 := (*geoReplication)(unsafe.Pointer(gr))
	return d.DecodeElement(gr2, &start)
}

// JSONTextConfiguration - json text configuration
type JSONTextConfiguration struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName xml.Name `xml:"JsonTextConfiguration"`
	// RecordSeparator - record separator
	RecordSeparator string `xml:"RecordSeparator"`
}

// KeyInfo - Key information
type KeyInfo struct {
	// Start - The date-time the key is active in ISO 8601 UTC time
	Start string `xml:"Start"`
	// Expiry - The date-time the key expires in ISO 8601 UTC time
	Expiry string `xml:"Expiry"`
}

// ListBlobsFlatSegmentResponse - An enumeration of blobs
type ListBlobsFlatSegmentResponse struct {
	rawResponse *http.Response
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName         xml.Name            `xml:"EnumerationResults"`
	ServiceEndpoint string              `xml:"ServiceEndpoint,attr"`
	ContainerName   string              `xml:"ContainerName,attr"`
	Prefix          *string             `xml:"Prefix"`
	Marker          *string             `xml:"Marker"`
	MaxResults      *int32              `xml:"MaxResults"`
	Segment         BlobFlatListSegment `xml:"Blobs"`
	NextMarker      Marker              `xml:"NextMarker"`
}

// Response returns the raw HTTP response object.
func (lbfsr ListBlobsFlatSegmentResponse) Response() *http.Response {
	return lbfsr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (lbfsr ListBlobsFlatSegmentResponse) StatusCode() int {
	return lbfsr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (lbfsr ListBlobsFlatSegmentResponse) Status() string {
	return lbfsr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (lbfsr ListBlobsFlatSegmentResponse) ClientRequestID() string {
	return lbfsr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentType returns the value for header Content-Type.
func (lbfsr ListBlobsFlatSegmentResponse) ContentType() string {
	return lbfsr.rawResponse.Header.Get("Content-Type")
}

// Date returns the value for header Date.
func (lbfsr ListBlobsFlatSegmentResponse) Date() time.Time {
	s := lbfsr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (lbfsr ListBlobsFlatSegmentResponse) ErrorCode() string {
	return lbfsr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (lbfsr ListBlobsFlatSegmentResponse) RequestID() string {
	return lbfsr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (lbfsr ListBlobsFlatSegmentResponse) Version() string {
	return lbfsr.rawResponse.Header.Get("x-ms-version")
}

// ListBlobsHierarchySegmentResponse - An enumeration of blobs
type ListBlobsHierarchySegmentResponse struct {
	rawResponse *http.Response
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName         xml.Name                 `xml:"EnumerationResults"`
	ServiceEndpoint string                   `xml:"ServiceEndpoint,attr"`
	ContainerName   string                   `xml:"ContainerName,attr"`
	Prefix          *string                  `xml:"Prefix"`
	Marker          *string                  `xml:"Marker"`
	MaxResults      *int32                   `xml:"MaxResults"`
	Delimiter       *string                  `xml:"Delimiter"`
	Segment         BlobHierarchyListSegment `xml:"Blobs"`
	NextMarker      Marker                   `xml:"NextMarker"`
}

// Response returns the raw HTTP response object.
func (lbhsr ListBlobsHierarchySegmentResponse) Response() *http.Response {
	return lbhsr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (lbhsr ListBlobsHierarchySegmentResponse) StatusCode() int {
	return lbhsr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (lbhsr ListBlobsHierarchySegmentResponse) Status() string {
	return lbhsr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (lbhsr ListBlobsHierarchySegmentResponse) ClientRequestID() string {
	return lbhsr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentType returns the value for header Content-Type.
func (lbhsr ListBlobsHierarchySegmentResponse) ContentType() string {
	return lbhsr.rawResponse.Header.Get("Content-Type")
}

// Date returns the value for header Date.
func (lbhsr ListBlobsHierarchySegmentResponse) Date() time.Time {
	s := lbhsr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (lbhsr ListBlobsHierarchySegmentResponse) ErrorCode() string {
	return lbhsr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (lbhsr ListBlobsHierarchySegmentResponse) RequestID() string {
	return lbhsr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (lbhsr ListBlobsHierarchySegmentResponse) Version() string {
	return lbhsr.rawResponse.Header.Get("x-ms-version")
}

// ListContainersSegmentResponse - An enumeration of containers
type ListContainersSegmentResponse struct {
	rawResponse *http.Response
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName         xml.Name        `xml:"EnumerationResults"`
	ServiceEndpoint string          `xml:"ServiceEndpoint,attr"`
	Prefix          *string         `xml:"Prefix"`
	Marker          *string         `xml:"Marker"`
	MaxResults      *int32          `xml:"MaxResults"`
	ContainerItems  []ContainerItem `xml:"Containers>Container"`
	NextMarker      Marker          `xml:"NextMarker"`
}

// Response returns the raw HTTP response object.
func (lcsr ListContainersSegmentResponse) Response() *http.Response {
	return lcsr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (lcsr ListContainersSegmentResponse) StatusCode() int {
	return lcsr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (lcsr ListContainersSegmentResponse) Status() string {
	return lcsr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (lcsr ListContainersSegmentResponse) ClientRequestID() string {
	return lcsr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ErrorCode returns the value for header x-ms-error-code.
func (lcsr ListContainersSegmentResponse) ErrorCode() string {
	return lcsr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (lcsr ListContainersSegmentResponse) RequestID() string {
	return lcsr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (lcsr ListContainersSegmentResponse) Version() string {
	return lcsr.rawResponse.Header.Get("x-ms-version")
}

// Logging - Azure Analytics Logging settings.
type Logging struct {
	// Version - The version of Storage Analytics to configure.
	Version string `xml:"Version"`
	// Delete - Indicates whether all delete requests should be logged.
	Delete bool `xml:"Delete"`
	// Read - Indicates whether all read requests should be logged.
	Read bool `xml:"Read"`
	// Write - Indicates whether all write requests should be logged.
	Write           bool            `xml:"Write"`
	RetentionPolicy RetentionPolicy `xml:"RetentionPolicy"`
}

// Metrics - a summary of request statistics grouped by API in hour or minute aggregates for blobs
type Metrics struct {
	// Version - The version of Storage Analytics to configure.
	Version *string `xml:"Version"`
	// Enabled - Indicates whether metrics are enabled for the Blob service.
	Enabled bool `xml:"Enabled"`
	// IncludeAPIs - Indicates whether metrics should generate summary statistics for called API operations.
	IncludeAPIs     *bool            `xml:"IncludeAPIs"`
	RetentionPolicy *RetentionPolicy `xml:"RetentionPolicy"`
}

// PageBlobClearPagesResponse ...
type PageBlobClearPagesResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbcpr PageBlobClearPagesResponse) Response() *http.Response {
	return pbcpr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbcpr PageBlobClearPagesResponse) StatusCode() int {
	return pbcpr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbcpr PageBlobClearPagesResponse) Status() string {
	return pbcpr.rawResponse.Status
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (pbcpr PageBlobClearPagesResponse) BlobSequenceNumber() int64 {
	s := pbcpr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pbcpr PageBlobClearPagesResponse) ClientRequestID() string {
	return pbcpr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (pbcpr PageBlobClearPagesResponse) ContentMD5() []byte {
	s := pbcpr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (pbcpr PageBlobClearPagesResponse) Date() time.Time {
	s := pbcpr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbcpr PageBlobClearPagesResponse) ErrorCode() string {
	return pbcpr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbcpr PageBlobClearPagesResponse) ETag() ETag {
	return ETag(pbcpr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (pbcpr PageBlobClearPagesResponse) LastModified() time.Time {
	s := pbcpr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbcpr PageBlobClearPagesResponse) RequestID() string {
	return pbcpr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbcpr PageBlobClearPagesResponse) Version() string {
	return pbcpr.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (pbcpr PageBlobClearPagesResponse) XMsContentCrc64() []byte {
	s := pbcpr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// PageBlobCopyIncrementalResponse ...
type PageBlobCopyIncrementalResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbcir PageBlobCopyIncrementalResponse) Response() *http.Response {
	return pbcir.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbcir PageBlobCopyIncrementalResponse) StatusCode() int {
	return pbcir.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbcir PageBlobCopyIncrementalResponse) Status() string {
	return pbcir.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pbcir PageBlobCopyIncrementalResponse) ClientRequestID() string {
	return pbcir.rawResponse.Header.Get("x-ms-client-request-id")
}

// CopyID returns the value for header x-ms-copy-id.
func (pbcir PageBlobCopyIncrementalResponse) CopyID() string {
	return pbcir.rawResponse.Header.Get("x-ms-copy-id")
}

// CopyStatus returns the value for header x-ms-copy-status.
func (pbcir PageBlobCopyIncrementalResponse) CopyStatus() CopyStatusType {
	return CopyStatusType(pbcir.rawResponse.Header.Get("x-ms-copy-status"))
}

// Date returns the value for header Date.
func (pbcir PageBlobCopyIncrementalResponse) Date() time.Time {
	s := pbcir.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbcir PageBlobCopyIncrementalResponse) ErrorCode() string {
	return pbcir.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbcir PageBlobCopyIncrementalResponse) ETag() ETag {
	return ETag(pbcir.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (pbcir PageBlobCopyIncrementalResponse) LastModified() time.Time {
	s := pbcir.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbcir PageBlobCopyIncrementalResponse) RequestID() string {
	return pbcir.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbcir PageBlobCopyIncrementalResponse) Version() string {
	return pbcir.rawResponse.Header.Get("x-ms-version")
}

// PageBlobCreateResponse ...
type PageBlobCreateResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbcr PageBlobCreateResponse) Response() *http.Response {
	return pbcr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbcr PageBlobCreateResponse) StatusCode() int {
	return pbcr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbcr PageBlobCreateResponse) Status() string {
	return pbcr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pbcr PageBlobCreateResponse) ClientRequestID() string {
	return pbcr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (pbcr PageBlobCreateResponse) ContentMD5() []byte {
	s := pbcr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (pbcr PageBlobCreateResponse) Date() time.Time {
	s := pbcr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (pbcr PageBlobCreateResponse) EncryptionKeySha256() string {
	return pbcr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (pbcr PageBlobCreateResponse) EncryptionScope() string {
	return pbcr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbcr PageBlobCreateResponse) ErrorCode() string {
	return pbcr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbcr PageBlobCreateResponse) ETag() ETag {
	return ETag(pbcr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (pbcr PageBlobCreateResponse) IsServerEncrypted() string {
	return pbcr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (pbcr PageBlobCreateResponse) LastModified() time.Time {
	s := pbcr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbcr PageBlobCreateResponse) RequestID() string {
	return pbcr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbcr PageBlobCreateResponse) Version() string {
	return pbcr.rawResponse.Header.Get("x-ms-version")
}

// VersionID returns the value for header x-ms-version-id.
func (pbcr PageBlobCreateResponse) VersionID() string {
	return pbcr.rawResponse.Header.Get("x-ms-version-id")
}

// PageBlobResizeResponse ...
type PageBlobResizeResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbrr PageBlobResizeResponse) Response() *http.Response {
	return pbrr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbrr PageBlobResizeResponse) StatusCode() int {
	return pbrr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbrr PageBlobResizeResponse) Status() string {
	return pbrr.rawResponse.Status
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (pbrr PageBlobResizeResponse) BlobSequenceNumber() int64 {
	s := pbrr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pbrr PageBlobResizeResponse) ClientRequestID() string {
	return pbrr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (pbrr PageBlobResizeResponse) Date() time.Time {
	s := pbrr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbrr PageBlobResizeResponse) ErrorCode() string {
	return pbrr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbrr PageBlobResizeResponse) ETag() ETag {
	return ETag(pbrr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (pbrr PageBlobResizeResponse) LastModified() time.Time {
	s := pbrr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbrr PageBlobResizeResponse) RequestID() string {
	return pbrr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbrr PageBlobResizeResponse) Version() string {
	return pbrr.rawResponse.Header.Get("x-ms-version")
}

// PageBlobUpdateSequenceNumberResponse ...
type PageBlobUpdateSequenceNumberResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbusnr PageBlobUpdateSequenceNumberResponse) Response() *http.Response {
	return pbusnr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbusnr PageBlobUpdateSequenceNumberResponse) StatusCode() int {
	return pbusnr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbusnr PageBlobUpdateSequenceNumberResponse) Status() string {
	return pbusnr.rawResponse.Status
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (pbusnr PageBlobUpdateSequenceNumberResponse) BlobSequenceNumber() int64 {
	s := pbusnr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pbusnr PageBlobUpdateSequenceNumberResponse) ClientRequestID() string {
	return pbusnr.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (pbusnr PageBlobUpdateSequenceNumberResponse) Date() time.Time {
	s := pbusnr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbusnr PageBlobUpdateSequenceNumberResponse) ErrorCode() string {
	return pbusnr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbusnr PageBlobUpdateSequenceNumberResponse) ETag() ETag {
	return ETag(pbusnr.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (pbusnr PageBlobUpdateSequenceNumberResponse) LastModified() time.Time {
	s := pbusnr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbusnr PageBlobUpdateSequenceNumberResponse) RequestID() string {
	return pbusnr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbusnr PageBlobUpdateSequenceNumberResponse) Version() string {
	return pbusnr.rawResponse.Header.Get("x-ms-version")
}

// PageBlobUploadPagesFromURLResponse ...
type PageBlobUploadPagesFromURLResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbupfur PageBlobUploadPagesFromURLResponse) Response() *http.Response {
	return pbupfur.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbupfur PageBlobUploadPagesFromURLResponse) StatusCode() int {
	return pbupfur.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbupfur PageBlobUploadPagesFromURLResponse) Status() string {
	return pbupfur.rawResponse.Status
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (pbupfur PageBlobUploadPagesFromURLResponse) BlobSequenceNumber() int64 {
	s := pbupfur.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ContentMD5 returns the value for header Content-MD5.
func (pbupfur PageBlobUploadPagesFromURLResponse) ContentMD5() []byte {
	s := pbupfur.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (pbupfur PageBlobUploadPagesFromURLResponse) Date() time.Time {
	s := pbupfur.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (pbupfur PageBlobUploadPagesFromURLResponse) EncryptionKeySha256() string {
	return pbupfur.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (pbupfur PageBlobUploadPagesFromURLResponse) EncryptionScope() string {
	return pbupfur.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbupfur PageBlobUploadPagesFromURLResponse) ErrorCode() string {
	return pbupfur.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbupfur PageBlobUploadPagesFromURLResponse) ETag() ETag {
	return ETag(pbupfur.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (pbupfur PageBlobUploadPagesFromURLResponse) IsServerEncrypted() string {
	return pbupfur.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (pbupfur PageBlobUploadPagesFromURLResponse) LastModified() time.Time {
	s := pbupfur.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbupfur PageBlobUploadPagesFromURLResponse) RequestID() string {
	return pbupfur.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbupfur PageBlobUploadPagesFromURLResponse) Version() string {
	return pbupfur.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (pbupfur PageBlobUploadPagesFromURLResponse) XMsContentCrc64() []byte {
	s := pbupfur.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// PageBlobUploadPagesResponse ...
type PageBlobUploadPagesResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (pbupr PageBlobUploadPagesResponse) Response() *http.Response {
	return pbupr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pbupr PageBlobUploadPagesResponse) StatusCode() int {
	return pbupr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pbupr PageBlobUploadPagesResponse) Status() string {
	return pbupr.rawResponse.Status
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (pbupr PageBlobUploadPagesResponse) BlobSequenceNumber() int64 {
	s := pbupr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pbupr PageBlobUploadPagesResponse) ClientRequestID() string {
	return pbupr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentMD5 returns the value for header Content-MD5.
func (pbupr PageBlobUploadPagesResponse) ContentMD5() []byte {
	s := pbupr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// Date returns the value for header Date.
func (pbupr PageBlobUploadPagesResponse) Date() time.Time {
	s := pbupr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (pbupr PageBlobUploadPagesResponse) EncryptionKeySha256() string {
	return pbupr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (pbupr PageBlobUploadPagesResponse) EncryptionScope() string {
	return pbupr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (pbupr PageBlobUploadPagesResponse) ErrorCode() string {
	return pbupr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pbupr PageBlobUploadPagesResponse) ETag() ETag {
	return ETag(pbupr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-request-server-encrypted.
func (pbupr PageBlobUploadPagesResponse) IsServerEncrypted() string {
	return pbupr.rawResponse.Header.Get("x-ms-request-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (pbupr PageBlobUploadPagesResponse) LastModified() time.Time {
	s := pbupr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pbupr PageBlobUploadPagesResponse) RequestID() string {
	return pbupr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pbupr PageBlobUploadPagesResponse) Version() string {
	return pbupr.rawResponse.Header.Get("x-ms-version")
}

// XMsContentCrc64 returns the value for header x-ms-content-crc64.
func (pbupr PageBlobUploadPagesResponse) XMsContentCrc64() []byte {
	s := pbupr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// PageList - the list of pages
type PageList struct {
	rawResponse *http.Response
	PageRange   []PageRange  `xml:"PageRange"`
	ClearRange  []ClearRange `xml:"ClearRange"`
}

// Response returns the raw HTTP response object.
func (pl PageList) Response() *http.Response {
	return pl.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (pl PageList) StatusCode() int {
	return pl.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (pl PageList) Status() string {
	return pl.rawResponse.Status
}

// BlobContentLength returns the value for header x-ms-blob-content-length.
func (pl PageList) BlobContentLength() int64 {
	s := pl.rawResponse.Header.Get("x-ms-blob-content-length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (pl PageList) ClientRequestID() string {
	return pl.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (pl PageList) Date() time.Time {
	s := pl.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (pl PageList) ErrorCode() string {
	return pl.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (pl PageList) ETag() ETag {
	return ETag(pl.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (pl PageList) LastModified() time.Time {
	s := pl.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (pl PageList) RequestID() string {
	return pl.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (pl PageList) Version() string {
	return pl.rawResponse.Header.Get("x-ms-version")
}

// PageRange ...
type PageRange struct {
	Start int64 `xml:"Start"`
	End   int64 `xml:"End"`
}

// QueryFormat ...
type QueryFormat struct {
	// Type - Possible values include: 'QueryFormatDelimited', 'QueryFormatJSON', 'QueryFormatNone'
	Type                       QueryFormatType             `xml:"Type"`
	DelimitedTextConfiguration *DelimitedTextConfiguration `xml:"DelimitedTextConfiguration"`
	JSONTextConfiguration      *JSONTextConfiguration      `xml:"JsonTextConfiguration"`
}

// QueryRequest - the quick query body
type QueryRequest struct {
	// QueryType - the query type
	QueryType string `xml:"QueryType"`
	// Expression - a query statement
	Expression          string              `xml:"Expression"`
	InputSerialization  *QuerySerialization `xml:"InputSerialization"`
	OutputSerialization *QuerySerialization `xml:"OutputSerialization"`
}

// QueryResponse - Wraps the response from the blobClient.Query method.
type QueryResponse struct {
	rawResponse *http.Response
}

// NewMetadata returns user-defined key/value pairs.
func (qr QueryResponse) NewMetadata() Metadata {
	md := Metadata{}
	for k, v := range qr.rawResponse.Header {
		if len(k) > mdPrefixLen {
			if prefix := k[0:mdPrefixLen]; strings.EqualFold(prefix, mdPrefix) {
				md[strings.ToLower(k[mdPrefixLen:])] = v[0]
			}
		}
	}
	return md
}

// Response returns the raw HTTP response object.
func (qr QueryResponse) Response() *http.Response {
	return qr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (qr QueryResponse) StatusCode() int {
	return qr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (qr QueryResponse) Status() string {
	return qr.rawResponse.Status
}

// Body returns the raw HTTP response object's Body.
func (qr QueryResponse) Body() io.ReadCloser {
	return qr.rawResponse.Body
}

// AcceptRanges returns the value for header Accept-Ranges.
func (qr QueryResponse) AcceptRanges() string {
	return qr.rawResponse.Header.Get("Accept-Ranges")
}

// BlobCommittedBlockCount returns the value for header x-ms-blob-committed-block-count.
func (qr QueryResponse) BlobCommittedBlockCount() int32 {
	s := qr.rawResponse.Header.Get("x-ms-blob-committed-block-count")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 32)
	if err != nil {
		i = 0
	}
	return int32(i)
}

// BlobContentMD5 returns the value for header x-ms-blob-content-md5.
func (qr QueryResponse) BlobContentMD5() []byte {
	s := qr.rawResponse.Header.Get("x-ms-blob-content-md5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// BlobSequenceNumber returns the value for header x-ms-blob-sequence-number.
func (qr QueryResponse) BlobSequenceNumber() int64 {
	s := qr.rawResponse.Header.Get("x-ms-blob-sequence-number")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// BlobType returns the value for header x-ms-blob-type.
func (qr QueryResponse) BlobType() BlobType {
	return BlobType(qr.rawResponse.Header.Get("x-ms-blob-type"))
}

// CacheControl returns the value for header Cache-Control.
func (qr QueryResponse) CacheControl() string {
	return qr.rawResponse.Header.Get("Cache-Control")
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (qr QueryResponse) ClientRequestID() string {
	return qr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ContentCrc64 returns the value for header x-ms-content-crc64.
func (qr QueryResponse) ContentCrc64() []byte {
	s := qr.rawResponse.Header.Get("x-ms-content-crc64")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// ContentDisposition returns the value for header Content-Disposition.
func (qr QueryResponse) ContentDisposition() string {
	return qr.rawResponse.Header.Get("Content-Disposition")
}

// ContentEncoding returns the value for header Content-Encoding.
func (qr QueryResponse) ContentEncoding() string {
	return qr.rawResponse.Header.Get("Content-Encoding")
}

// ContentLanguage returns the value for header Content-Language.
func (qr QueryResponse) ContentLanguage() string {
	return qr.rawResponse.Header.Get("Content-Language")
}

// ContentLength returns the value for header Content-Length.
func (qr QueryResponse) ContentLength() int64 {
	s := qr.rawResponse.Header.Get("Content-Length")
	if s == "" {
		return -1
	}
	i, err := strconv.ParseInt(s, 10, 64)
	if err != nil {
		i = 0
	}
	return i
}

// ContentMD5 returns the value for header Content-MD5.
func (qr QueryResponse) ContentMD5() []byte {
	s := qr.rawResponse.Header.Get("Content-MD5")
	if s == "" {
		return nil
	}
	b, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		b = nil
	}
	return b
}

// ContentRange returns the value for header Content-Range.
func (qr QueryResponse) ContentRange() string {
	return qr.rawResponse.Header.Get("Content-Range")
}

// ContentType returns the value for header Content-Type.
func (qr QueryResponse) ContentType() string {
	return qr.rawResponse.Header.Get("Content-Type")
}

// CopyCompletionTime returns the value for header x-ms-copy-completion-time.
func (qr QueryResponse) CopyCompletionTime() time.Time {
	s := qr.rawResponse.Header.Get("x-ms-copy-completion-time")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// CopyID returns the value for header x-ms-copy-id.
func (qr QueryResponse) CopyID() string {
	return qr.rawResponse.Header.Get("x-ms-copy-id")
}

// CopyProgress returns the value for header x-ms-copy-progress.
func (qr QueryResponse) CopyProgress() string {
	return qr.rawResponse.Header.Get("x-ms-copy-progress")
}

// CopySource returns the value for header x-ms-copy-source.
func (qr QueryResponse) CopySource() string {
	return qr.rawResponse.Header.Get("x-ms-copy-source")
}

// CopyStatus returns the value for header x-ms-copy-status.
func (qr QueryResponse) CopyStatus() CopyStatusType {
	return CopyStatusType(qr.rawResponse.Header.Get("x-ms-copy-status"))
}

// CopyStatusDescription returns the value for header x-ms-copy-status-description.
func (qr QueryResponse) CopyStatusDescription() string {
	return qr.rawResponse.Header.Get("x-ms-copy-status-description")
}

// Date returns the value for header Date.
func (qr QueryResponse) Date() time.Time {
	s := qr.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// EncryptionKeySha256 returns the value for header x-ms-encryption-key-sha256.
func (qr QueryResponse) EncryptionKeySha256() string {
	return qr.rawResponse.Header.Get("x-ms-encryption-key-sha256")
}

// EncryptionScope returns the value for header x-ms-encryption-scope.
func (qr QueryResponse) EncryptionScope() string {
	return qr.rawResponse.Header.Get("x-ms-encryption-scope")
}

// ErrorCode returns the value for header x-ms-error-code.
func (qr QueryResponse) ErrorCode() string {
	return qr.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (qr QueryResponse) ETag() ETag {
	return ETag(qr.rawResponse.Header.Get("ETag"))
}

// IsServerEncrypted returns the value for header x-ms-server-encrypted.
func (qr QueryResponse) IsServerEncrypted() string {
	return qr.rawResponse.Header.Get("x-ms-server-encrypted")
}

// LastModified returns the value for header Last-Modified.
func (qr QueryResponse) LastModified() time.Time {
	s := qr.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// LeaseDuration returns the value for header x-ms-lease-duration.
func (qr QueryResponse) LeaseDuration() LeaseDurationType {
	return LeaseDurationType(qr.rawResponse.Header.Get("x-ms-lease-duration"))
}

// LeaseState returns the value for header x-ms-lease-state.
func (qr QueryResponse) LeaseState() LeaseStateType {
	return LeaseStateType(qr.rawResponse.Header.Get("x-ms-lease-state"))
}

// LeaseStatus returns the value for header x-ms-lease-status.
func (qr QueryResponse) LeaseStatus() LeaseStatusType {
	return LeaseStatusType(qr.rawResponse.Header.Get("x-ms-lease-status"))
}

// RequestID returns the value for header x-ms-request-id.
func (qr QueryResponse) RequestID() string {
	return qr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (qr QueryResponse) Version() string {
	return qr.rawResponse.Header.Get("x-ms-version")
}

// QuerySerialization ...
type QuerySerialization struct {
	Format QueryFormat `xml:"Format"`
}

// RetentionPolicy - the retention policy which determines how long the associated data should persist
type RetentionPolicy struct {
	// Enabled - Indicates whether a retention policy is enabled for the storage service
	Enabled bool `xml:"Enabled"`
	// Days - Indicates the number of days that metrics or logging or soft-deleted data should be retained. All data older than this value will be deleted
	Days *int32 `xml:"Days"`
}

// ServiceGetAccountInfoResponse ...
type ServiceGetAccountInfoResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (sgair ServiceGetAccountInfoResponse) Response() *http.Response {
	return sgair.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (sgair ServiceGetAccountInfoResponse) StatusCode() int {
	return sgair.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (sgair ServiceGetAccountInfoResponse) Status() string {
	return sgair.rawResponse.Status
}

// AccountKind returns the value for header x-ms-account-kind.
func (sgair ServiceGetAccountInfoResponse) AccountKind() AccountKindType {
	return AccountKindType(sgair.rawResponse.Header.Get("x-ms-account-kind"))
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (sgair ServiceGetAccountInfoResponse) ClientRequestID() string {
	return sgair.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (sgair ServiceGetAccountInfoResponse) Date() time.Time {
	s := sgair.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (sgair ServiceGetAccountInfoResponse) ErrorCode() string {
	return sgair.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (sgair ServiceGetAccountInfoResponse) RequestID() string {
	return sgair.rawResponse.Header.Get("x-ms-request-id")
}

// SkuName returns the value for header x-ms-sku-name.
func (sgair ServiceGetAccountInfoResponse) SkuName() SkuNameType {
	return SkuNameType(sgair.rawResponse.Header.Get("x-ms-sku-name"))
}

// Version returns the value for header x-ms-version.
func (sgair ServiceGetAccountInfoResponse) Version() string {
	return sgair.rawResponse.Header.Get("x-ms-version")
}

// ServiceSetPropertiesResponse ...
type ServiceSetPropertiesResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (sspr ServiceSetPropertiesResponse) Response() *http.Response {
	return sspr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (sspr ServiceSetPropertiesResponse) StatusCode() int {
	return sspr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (sspr ServiceSetPropertiesResponse) Status() string {
	return sspr.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (sspr ServiceSetPropertiesResponse) ClientRequestID() string {
	return sspr.rawResponse.Header.Get("x-ms-client-request-id")
}

// ErrorCode returns the value for header x-ms-error-code.
func (sspr ServiceSetPropertiesResponse) ErrorCode() string {
	return sspr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (sspr ServiceSetPropertiesResponse) RequestID() string {
	return sspr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (sspr ServiceSetPropertiesResponse) Version() string {
	return sspr.rawResponse.Header.Get("x-ms-version")
}

// SignedIdentifier - signed identifier
type SignedIdentifier struct {
	// ID - a unique id
	ID           string       `xml:"Id"`
	AccessPolicy AccessPolicy `xml:"AccessPolicy"`
}

// SignedIdentifiers - Wraps the response from the containerClient.GetAccessPolicy method.
type SignedIdentifiers struct {
	rawResponse *http.Response
	Items       []SignedIdentifier `xml:"SignedIdentifier"`
}

// Response returns the raw HTTP response object.
func (si SignedIdentifiers) Response() *http.Response {
	return si.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (si SignedIdentifiers) StatusCode() int {
	return si.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (si SignedIdentifiers) Status() string {
	return si.rawResponse.Status
}

// BlobPublicAccess returns the value for header x-ms-blob-public-access.
func (si SignedIdentifiers) BlobPublicAccess() PublicAccessType {
	return PublicAccessType(si.rawResponse.Header.Get("x-ms-blob-public-access"))
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (si SignedIdentifiers) ClientRequestID() string {
	return si.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (si SignedIdentifiers) Date() time.Time {
	s := si.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (si SignedIdentifiers) ErrorCode() string {
	return si.rawResponse.Header.Get("x-ms-error-code")
}

// ETag returns the value for header ETag.
func (si SignedIdentifiers) ETag() ETag {
	return ETag(si.rawResponse.Header.Get("ETag"))
}

// LastModified returns the value for header Last-Modified.
func (si SignedIdentifiers) LastModified() time.Time {
	s := si.rawResponse.Header.Get("Last-Modified")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// RequestID returns the value for header x-ms-request-id.
func (si SignedIdentifiers) RequestID() string {
	return si.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (si SignedIdentifiers) Version() string {
	return si.rawResponse.Header.Get("x-ms-version")
}

// StaticWebsite - The properties that enable an account to host a static website
type StaticWebsite struct {
	// Enabled - Indicates whether this account is hosting a static website
	Enabled bool `xml:"Enabled"`
	// IndexDocument - The default name of the index page under each directory
	IndexDocument *string `xml:"IndexDocument"`
	// ErrorDocument404Path - The absolute path of the custom 404 page
	ErrorDocument404Path *string `xml:"ErrorDocument404Path"`
	// DefaultIndexDocumentPath - Absolute path of the default index page
	DefaultIndexDocumentPath *string `xml:"DefaultIndexDocumentPath"`
}

// StorageServiceProperties - Storage Service Properties.
type StorageServiceProperties struct {
	rawResponse   *http.Response
	Logging       *Logging `xml:"Logging"`
	HourMetrics   *Metrics `xml:"HourMetrics"`
	MinuteMetrics *Metrics `xml:"MinuteMetrics"`
	// Cors - The set of CORS rules.
	Cors []CorsRule `xml:"Cors>CorsRule"`
	// DefaultServiceVersion - The default version to use for requests to the Blob service if an incoming request's version is not specified. Possible values include version 2008-10-27 and all more recent versions
	DefaultServiceVersion *string          `xml:"DefaultServiceVersion"`
	DeleteRetentionPolicy *RetentionPolicy `xml:"DeleteRetentionPolicy"`
	StaticWebsite         *StaticWebsite   `xml:"StaticWebsite"`
}

// Response returns the raw HTTP response object.
func (ssp StorageServiceProperties) Response() *http.Response {
	return ssp.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (ssp StorageServiceProperties) StatusCode() int {
	return ssp.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (ssp StorageServiceProperties) Status() string {
	return ssp.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (ssp StorageServiceProperties) ClientRequestID() string {
	return ssp.rawResponse.Header.Get("x-ms-client-request-id")
}

// ErrorCode returns the value for header x-ms-error-code.
func (ssp StorageServiceProperties) ErrorCode() string {
	return ssp.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (ssp StorageServiceProperties) RequestID() string {
	return ssp.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (ssp StorageServiceProperties) Version() string {
	return ssp.rawResponse.Header.Get("x-ms-version")
}

// StorageServiceStats - Stats for the storage service.
type StorageServiceStats struct {
	rawResponse    *http.Response
	GeoReplication *GeoReplication `xml:"GeoReplication"`
}

// Response returns the raw HTTP response object.
func (sss StorageServiceStats) Response() *http.Response {
	return sss.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (sss StorageServiceStats) StatusCode() int {
	return sss.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (sss StorageServiceStats) Status() string {
	return sss.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (sss StorageServiceStats) ClientRequestID() string {
	return sss.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (sss StorageServiceStats) Date() time.Time {
	s := sss.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (sss StorageServiceStats) ErrorCode() string {
	return sss.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (sss StorageServiceStats) RequestID() string {
	return sss.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (sss StorageServiceStats) Version() string {
	return sss.rawResponse.Header.Get("x-ms-version")
}

// SubmitBatchResponse - Wraps the response from the serviceClient.SubmitBatch method.
type SubmitBatchResponse struct {
	rawResponse *http.Response
}

// Response returns the raw HTTP response object.
func (sbr SubmitBatchResponse) Response() *http.Response {
	return sbr.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (sbr SubmitBatchResponse) StatusCode() int {
	return sbr.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (sbr SubmitBatchResponse) Status() string {
	return sbr.rawResponse.Status
}

// Body returns the raw HTTP response object's Body.
func (sbr SubmitBatchResponse) Body() io.ReadCloser {
	return sbr.rawResponse.Body
}

// ContentType returns the value for header Content-Type.
func (sbr SubmitBatchResponse) ContentType() string {
	return sbr.rawResponse.Header.Get("Content-Type")
}

// ErrorCode returns the value for header x-ms-error-code.
func (sbr SubmitBatchResponse) ErrorCode() string {
	return sbr.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (sbr SubmitBatchResponse) RequestID() string {
	return sbr.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (sbr SubmitBatchResponse) Version() string {
	return sbr.rawResponse.Header.Get("x-ms-version")
}

// UserDelegationKey - A user delegation key
type UserDelegationKey struct {
	rawResponse *http.Response
	// SignedOid - The Azure Active Directory object ID in GUID format.
	SignedOid string `xml:"SignedOid"`
	// SignedTid - The Azure Active Directory tenant ID in GUID format
	SignedTid string `xml:"SignedTid"`
	// SignedStart - The date-time the key is active
	SignedStart time.Time `xml:"SignedStart"`
	// SignedExpiry - The date-time the key expires
	SignedExpiry time.Time `xml:"SignedExpiry"`
	// SignedService - Abbreviation of the Azure Storage service that accepts the key
	SignedService string `xml:"SignedService"`
	// SignedVersion - The service version that created the key
	SignedVersion string `xml:"SignedVersion"`
	// Value - The key as a base64 string
	Value string `xml:"Value"`
}

// MarshalXML implements the xml.Marshaler interface for UserDelegationKey.
func (udk UserDelegationKey) MarshalXML(e *xml.Encoder, start xml.StartElement) error {
	udk2 := (*userDelegationKey)(unsafe.Pointer(&udk))
	return e.EncodeElement(*udk2, start)
}

// UnmarshalXML implements the xml.Unmarshaler interface for UserDelegationKey.
func (udk *UserDelegationKey) UnmarshalXML(d *xml.Decoder, start xml.StartElement) error {
	udk2 := (*userDelegationKey)(unsafe.Pointer(udk))
	return d.DecodeElement(udk2, &start)
}

// Response returns the raw HTTP response object.
func (udk UserDelegationKey) Response() *http.Response {
	return udk.rawResponse
}

// StatusCode returns the HTTP status code of the response, e.g. 200.
func (udk UserDelegationKey) StatusCode() int {
	return udk.rawResponse.StatusCode
}

// Status returns the HTTP status message of the response, e.g. "200 OK".
func (udk UserDelegationKey) Status() string {
	return udk.rawResponse.Status
}

// ClientRequestID returns the value for header x-ms-client-request-id.
func (udk UserDelegationKey) ClientRequestID() string {
	return udk.rawResponse.Header.Get("x-ms-client-request-id")
}

// Date returns the value for header Date.
func (udk UserDelegationKey) Date() time.Time {
	s := udk.rawResponse.Header.Get("Date")
	if s == "" {
		return time.Time{}
	}
	t, err := time.Parse(time.RFC1123, s)
	if err != nil {
		t = time.Time{}
	}
	return t
}

// ErrorCode returns the value for header x-ms-error-code.
func (udk UserDelegationKey) ErrorCode() string {
	return udk.rawResponse.Header.Get("x-ms-error-code")
}

// RequestID returns the value for header x-ms-request-id.
func (udk UserDelegationKey) RequestID() string {
	return udk.rawResponse.Header.Get("x-ms-request-id")
}

// Version returns the value for header x-ms-version.
func (udk UserDelegationKey) Version() string {
	return udk.rawResponse.Header.Get("x-ms-version")
}

func init() {
	if reflect.TypeOf((*UserDelegationKey)(nil)).Elem().Size() != reflect.TypeOf((*userDelegationKey)(nil)).Elem().Size() {
		validateError(errors.New("size mismatch between UserDelegationKey and userDelegationKey"))
	}
	if reflect.TypeOf((*AccessPolicy)(nil)).Elem().Size() != reflect.TypeOf((*accessPolicy)(nil)).Elem().Size() {
		validateError(errors.New("size mismatch between AccessPolicy and accessPolicy"))
	}
	if reflect.TypeOf((*BlobProperties)(nil)).Elem().Size() != reflect.TypeOf((*blobProperties)(nil)).Elem().Size() {
		validateError(errors.New("size mismatch between BlobProperties and blobProperties"))
	}
	if reflect.TypeOf((*ContainerProperties)(nil)).Elem().Size() != reflect.TypeOf((*containerProperties)(nil)).Elem().Size() {
		validateError(errors.New("size mismatch between ContainerProperties and containerProperties"))
	}
	if reflect.TypeOf((*GeoReplication)(nil)).Elem().Size() != reflect.TypeOf((*geoReplication)(nil)).Elem().Size() {
		validateError(errors.New("size mismatch between GeoReplication and geoReplication"))
	}
}

const (
	rfc3339Format = "2006-01-02T15:04:05Z" //This was wrong in the generated code, FYI
)

// used to convert times from UTC to GMT before sending across the wire
var gmt = time.FixedZone("GMT", 0)

// internal type used for marshalling time in RFC1123 format
type timeRFC1123 struct {
	time.Time
}

// MarshalText implements the encoding.TextMarshaler interface for timeRFC1123.
func (t timeRFC1123) MarshalText() ([]byte, error) {
	return []byte(t.Format(time.RFC1123)), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for timeRFC1123.
func (t *timeRFC1123) UnmarshalText(data []byte) (err error) {
	t.Time, err = time.Parse(time.RFC1123, string(data))
	return
}

// internal type used for marshalling time in RFC3339 format
type timeRFC3339 struct {
	time.Time
}

// MarshalText implements the encoding.TextMarshaler interface for timeRFC3339.
func (t timeRFC3339) MarshalText() ([]byte, error) {
	return []byte(t.Format(rfc3339Format)), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for timeRFC3339.
func (t *timeRFC3339) UnmarshalText(data []byte) (err error) {
	t.Time, err = time.Parse(rfc3339Format, string(data))
	return
}

// internal type used for marshalling base64 encoded strings
type base64Encoded struct {
	b []byte
}

// MarshalText implements the encoding.TextMarshaler interface for base64Encoded.
func (c base64Encoded) MarshalText() ([]byte, error) {
	return []byte(base64.StdEncoding.EncodeToString(c.b)), nil
}

// UnmarshalText implements the encoding.TextUnmarshaler interface for base64Encoded.
func (c *base64Encoded) UnmarshalText(data []byte) error {
	b, err := base64.StdEncoding.DecodeString(string(data))
	if err != nil {
		return err
	}
	c.b = b
	return nil
}

// internal type used for marshalling
type userDelegationKey struct {
	rawResponse   *http.Response
	SignedOid     string      `xml:"SignedOid"`
	SignedTid     string      `xml:"SignedTid"`
	SignedStart   timeRFC3339 `xml:"SignedStart"`
	SignedExpiry  timeRFC3339 `xml:"SignedExpiry"`
	SignedService string      `xml:"SignedService"`
	SignedVersion string      `xml:"SignedVersion"`
	Value         string      `xml:"Value"`
}

// internal type used for marshalling
type accessPolicy struct {
	Start      *timeRFC3339 `xml:"Start"`
	Expiry     *timeRFC3339 `xml:"Expiry"`
	Permission *string      `xml:"Permission"`
}

// internal type used for marshalling
type blobProperties struct {
	// XMLName is used for marshalling and is subject to removal in a future release.
	XMLName                   xml.Name              `xml:"Properties"`
	CreationTime              *timeRFC1123          `xml:"Creation-Time"`
	LastModified              timeRFC1123           `xml:"Last-Modified"`
	Etag                      ETag                  `xml:"Etag"`
	ContentLength             *int64                `xml:"Content-Length"`
	ContentType               *string               `xml:"Content-Type"`
	ContentEncoding           *string               `xml:"Content-Encoding"`
	ContentLanguage           *string               `xml:"Content-Language"`
	ContentMD5                base64Encoded         `xml:"Content-MD5"`
	ContentDisposition        *string               `xml:"Content-Disposition"`
	CacheControl              *string               `xml:"Cache-Control"`
	BlobSequenceNumber        *int64                `xml:"x-ms-blob-sequence-number"`
	BlobType                  BlobType              `xml:"BlobType"`
	LeaseStatus               LeaseStatusType       `xml:"LeaseStatus"`
	LeaseState                LeaseStateType        `xml:"LeaseState"`
	LeaseDuration             LeaseDurationType     `xml:"LeaseDuration"`
	CopyID                    *string               `xml:"CopyId"`
	CopyStatus                CopyStatusType        `xml:"CopyStatus"`
	CopySource                *string               `xml:"CopySource"`
	CopyProgress              *string               `xml:"CopyProgress"`
	CopyCompletionTime        *timeRFC1123          `xml:"CopyCompletionTime"`
	CopyStatusDescription     *string               `xml:"CopyStatusDescription"`
	ServerEncrypted           *bool                 `xml:"ServerEncrypted"`
	IncrementalCopy           *bool                 `xml:"IncrementalCopy"`
	DestinationSnapshot       *string               `xml:"DestinationSnapshot"`
	DeletedTime               *timeRFC1123          `xml:"DeletedTime"`
	RemainingRetentionDays    *int32                `xml:"RemainingRetentionDays"`
	AccessTier                AccessTierType        `xml:"AccessTier"`
	AccessTierInferred        *bool                 `xml:"AccessTierInferred"`
	ArchiveStatus             ArchiveStatusType     `xml:"ArchiveStatus"`
	CustomerProvidedKeySha256 *string               `xml:"CustomerProvidedKeySha256"`
	EncryptionScope           *string               `xml:"EncryptionScope"`
	AccessTierChangeTime      *timeRFC1123          `xml:"AccessTierChangeTime"`
	TagCount                  *int32                `xml:"TagCount"`
	ExpiresOn                 *timeRFC1123          `xml:"Expiry-Time"`
	IsSealed                  *bool                 `xml:"IsSealed"`
	RehydratePriority         RehydratePriorityType `xml:"RehydratePriority"`
}

// internal type used for marshalling
type containerProperties struct {
	LastModified                   timeRFC1123       `xml:"Last-Modified"`
	Etag                           ETag              `xml:"Etag"`
	LeaseStatus                    LeaseStatusType   `xml:"LeaseStatus"`
	LeaseState                     LeaseStateType    `xml:"LeaseState"`
	LeaseDuration                  LeaseDurationType `xml:"LeaseDuration"`
	PublicAccess                   PublicAccessType  `xml:"PublicAccess"`
	HasImmutabilityPolicy          *bool             `xml:"HasImmutabilityPolicy"`
	HasLegalHold                   *bool             `xml:"HasLegalHold"`
	DefaultEncryptionScope         *string           `xml:"DefaultEncryptionScope"`
	PreventEncryptionScopeOverride *bool             `xml:"DenyEncryptionScopeOverride"`
	DeletedTime                    *timeRFC1123      `xml:"DeletedTime"`
	RemainingRetentionDays         *int32            `xml:"RemainingRetentionDays"`
}

// internal type used for marshalling
type geoReplication struct {
	Status       GeoReplicationStatusType `xml:"Status"`
	LastSyncTime timeRFC1123              `xml:"LastSyncTime"`
}
