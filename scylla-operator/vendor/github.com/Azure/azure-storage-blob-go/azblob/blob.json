{"swagger": "2.0", "info": {"title": "Azure Blob Storage", "version": "2018-11-09", "x-ms-code-generation-settings": {"header": "MIT", "strictSpecAdherence": false}}, "x-ms-parameterized-host": {"hostTemplate": "{url}", "useSchemePrefix": false, "positionInOperation": "first", "parameters": [{"$ref": "#/parameters/Url"}]}, "securityDefinitions": {"blob_shared_key": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "schemes": ["https"], "consumes": ["application/xml"], "produces": ["application/xml"], "paths": {}, "x-ms-paths": {"/?restype=service&comp=properties": {"put": {"tags": ["service"], "operationId": "Service_SetProperties", "description": "Sets properties for a storage account's Blob service endpoint, including properties for Storage Analytics and CORS (Cross-Origin Resource Sharing) rules", "parameters": [{"$ref": "#/parameters/StorageServiceProperties"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "Success (Accepted)", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "get": {"tags": ["service"], "operationId": "Service_GetProperties", "description": "gets the properties of a storage account's Blob service, including properties for Storage Analytics and CORS (Cross-Origin Resource Sharing) rules.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}}, "schema": {"$ref": "#/definitions/StorageServiceProperties"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["service"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/?restype=service&comp=stats": {"get": {"tags": ["service"], "operationId": "Service_GetStatistics", "description": "Retrieves statistics related to replication for the Blob service. It is only available on the secondary location endpoint when read-access geo-redundant replication is enabled for the storage account.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/StorageServiceStats"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["service"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["stats"]}]}, "/?comp=list": {"get": {"tags": ["service"], "operationId": "Service_ListContainersSegment", "description": "The List Containers Segment operation returns a list of the containers under the specified account", "parameters": [{"$ref": "#/parameters/Prefix"}, {"$ref": "#/parameters/Marker"}, {"$ref": "#/parameters/MaxResults"}, {"$ref": "#/parameters/ListContainersInclude"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}}, "schema": {"$ref": "#/definitions/ListContainersSegmentResponse"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}, "x-ms-pageable": {"nextLinkName": "NextMarker"}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["list"]}]}, "/?restype=service&comp=userdelegationkey": {"post": {"tags": ["service"], "operationId": "Service_GetUserDelegationKey", "description": "Retrieves a user delgation key for the Blob service. This is only a valid operation when using bearer token authentication.", "parameters": [{"$ref": "#/parameters/KeyInfo"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/UserDelegationKey"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["service"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["userdelegationkey"]}]}, "/?restype=account&comp=properties": {"get": {"tags": ["service"], "operationId": "Service_GetAccountInfo", "description": "Returns the sku name and account kind ", "parameters": [{"$ref": "#/parameters/ApiVersionParameter"}], "responses": {"200": {"description": "Success (OK)", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-sku-name": {"x-ms-client-name": "SkuName", "type": "string", "enum": ["Standard_LRS", "Standard_GRS", "Standard_RAGRS", "Standard_ZRS", "Premium_LRS"], "x-ms-enum": {"name": "SkuName", "modelAsString": false}, "description": "Identifies the sku name of the account"}, "x-ms-account-kind": {"x-ms-client-name": "Account<PERSON><PERSON>", "type": "string", "enum": ["Storage", "BlobStorage", "StorageV2"], "x-ms-enum": {"name": "Account<PERSON><PERSON>", "modelAsString": false}, "description": "Identifies the account kind"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["account"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/{containerName}?restype=container": {"put": {"tags": ["container"], "operationId": "Container_Create", "description": "creates a new container under the specified account. If the container with the same name already exists, the operation fails", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/BlobPublicAccess"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "Success, Container created.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "get": {"tags": ["container"], "operationId": "Container_GetProperties", "description": "returns all user-defined metadata and system properties for the specified container. The data returned does not include the container's list of blobs", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success", "headers": {"x-ms-meta": {"type": "string", "x-ms-client-name": "<PERSON><PERSON><PERSON>", "x-ms-header-collection-prefix": "x-ms-meta-"}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-duration": {"x-ms-client-name": "LeaseDuration", "description": "When a blob is leased, specifies whether the lease is of infinite or fixed duration.", "type": "string", "enum": ["infinite", "fixed"], "x-ms-enum": {"name": "LeaseDurationType", "modelAsString": false}}, "x-ms-lease-state": {"x-ms-client-name": "LeaseState", "description": "Lease state of the blob.", "type": "string", "enum": ["available", "leased", "expired", "breaking", "broken"], "x-ms-enum": {"name": "LeaseStateType", "modelAsString": false}}, "x-ms-lease-status": {"x-ms-client-name": "LeaseStatus", "description": "The current lease status of the blob.", "type": "string", "enum": ["locked", "unlocked"], "x-ms-enum": {"name": "LeaseStatusType", "modelAsString": false}}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-blob-public-access": {"x-ms-client-name": "BlobPublicAccess", "description": "Indicated whether data in the container may be accessed publicly and the level of access", "type": "string", "enum": ["container", "blob"], "x-ms-enum": {"name": "PublicAccessType", "modelAsString": true}}, "x-ms-has-immutability-policy": {"x-ms-client-name": "HasImmutabilityPolicy", "description": "Indicates whether the container has an immutability policy set on it.", "type": "boolean"}, "x-ms-has-legal-hold": {"x-ms-client-name": "HasLegalHold", "description": "Indicates whether the container has a legal hold.", "type": "boolean"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "delete": {"tags": ["container"], "operationId": "Container_Delete", "description": "operation marks the specified container for deletion. The container and any blobs contained within it are later deleted during garbage collection", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "Accepted", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}]}, "/{containerName}?restype=container&comp=metadata": {"put": {"tags": ["container"], "operationId": "Container_SetMetadata", "description": "operation sets one or more user-defined name-value pairs for the specified container.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["metadata"]}]}, "/{containerName}?restype=container&comp=acl": {"get": {"tags": ["container"], "operationId": "Container_GetAccessPolicy", "description": "gets the permissions for the specified container. The permissions indicate whether container data may be accessed publicly.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success", "headers": {"x-ms-blob-public-access": {"x-ms-client-name": "BlobPublicAccess", "description": "Indicated whether data in the container may be accessed publicly and the level of access", "type": "string", "enum": ["container", "blob"], "x-ms-enum": {"name": "PublicAccessType", "modelAsString": true}}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/SignedIdentifiers"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "put": {"tags": ["container"], "operationId": "Container_SetAccessPolicy", "description": "sets the permissions for the specified container. The permissions indicate whether blobs in a container may be accessed publicly.", "parameters": [{"$ref": "#/parameters/ContainerAcl"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobPublicAccess"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["acl"]}]}, "/{containerName}?comp=lease&restype=container&acquire": {"put": {"tags": ["container"], "operationId": "Container_AcquireLease", "description": "[Update] establishes and manages a lock on a container for delete operations. The lock duration can be 15 to 60 seconds, or can be infinite", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseDuration"}, {"$ref": "#/parameters/ProposedLeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The Acquire operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-id": {"x-ms-client-name": "LeaseId", "type": "string", "description": "Uniquely identifies a container's lease"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["acquire"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}?comp=lease&restype=container&release": {"put": {"tags": ["container"], "operationId": "Container_ReleaseLease", "description": "[Update] establishes and manages a lock on a container for delete operations. The lock duration can be 15 to 60 seconds, or can be infinite", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdRequired"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Release operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["release"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}?comp=lease&restype=container&renew": {"put": {"tags": ["container"], "operationId": "Container_RenewLease", "description": "[Update] establishes and manages a lock on a container for delete operations. The lock duration can be 15 to 60 seconds, or can be infinite", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdRequired"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Renew operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-id": {"x-ms-client-name": "LeaseId", "type": "string", "description": "Uniquely identifies a container's lease"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["renew"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}?comp=lease&restype=container&break": {"put": {"tags": ["container"], "operationId": "Container_BreakLease", "description": "[Update] establishes and manages a lock on a container for delete operations. The lock duration can be 15 to 60 seconds, or can be infinite", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseBreakPeriod"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "The Break operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-time": {"x-ms-client-name": "LeaseTime", "type": "integer", "description": "Approximate time remaining in the lease period, in seconds."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["break"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}?comp=lease&restype=container&change": {"put": {"tags": ["container"], "operationId": "Container_ChangeLease", "description": "[Update] establishes and manages a lock on a container for delete operations. The lock duration can be 15 to 60 seconds, or can be infinite", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdRequired"}, {"$ref": "#/parameters/ProposedLeaseIdRequired"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Change operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-id": {"x-ms-client-name": "LeaseId", "type": "string", "description": "Uniquely identifies a container's lease"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["change"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}?restype=container&comp=list&flat": {"get": {"tags": ["containers"], "operationId": "Container_ListBlobFlatSegment", "description": "[Update] The List Blobs operation returns a list of the blobs under the specified container", "parameters": [{"$ref": "#/parameters/Prefix"}, {"$ref": "#/parameters/Marker"}, {"$ref": "#/parameters/MaxResults"}, {"$ref": "#/parameters/ListBlobsInclude"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"Content-Type": {"type": "string", "description": "The media type of the body of the response. For List Blobs this is 'application/xml'"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/ListBlobsFlatSegmentResponse"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}, "x-ms-pageable": {"nextLinkName": "NextMarker"}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["list"]}]}, "/{containerName}?restype=container&comp=list&hierarchy": {"get": {"tags": ["containers"], "operationId": "Container_ListBlobHierarchySegment", "description": "[Update] The List Blobs operation returns a list of the blobs under the specified container", "parameters": [{"$ref": "#/parameters/Prefix"}, {"$ref": "#/parameters/Delimiter"}, {"$ref": "#/parameters/Marker"}, {"$ref": "#/parameters/MaxResults"}, {"$ref": "#/parameters/ListBlobsInclude"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Success.", "headers": {"Content-Type": {"type": "string", "description": "The media type of the body of the response. For List Blobs this is 'application/xml'"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/ListBlobsHierarchySegmentResponse"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}, "x-ms-pageable": {"nextLinkName": "NextMarker"}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["container"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["list"]}]}, "/{containerName}?restype=account&comp=properties": {"get": {"tags": ["container"], "operationId": "Container_GetAccountInfo", "description": "Returns the sku name and account kind ", "parameters": [{"$ref": "#/parameters/ApiVersionParameter"}], "responses": {"200": {"description": "Success (OK)", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-sku-name": {"x-ms-client-name": "SkuName", "type": "string", "enum": ["Standard_LRS", "Standard_GRS", "Standard_RAGRS", "Standard_ZRS", "Premium_LRS"], "x-ms-enum": {"name": "SkuName", "modelAsString": false}, "description": "Identifies the sku name of the account"}, "x-ms-account-kind": {"x-ms-client-name": "Account<PERSON><PERSON>", "type": "string", "enum": ["Storage", "BlobStorage", "StorageV2"], "x-ms-enum": {"name": "Account<PERSON><PERSON>", "modelAsString": false}, "description": "Identifies the account kind"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["account"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/{containerName}/{blob}": {"get": {"tags": ["blob"], "operationId": "Blob_Download", "description": "The Download operation reads or downloads a blob from the system, including its metadata and properties. You can also call Download to read a snapshot.", "parameters": [{"$ref": "#/parameters/Snapshot"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Range"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/GetRangeContentMD5"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Returns the content of the entire blob.", "headers": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-meta": {"type": "string", "x-ms-client-name": "<PERSON><PERSON><PERSON>", "x-ms-header-collection-prefix": "x-ms-meta-"}, "Content-Length": {"type": "integer", "format": "int64", "description": "The number of bytes present in the response body."}, "Content-Type": {"type": "string", "description": "The media type of the body of the response. For Download Blob this is 'application/octet-stream'"}, "Content-Range": {"type": "string", "description": "Indicates the range of bytes returned in the event that the client requested a subset of the blob by setting the 'Range' request header."}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "Content-Encoding": {"type": "string", "description": "This header returns the value that was specified for the Content-Encoding request header"}, "Cache-Control": {"type": "string", "description": "This header is returned if it was previously specified for the blob."}, "Content-Disposition": {"type": "string", "description": "This header returns the value that was specified for the 'x-ms-blob-content-disposition' header. The Content-Disposition response header field conveys additional information about how to process the response payload, and also can be used to attach additional metadata. For example, if set to attachment, it indicates that the user-agent should not display the response, but instead show a Save As dialog with a filename other than the blob name specified."}, "Content-Language": {"type": "string", "description": "This header returns the value that was specified for the Content-Language request header."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for a page blob. This header is not returned for block blobs or append blobs"}, "x-ms-blob-type": {"x-ms-client-name": "BlobType", "description": "The blob's type.", "type": "string", "enum": ["BlockBlob", "PageBlob", "AppendBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}, "x-ms-copy-completion-time": {"x-ms-client-name": "CopyCompletionTime", "type": "string", "format": "date-time-rfc1123", "description": "Conclusion time of the last attempted Copy Blob operation where this blob was the destination blob. This value can specify the time of a completed, aborted, or failed copy attempt. This header does not appear if a copy is pending, if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List."}, "x-ms-copy-status-description": {"x-ms-client-name": "CopyStatusDescription", "type": "string", "description": "Only appears when x-ms-copy-status is failed or pending. Describes the cause of the last fatal or non-fatal copy operation failure. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List"}, "x-ms-copy-id": {"x-ms-client-name": "CopyId", "type": "string", "description": "String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy."}, "x-ms-copy-progress": {"x-ms-client-name": "CopyProgress", "type": "string", "description": "Contains the number of bytes copied and the total bytes in the source in the last attempted Copy Blob operation where this blob was the destination blob. Can show between 0 and Content-Length bytes copied. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List"}, "x-ms-copy-source": {"x-ms-client-name": "CopySource", "type": "string", "description": "URL up to 2 KB in length that specifies the source blob or file used in the last attempted Copy Blob operation where this blob was the destination blob. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List."}, "x-ms-copy-status": {"x-ms-client-name": "CopyStatus", "description": "State of the copy operation identified by x-ms-copy-id.", "type": "string", "enum": ["pending", "success", "aborted", "failed"], "x-ms-enum": {"name": "CopyStatusType", "modelAsString": false}}, "x-ms-lease-duration": {"x-ms-client-name": "LeaseDuration", "description": "When a blob is leased, specifies whether the lease is of infinite or fixed duration.", "type": "string", "enum": ["infinite", "fixed"], "x-ms-enum": {"name": "LeaseDurationType", "modelAsString": false}}, "x-ms-lease-state": {"x-ms-client-name": "LeaseState", "description": "Lease state of the blob.", "type": "string", "enum": ["available", "leased", "expired", "breaking", "broken"], "x-ms-enum": {"name": "LeaseStateType", "modelAsString": false}}, "x-ms-lease-status": {"x-ms-client-name": "LeaseStatus", "description": "The current lease status of the blob.", "type": "string", "enum": ["locked", "unlocked"], "x-ms-enum": {"name": "LeaseStatusType", "modelAsString": false}}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Accept-Ranges": {"type": "string", "description": "Indicates that the service supports requests for partial blob content."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-blob-committed-block-count": {"x-ms-client-name": "BlobCommittedBlockCount", "type": "integer", "description": "The number of committed blocks present in the blob. This header is returned only for append blobs."}, "x-ms-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the blob data and application metadata are completely encrypted using the specified algorithm. Otherwise, the value is set to false (when the blob is unencrypted, or if only parts of the blob/application metadata are encrypted)."}, "x-ms-blob-content-md5": {"x-ms-client-name": "BlobContentMD5", "type": "string", "format": "byte", "description": "If the blob has a MD5 hash, and if request contains range header (Range or x-ms-range), this response header is returned with the value of the whole blob's MD5 value. This value may or may not be equal to the value returned in Content-MD5 header, with the latter calculated from the requested range"}}, "schema": {"type": "object", "format": "file"}}, "206": {"description": "Returns the content of a specified range of the blob.", "headers": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-meta": {"type": "string", "x-ms-client-name": "<PERSON><PERSON><PERSON>", "x-ms-header-collection-prefix": "x-ms-meta-"}, "Content-Length": {"type": "integer", "format": "int64", "description": "The number of bytes present in the response body."}, "Content-Type": {"type": "string", "description": "The media type of the body of the response. For Download Blob this is 'application/octet-stream'"}, "Content-Range": {"type": "string", "description": "Indicates the range of bytes returned in the event that the client requested a subset of the blob by setting the 'Range' request header."}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "Content-Encoding": {"type": "string", "description": "This header returns the value that was specified for the Content-Encoding request header"}, "Cache-Control": {"type": "string", "description": "This header is returned if it was previously specified for the blob."}, "Content-Disposition": {"type": "string", "description": "This header returns the value that was specified for the 'x-ms-blob-content-disposition' header. The Content-Disposition response header field conveys additional information about how to process the response payload, and also can be used to attach additional metadata. For example, if set to attachment, it indicates that the user-agent should not display the response, but instead show a Save As dialog with a filename other than the blob name specified."}, "Content-Language": {"type": "string", "description": "This header returns the value that was specified for the Content-Language request header."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for a page blob. This header is not returned for block blobs or append blobs"}, "x-ms-blob-type": {"x-ms-client-name": "BlobType", "description": "The blob's type.", "type": "string", "enum": ["BlockBlob", "PageBlob", "AppendBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}, "x-ms-copy-completion-time": {"x-ms-client-name": "CopyCompletionTime", "type": "string", "format": "date-time-rfc1123", "description": "Conclusion time of the last attempted Copy Blob operation where this blob was the destination blob. This value can specify the time of a completed, aborted, or failed copy attempt. This header does not appear if a copy is pending, if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List."}, "x-ms-copy-status-description": {"x-ms-client-name": "CopyStatusDescription", "type": "string", "description": "Only appears when x-ms-copy-status is failed or pending. Describes the cause of the last fatal or non-fatal copy operation failure. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List"}, "x-ms-copy-id": {"x-ms-client-name": "CopyId", "type": "string", "description": "String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy."}, "x-ms-copy-progress": {"x-ms-client-name": "CopyProgress", "type": "string", "description": "Contains the number of bytes copied and the total bytes in the source in the last attempted Copy Blob operation where this blob was the destination blob. Can show between 0 and Content-Length bytes copied. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List"}, "x-ms-copy-source": {"x-ms-client-name": "CopySource", "type": "string", "description": "URL up to 2 KB in length that specifies the source blob or file used in the last attempted Copy Blob operation where this blob was the destination blob. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List."}, "x-ms-copy-status": {"x-ms-client-name": "CopyStatus", "description": "State of the copy operation identified by x-ms-copy-id.", "type": "string", "enum": ["pending", "success", "aborted", "failed"], "x-ms-enum": {"name": "CopyStatusType", "modelAsString": false}}, "x-ms-lease-duration": {"x-ms-client-name": "LeaseDuration", "description": "When a blob is leased, specifies whether the lease is of infinite or fixed duration.", "type": "string", "enum": ["infinite", "fixed"], "x-ms-enum": {"name": "LeaseDurationType", "modelAsString": false}}, "x-ms-lease-state": {"x-ms-client-name": "LeaseState", "description": "Lease state of the blob.", "type": "string", "enum": ["available", "leased", "expired", "breaking", "broken"], "x-ms-enum": {"name": "LeaseStateType", "modelAsString": false}}, "x-ms-lease-status": {"x-ms-client-name": "LeaseStatus", "description": "The current lease status of the blob.", "type": "string", "enum": ["locked", "unlocked"], "x-ms-enum": {"name": "LeaseStatusType", "modelAsString": false}}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Accept-Ranges": {"type": "string", "description": "Indicates that the service supports requests for partial blob content."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-blob-committed-block-count": {"x-ms-client-name": "BlobCommittedBlockCount", "type": "integer", "description": "The number of committed blocks present in the blob. This header is returned only for append blobs."}, "x-ms-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the blob data and application metadata are completely encrypted using the specified algorithm. Otherwise, the value is set to false (when the blob is unencrypted, or if only parts of the blob/application metadata are encrypted)."}, "x-ms-blob-content-md5": {"x-ms-client-name": "BlobContentMD5", "type": "string", "format": "byte", "description": "If the blob has a MD5 hash, and if request contains range header (Range or x-ms-range), this response header is returned with the value of the whole blob's MD5 value. This value may or may not be equal to the value returned in Content-MD5 header, with the latter calculated from the requested range"}}, "schema": {"type": "object", "format": "file"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "head": {"tags": ["blob"], "operationId": "Blob_GetProperties", "description": "The Get Properties operation returns all user-defined metadata, standard HTTP properties, and system properties for the blob. It does not return the content of the blob.", "parameters": [{"$ref": "#/parameters/Snapshot"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Returns the properties of the blob.", "headers": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-creation-time": {"x-ms-client-name": "CreationTime", "type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was created."}, "x-ms-meta": {"type": "string", "x-ms-client-name": "<PERSON><PERSON><PERSON>", "x-ms-header-collection-prefix": "x-ms-meta-"}, "x-ms-blob-type": {"x-ms-client-name": "BlobType", "description": "The blob's type.", "type": "string", "enum": ["BlockBlob", "PageBlob", "AppendBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}, "x-ms-copy-completion-time": {"x-ms-client-name": "CopyCompletionTime", "type": "string", "format": "date-time-rfc1123", "description": "Conclusion time of the last attempted Copy Blob operation where this blob was the destination blob. This value can specify the time of a completed, aborted, or failed copy attempt. This header does not appear if a copy is pending, if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List."}, "x-ms-copy-status-description": {"x-ms-client-name": "CopyStatusDescription", "type": "string", "description": "Only appears when x-ms-copy-status is failed or pending. Describes the cause of the last fatal or non-fatal copy operation failure. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List"}, "x-ms-copy-id": {"x-ms-client-name": "CopyId", "type": "string", "description": "String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy."}, "x-ms-copy-progress": {"x-ms-client-name": "CopyProgress", "type": "string", "description": "Contains the number of bytes copied and the total bytes in the source in the last attempted Copy Blob operation where this blob was the destination blob. Can show between 0 and Content-Length bytes copied. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List"}, "x-ms-copy-source": {"x-ms-client-name": "CopySource", "type": "string", "description": "URL up to 2 KB in length that specifies the source blob or file used in the last attempted Copy Blob operation where this blob was the destination blob. This header does not appear if this blob has never been the destination in a Copy Blob operation, or if this blob has been modified after a concluded Copy Blob operation using Set Blob Properties, Put Blob, or Put Block List."}, "x-ms-copy-status": {"x-ms-client-name": "CopyStatus", "description": "State of the copy operation identified by x-ms-copy-id.", "type": "string", "enum": ["pending", "success", "aborted", "failed"], "x-ms-enum": {"name": "CopyStatusType", "modelAsString": false}}, "x-ms-incremental-copy": {"x-ms-client-name": "IsIncrementalCopy", "type": "boolean", "description": "Included if the blob is incremental copy blob."}, "x-ms-copy-destination-snapshot": {"x-ms-client-name": "DestinationSnapshot", "type": "string", "description": "Included if the blob is incremental copy blob or incremental copy snapshot, if x-ms-copy-status is success. Snapshot time of the last successful incremental copy snapshot for this blob."}, "x-ms-lease-duration": {"x-ms-client-name": "LeaseDuration", "description": "When a blob is leased, specifies whether the lease is of infinite or fixed duration.", "type": "string", "enum": ["infinite", "fixed"], "x-ms-enum": {"name": "LeaseDurationType", "modelAsString": false}}, "x-ms-lease-state": {"x-ms-client-name": "LeaseState", "description": "Lease state of the blob.", "type": "string", "enum": ["available", "leased", "expired", "breaking", "broken"], "x-ms-enum": {"name": "LeaseStateType", "modelAsString": false}}, "x-ms-lease-status": {"x-ms-client-name": "LeaseStatus", "description": "The current lease status of the blob.", "type": "string", "enum": ["locked", "unlocked"], "x-ms-enum": {"name": "LeaseStatusType", "modelAsString": false}}, "Content-Length": {"type": "integer", "format": "int64", "description": "The number of bytes present in the response body."}, "Content-Type": {"type": "string", "description": "The content type specified for the blob. The default content type is 'application/octet-stream'"}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "Content-Encoding": {"type": "string", "description": "This header returns the value that was specified for the Content-Encoding request header"}, "Content-Disposition": {"type": "string", "description": "This header returns the value that was specified for the 'x-ms-blob-content-disposition' header. The Content-Disposition response header field conveys additional information about how to process the response payload, and also can be used to attach additional metadata. For example, if set to attachment, it indicates that the user-agent should not display the response, but instead show a Save As dialog with a filename other than the blob name specified."}, "Content-Language": {"type": "string", "description": "This header returns the value that was specified for the Content-Language request header."}, "Cache-Control": {"type": "string", "description": "This header is returned if it was previously specified for the blob."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for a page blob. This header is not returned for block blobs or append blobs"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "Accept-Ranges": {"type": "string", "description": "Indicates that the service supports requests for partial blob content."}, "x-ms-blob-committed-block-count": {"x-ms-client-name": "BlobCommittedBlockCount", "type": "integer", "description": "The number of committed blocks present in the blob. This header is returned only for append blobs."}, "x-ms-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the blob data and application metadata are completely encrypted using the specified algorithm. Otherwise, the value is set to false (when the blob is unencrypted, or if only parts of the blob/application metadata are encrypted)."}, "x-ms-access-tier": {"x-ms-client-name": "AccessTier", "type": "string", "description": "The tier of page blob on a premium storage account or tier of block blob on blob storage LRS accounts. For a list of allowed premium page blob tiers, see https://docs.microsoft.com/en-us/azure/virtual-machines/windows/premium-storage#features. For blob storage LRS accounts, valid values are Hot/Cool/Archive."}, "x-ms-access-tier-inferred": {"x-ms-client-name": "AccessTierInferred", "type": "boolean", "description": "For page blobs on a premium storage account only. If the access tier is not explicitly set on the blob, the tier is inferred based on its content length and this header will be returned with true value."}, "x-ms-archive-status": {"x-ms-client-name": "ArchiveStatus", "type": "string", "description": "For blob storage LRS accounts, valid values are rehydrate-pending-to-hot/rehydrate-pending-to-cool. If the blob is being rehydrated and is not complete then this header is returned indicating that rehydrate is pending and also tells the destination tier."}, "x-ms-access-tier-change-time": {"x-ms-client-name": "AccessTierChangeTime", "type": "string", "format": "date-time-rfc1123", "description": "The time the tier was changed on the object. This is only returned if the tier on the block blob was ever set."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "delete": {"tags": ["blob"], "operationId": "Blob_Delete", "description": "If the storage account's soft delete feature is disabled then, when a blob is deleted, it is permanently removed from the storage account. If the storage account's soft delete feature is enabled, then, when a blob is deleted, it is marked for deletion and becomes inaccessible immediately. However, the blob service retains the blob or snapshot for the number of days specified by the DeleteRetentionPolicy section of [Storage service properties] (Set-Blob-Service-Properties.md). After the specified number of days has passed, the blob's data is permanently removed from the storage account. Note that you continue to be charged for the soft-deleted blob's storage until it is permanently removed. Use the List Blobs API and specify the \"include=deleted\" query parameter to discover which blobs and snapshots have been soft deleted. You can then use the Undelete Blob API to restore a soft-deleted blob. All other operations on a soft-deleted blob or snapshot causes the service to return an HTTP status code of 404 (ResourceNotFound).", "parameters": [{"$ref": "#/parameters/Snapshot"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/DeleteSnapshots"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "The delete request was accepted and the blob will be deleted.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}}, "/{containerName}/{blob}?PageBlob": {"put": {"tags": ["blob"], "operationId": "PageBlob_Create", "description": "The Create operation creates a new page blob.", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/BlobContentType"}, {"$ref": "#/parameters/BlobContentEncoding"}, {"$ref": "#/parameters/BlobContentLanguage"}, {"$ref": "#/parameters/BlobContentMD5"}, {"$ref": "#/parameters/BlobCacheControl"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobContentDisposition"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/BlobContentLengthRequired"}, {"$ref": "#/parameters/BlobSequenceNumber"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The blob was created.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "x-ms-blob-type", "x-ms-client-name": "blobType", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Specifies the type of blob to create: block blob, page blob, or append blob.", "type": "string", "enum": ["PageBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}]}, "/{containerName}/{blob}?AppendBlob": {"put": {"tags": ["blob"], "operationId": "AppendBlob_Create", "description": "The Create Append Blob operation creates a new append blob.", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/BlobContentType"}, {"$ref": "#/parameters/BlobContentEncoding"}, {"$ref": "#/parameters/BlobContentLanguage"}, {"$ref": "#/parameters/BlobContentMD5"}, {"$ref": "#/parameters/BlobCacheControl"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobContentDisposition"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The blob was created.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "x-ms-blob-type", "x-ms-client-name": "blobType", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Specifies the type of blob to create: block blob, page blob, or append blob.", "type": "string", "enum": ["AppendBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}]}, "/{containerName}/{blob}?BlockBlob": {"put": {"tags": ["blob"], "operationId": "BlockBlob_Upload", "description": "The Upload Block Blob operation updates the content of an existing block blob. Updating an existing block blob overwrites any existing metadata on the blob. Partial updates are not supported with Put Blob; the content of the existing blob is overwritten with the content of the new blob. To perform a partial update of the content of a block blob, use the Put Block List operation.", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/Body"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/BlobContentType"}, {"$ref": "#/parameters/BlobContentEncoding"}, {"$ref": "#/parameters/BlobContentLanguage"}, {"$ref": "#/parameters/BlobContentMD5"}, {"$ref": "#/parameters/BlobCacheControl"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobContentDisposition"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The blob was updated.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "x-ms-blob-type", "x-ms-client-name": "blobType", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Specifies the type of blob to create: block blob, page blob, or append blob.", "type": "string", "enum": ["BlockBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}]}, "/{containerName}/{blob}?comp=undelete": {"put": {"tags": ["blob"], "operationId": "Blob_Undelete", "description": "Undelete a blob that was previously soft deleted", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The blob was undeleted successfully.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["undelete"]}]}, "/{containerName}/{blob}?comp=properties&SetHTTPHeaders": {"put": {"tags": ["blob"], "operationId": "Blob_SetHTTPHeaders", "description": "The Set HTTP Headers operation sets system properties on the blob", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/BlobCacheControl"}, {"$ref": "#/parameters/BlobContentType"}, {"$ref": "#/parameters/BlobContentMD5"}, {"$ref": "#/parameters/BlobContentEncoding"}, {"$ref": "#/parameters/BlobContentLanguage"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/BlobContentDisposition"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The properties were set successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for a page blob. This header is not returned for block blobs or append blobs"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/{containerName}/{blob}?comp=metadata": {"put": {"tags": ["blob"], "operationId": "Blob_SetMetadata", "description": "The Set Blob Metadata operation sets user-defined metadata for the specified blob as one or more name-value pairs", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The metadata was set successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["metadata"]}]}, "/{containerName}/{blob}?comp=lease&acquire": {"put": {"tags": ["blob"], "operationId": "Blob_AcquireLease", "description": "[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete operations", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseDuration"}, {"$ref": "#/parameters/ProposedLeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The Acquire operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-id": {"x-ms-client-name": "LeaseId", "type": "string", "description": "Uniquely identifies a blobs's lease"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["acquire"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}/{blob}?comp=lease&release": {"put": {"tags": ["blob"], "operationId": "Blob_ReleaseLease", "description": "[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete operations", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdRequired"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Release operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["release"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}/{blob}?comp=lease&renew": {"put": {"tags": ["blob"], "operationId": "Blob_RenewLease", "description": "[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete operations", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdRequired"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Renew operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-id": {"x-ms-client-name": "LeaseId", "type": "string", "description": "Uniquely identifies a blobs's lease"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["renew"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}/{blob}?comp=lease&change": {"put": {"tags": ["blob"], "operationId": "Blob_ChangeLease", "description": "[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete operations", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdRequired"}, {"$ref": "#/parameters/ProposedLeaseIdRequired"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Change operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-lease-id": {"x-ms-client-name": "LeaseId", "type": "string", "description": "Uniquely identifies a blobs's lease"}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["change"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}/{blob}?comp=lease&break": {"put": {"tags": ["blob"], "operationId": "Blob_BreakLease", "description": "[Update] The Lease Blob operation establishes and manages a lock on a blob for write and delete operations", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseBreakPeriod"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "The Break operation completed successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the blob was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-lease-time": {"x-ms-client-name": "LeaseTime", "type": "integer", "description": "Approximate time remaining in the lease period, in seconds."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["lease"]}, {"name": "x-ms-lease-action", "x-ms-client-name": "action", "in": "header", "required": true, "type": "string", "enum": ["break"], "x-ms-enum": {"name": "LeaseAction", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Describes what lease action to take."}]}, "/{containerName}/{blob}?comp=snapshot": {"put": {"tags": ["blob"], "operationId": "Blob_CreateSnapshot", "description": "The Create Snapshot operation creates a read-only snapshot of a blob", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The snaptshot was taken successfully.", "headers": {"x-ms-snapshot": {"x-ms-client-name": "Snapshot", "type": "string", "description": "Uniquely identifies the snapshot and indicates the snapshot version. It may be used in subsequent requests to access the snapshot"}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["snapshot"]}]}, "/{containerName}/{blob}?comp=copy": {"put": {"tags": ["blob"], "operationId": "Blob_StartCopyFromURL", "description": "The Start Copy From URL operation copies a blob or an internet resource to a new blob.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/SourceIfModifiedSince"}, {"$ref": "#/parameters/SourceIfUnmodifiedSince"}, {"$ref": "#/parameters/SourceIfMatch"}, {"$ref": "#/parameters/SourceIfNoneMatch"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/CopySource"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "The copy blob has been accepted with the specified copy status.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-copy-id": {"x-ms-client-name": "CopyId", "type": "string", "description": "String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy."}, "x-ms-copy-status": {"x-ms-client-name": "CopyStatus", "description": "State of the copy operation identified by x-ms-copy-id.", "type": "string", "enum": ["pending", "success", "aborted", "failed"], "x-ms-enum": {"name": "CopyStatusType", "modelAsString": false}}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": []}, "/{containerName}/{blob}?comp=copy&sync": {"put": {"tags": ["blob"], "operationId": "Blob_CopyFromURL", "description": "The Copy From URL operation copies a blob or an internet resource to a new blob. It will not return a response until the copy is complete.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/SourceIfModifiedSince"}, {"$ref": "#/parameters/SourceIfUnmodifiedSince"}, {"$ref": "#/parameters/SourceIfMatch"}, {"$ref": "#/parameters/SourceIfNoneMatch"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/CopySource"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "The copy has completed.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-copy-id": {"x-ms-client-name": "CopyId", "type": "string", "description": "String identifier for this copy operation."}, "x-ms-copy-status": {"x-ms-client-name": "CopyStatus", "description": "State of the copy operation identified by x-ms-copy-id.", "type": "string", "enum": ["success"], "x-ms-enum": {"name": "SyncCopyStatusType", "modelAsString": false}}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "x-ms-requires-sync", "in": "header", "required": true, "type": "string", "enum": ["true"]}]}, "/{containerName}/{blob}?comp=copy&copyid={CopyId}": {"put": {"tags": ["blob"], "operationId": "Blob_AbortCopyFromURL", "description": "The Abort Copy From URL operation aborts a pending Copy From URL operation, and leaves a destination blob with zero length and full metadata.", "parameters": [{"$ref": "#/parameters/CopyId"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"204": {"description": "The delete request was accepted and the blob will be deleted.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["copy"]}, {"name": "x-ms-copy-action", "x-ms-client-name": "copyActionAbortConstant", "in": "header", "required": true, "type": "string", "enum": ["abort"], "x-ms-parameter-location": "method"}]}, "/{containerName}/{blob}?comp=tier": {"put": {"tags": ["blobs"], "operationId": "Blob_SetTier", "description": "The Set Tier operation sets the tier on a blob. The operation is allowed on a page blob in a premium storage account and on a block blob in a blob storage account (locally redundant storage only). A premium page blob's tier determines the allowed size, IOPS, and bandwidth of the blob. A block blob's tier determines Hot/Cool/Archive storage type. This operation does not update the blob's ETag.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/AccessTier"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}, {"$ref": "#/parameters/LeaseIdOptional"}], "responses": {"200": {"description": "The new tier will take effect immediately.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and newer."}}}, "202": {"description": "The transition to the new tier is pending.", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and newer."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["tier"]}]}, "/{containerName}/{blob}?restype=account&comp=properties": {"get": {"tags": ["blob"], "operationId": "Blob_GetAccountInfo", "description": "Returns the sku name and account kind ", "parameters": [{"$ref": "#/parameters/ApiVersionParameter"}], "responses": {"200": {"description": "Success (OK)", "headers": {"x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-sku-name": {"x-ms-client-name": "SkuName", "type": "string", "enum": ["Standard_LRS", "Standard_GRS", "Standard_RAGRS", "Standard_ZRS", "Premium_LRS"], "x-ms-enum": {"name": "SkuName", "modelAsString": false}, "description": "Identifies the sku name of the account"}, "x-ms-account-kind": {"x-ms-client-name": "Account<PERSON><PERSON>", "type": "string", "enum": ["Storage", "BlobStorage", "StorageV2"], "x-ms-enum": {"name": "Account<PERSON><PERSON>", "modelAsString": false}, "description": "Identifies the account kind"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "restype", "in": "query", "required": true, "type": "string", "enum": ["account"]}, {"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/{containerName}/{blob}?comp=block": {"put": {"tags": ["blockblob"], "operationId": "BlockBlob_StageBlock", "description": "The Stage Block operation creates a new block to be committed as part of a blob", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/BlockId"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/ContentMD5"}, {"$ref": "#/parameters/Body"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The block was created.", "headers": {"Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["block"]}]}, "/{containerName}/{blob}?comp=block&fromURL": {"put": {"tags": ["blockblob"], "operationId": "BlockBlob_StageBlockFromURL", "description": "The Stage Block operation creates a new block to be committed as part of a blob where the contents are read from a URL.", "parameters": [{"$ref": "#/parameters/BlockId"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/SourceUrl"}, {"$ref": "#/parameters/SourceRange"}, {"$ref": "#/parameters/SourceContentMD5"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/SourceIfModifiedSince"}, {"$ref": "#/parameters/SourceIfUnmodifiedSince"}, {"$ref": "#/parameters/SourceIfMatch"}, {"$ref": "#/parameters/SourceIfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The block was created.", "headers": {"Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["block"]}]}, "/{containerName}/{blob}?comp=blocklist": {"put": {"tags": ["blockblob"], "operationId": "BlockBlob_CommitBlockList", "description": "The Commit Block List operation writes a blob by specifying the list of block IDs that make up the blob. In order to be written as part of a blob, a block must have been successfully written to the server in a prior Put Block operation. You can call Put Block List to update a blob by uploading only those blocks that have changed, then committing the new and existing blocks together. You can do this by specifying whether to commit a block from the committed block list or from the uncommitted block list, or to commit the most recently uploaded version of the block, whichever list it may belong to.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/BlobCacheControl"}, {"$ref": "#/parameters/BlobContentType"}, {"$ref": "#/parameters/BlobContentEncoding"}, {"$ref": "#/parameters/BlobContentLanguage"}, {"$ref": "#/parameters/BlobContentMD5"}, {"$ref": "#/parameters/Metadata"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobContentDisposition"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"name": "blocks", "in": "body", "required": true, "schema": {"$ref": "#/definitions/BlockLookupList"}}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The block list was recorded.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "get": {"tags": ["blockblob"], "operationId": "BlockBlob_GetBlockList", "description": "The Get Block List operation retrieves the list of blocks that have been uploaded as part of a block blob", "parameters": [{"$ref": "#/parameters/Snapshot"}, {"$ref": "#/parameters/BlockListType"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The page range was written.", "headers": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Content-Type": {"type": "string", "description": "The media type of the body of the response. For Get Block List this is 'application/xml'"}, "x-ms-blob-content-length": {"x-ms-client-name": "BlobContentLength", "type": "integer", "format": "int64", "description": "The size of the blob in bytes."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/BlockList"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["blocklist"]}]}, "/{containerName}/{blob}?comp=page&update": {"put": {"tags": ["pageblob"], "operationId": "PageBlob_UploadPages", "description": "The Upload Pages operation writes a range of pages to a page blob", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/Body"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/ContentMD5"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Range"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfSequenceNumberLessThanOrEqualTo"}, {"$ref": "#/parameters/IfSequenceNumberLessThan"}, {"$ref": "#/parameters/IfSequenceNumberEqualTo"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The page range was written.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for the page blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["page"]}, {"name": "x-ms-page-write", "x-ms-client-name": "pageWrite", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Required. You may specify one of the following options:\n  - Update: Writes the bytes specified by the request body into the specified range. The Range and Content-Length headers must match to perform the update.\n  - Clear: Clears the specified range and releases the space used in storage for that range. To clear a range, set the Content-Length header to zero, and the Range header to a value that indicates the range to clear, up to maximum blob size.", "type": "string", "enum": ["update"], "x-ms-enum": {"name": "PageWriteType", "modelAsString": false}}]}, "/{containerName}/{blob}?comp=page&clear": {"put": {"tags": ["pageblob"], "operationId": "PageBlob_ClearPages", "description": "The Clear Pages operation clears a set of pages from a page blob", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Range"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfSequenceNumberLessThanOrEqualTo"}, {"$ref": "#/parameters/IfSequenceNumberLessThan"}, {"$ref": "#/parameters/IfSequenceNumberEqualTo"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The page range was cleared.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for the page blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["page"]}, {"name": "x-ms-page-write", "x-ms-client-name": "pageWrite", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Required. You may specify one of the following options:\n  - Update: Writes the bytes specified by the request body into the specified range. The Range and Content-Length headers must match to perform the update.\n  - Clear: Clears the specified range and releases the space used in storage for that range. To clear a range, set the Content-Length header to zero, and the Range header to a value that indicates the range to clear, up to maximum blob size.", "type": "string", "enum": ["clear"], "x-ms-enum": {"name": "PageWriteType", "modelAsString": false}}]}, "/{containerName}/{blob}?comp=page&update&fromUrl": {"put": {"tags": ["pageblob"], "operationId": "PageBlob_UploadPagesFromURL", "description": "The Upload Pages operation writes a range of pages to a page blob where the contents are read from a URL", "consumes": ["application/octet-stream"], "parameters": [{"$ref": "#/parameters/SourceUrl"}, {"$ref": "#/parameters/SourceRangeRequiredPutPageFromUrl"}, {"$ref": "#/parameters/SourceContentMD5"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/RangeRequiredPutPageFromUrl"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfSequenceNumberLessThanOrEqualTo"}, {"$ref": "#/parameters/IfSequenceNumberLessThan"}, {"$ref": "#/parameters/IfSequenceNumberEqualTo"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/SourceIfModifiedSince"}, {"$ref": "#/parameters/SourceIfUnmodifiedSince"}, {"$ref": "#/parameters/SourceIfMatch"}, {"$ref": "#/parameters/SourceIfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The page range was written.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for the page blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["page"]}, {"name": "x-ms-page-write", "x-ms-client-name": "pageWrite", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Required. You may specify one of the following options:\n  - Update: Writes the bytes specified by the request body into the specified range. The Range and Content-Length headers must match to perform the update.\n  - Clear: Clears the specified range and releases the space used in storage for that range. To clear a range, set the Content-Length header to zero, and the Range header to a value that indicates the range to clear, up to maximum blob size.", "type": "string", "enum": ["update"], "x-ms-enum": {"name": "PageWriteType", "modelAsString": false}}]}, "/{containerName}/{blob}?comp=pagelist": {"get": {"tags": ["pageblob"], "operationId": "PageBlob_GetPageRanges", "description": "The Get Page Ranges operation returns the list of valid page ranges for a page blob or snapshot of a page blob", "parameters": [{"$ref": "#/parameters/Snapshot"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/Range"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Information on the page blob was found.", "headers": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "x-ms-blob-content-length": {"x-ms-client-name": "BlobContentLength", "type": "integer", "format": "int64", "description": "The size of the blob in bytes."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/PageList"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["pagelist"]}]}, "/{containerName}/{blob}?comp=pagelist&diff": {"get": {"tags": ["pageblob"], "operationId": "PageBlob_GetPageRangesDiff", "description": "The Get Page Ranges Diff operation returns the list of valid page ranges for a page blob that were changed between target blob and previous snapshot.", "parameters": [{"$ref": "#/parameters/Snapshot"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/PrevSnapshot"}, {"$ref": "#/parameters/Range"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "Information on the page blob was found.", "headers": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "x-ms-blob-content-length": {"x-ms-client-name": "BlobContentLength", "type": "integer", "format": "int64", "description": "The size of the blob in bytes."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}, "schema": {"$ref": "#/definitions/PageList"}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["pagelist"]}]}, "/{containerName}/{blob}?comp=properties&Resize": {"put": {"tags": ["pageblob"], "operationId": "PageBlob_Resize", "description": "Resize the Blob", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/BlobContentLengthRequired"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The Blob was resized successfully", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for a page blob. This header is not returned for block blobs or append blobs"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/{containerName}/{blob}?comp=properties&UpdateSequenceNumber": {"put": {"tags": ["pageblob"], "operationId": "PageBlob_UpdateSequenceNumber", "description": "Update the sequence number of the blob", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/SequenceNumberAction"}, {"$ref": "#/parameters/BlobSequenceNumber"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"200": {"description": "The sequence numbers were updated successfully.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-blob-sequence-number": {"x-ms-client-name": "BlobSequenceNumber", "type": "integer", "format": "int64", "description": "The current sequence number for a page blob. This header is not returned for block blobs or append blobs"}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["properties"]}]}, "/{containerName}/{blob}?comp=incrementalcopy": {"put": {"tags": ["pageblob"], "operationId": "PageBlob_CopyIncremental", "description": "The Copy Incremental operation copies a snapshot of the source page blob to a destination page blob. The snapshot is copied such that only the differential changes between the previously copied snapshot are transferred to the destination. The copied snapshots are complete copies of the original snapshot and can be read or copied from as usual. This API is supported since REST version 2016-05-31.", "parameters": [{"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/CopySource"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"202": {"description": "The blob was copied.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-copy-id": {"x-ms-client-name": "CopyId", "type": "string", "description": "String identifier for this copy operation. Use with Get Blob Properties to check the status of this copy operation, or pass to Abort Copy Blob to abort a pending copy."}, "x-ms-copy-status": {"x-ms-client-name": "CopyStatus", "description": "State of the copy operation identified by x-ms-copy-id.", "type": "string", "enum": ["pending", "success", "aborted", "failed"], "x-ms-enum": {"name": "CopyStatusType", "modelAsString": false}}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["incrementalcopy"]}]}, "/{containerName}/{blob}?comp=appendblock": {"put": {"tags": ["appendblob"], "consumes": ["application/octet-stream"], "operationId": "AppendBlob_AppendBlock", "description": "The Append Block operation commits a new block of data to the end of an existing append blob. The Append Block operation is permitted only if the blob was created with x-ms-blob-type set to AppendBlob. Append Block is supported only on version 2015-02-21 version or later.", "parameters": [{"$ref": "#/parameters/Body"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/ContentMD5"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobConditionMaxSize"}, {"$ref": "#/parameters/BlobConditionAppendPos"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The block was created.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-blob-append-offset": {"x-ms-client-name": "BlobAppendOffset", "type": "string", "description": "This response header is returned only for append operations. It returns the offset at which the block was committed, in bytes."}, "x-ms-blob-committed-block-count": {"x-ms-client-name": "BlobCommittedBlockCount", "type": "integer", "description": "The number of committed blocks present in the blob. This header is returned only for append blobs."}, "x-ms-request-server-encrypted": {"x-ms-client-name": "IsServerEncrypted", "type": "boolean", "description": "The value of this header is set to true if the contents of the request are successfully encrypted using the specified algorithm, and false otherwise."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["appendblock"]}]}, "/{containerName}/{blob}?comp=appendblock&fromUrl": {"put": {"tags": ["appendblob"], "operationId": "AppendBlob_AppendBlockFromUrl", "description": "The Append Block operation commits a new block of data to the end of an existing append blob where the contents are read from a source url. The Append Block operation is permitted only if the blob was created with x-ms-blob-type set to AppendBlob. Append Block is supported only on version 2015-02-21 version or later.", "parameters": [{"$ref": "#/parameters/SourceUrl"}, {"$ref": "#/parameters/SourceRange"}, {"$ref": "#/parameters/SourceContentMD5"}, {"$ref": "#/parameters/Timeout"}, {"$ref": "#/parameters/ContentLength"}, {"$ref": "#/parameters/LeaseIdOptional"}, {"$ref": "#/parameters/BlobConditionMaxSize"}, {"$ref": "#/parameters/BlobConditionAppendPos"}, {"$ref": "#/parameters/IfModifiedSince"}, {"$ref": "#/parameters/IfUnmodifiedSince"}, {"$ref": "#/parameters/IfMatch"}, {"$ref": "#/parameters/IfNoneMatch"}, {"$ref": "#/parameters/SourceIfModifiedSince"}, {"$ref": "#/parameters/SourceIfUnmodifiedSince"}, {"$ref": "#/parameters/SourceIfMatch"}, {"$ref": "#/parameters/SourceIfNoneMatch"}, {"$ref": "#/parameters/ApiVersionParameter"}, {"$ref": "#/parameters/ClientRequestId"}], "responses": {"201": {"description": "The block was created.", "headers": {"ETag": {"type": "string", "format": "etag", "description": "The ETag contains a value that you can use to perform operations conditionally. If the request version is 2011-08-18 or newer, the ETag value will be in quotes."}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123", "description": "Returns the date and time the container was last modified. Any operation that modifies the blob, including an update of the blob's metadata or properties, changes the last-modified time of the blob."}, "Content-MD5": {"type": "string", "format": "byte", "description": "If the blob has an MD5 hash and this operation is to read the full blob, this response header is returned so that the client can check for message content integrity."}, "x-ms-request-id": {"x-ms-client-name": "RequestId", "type": "string", "description": "This header uniquely identifies the request that was made and can be used for troubleshooting the request."}, "x-ms-version": {"x-ms-client-name": "Version", "type": "string", "description": "Indicates the version of the Blob service used to execute the request. This header is returned for requests made against version 2009-09-19 and above."}, "Date": {"type": "string", "format": "date-time-rfc1123", "description": "UTC date/time value generated by the service that indicates the time at which the response was initiated"}, "x-ms-blob-append-offset": {"x-ms-client-name": "BlobAppendOffset", "type": "string", "description": "This response header is returned only for append operations. It returns the offset at which the block was committed, in bytes."}, "x-ms-blob-committed-block-count": {"x-ms-client-name": "BlobCommittedBlockCount", "type": "integer", "description": "The number of committed blocks present in the blob. This header is returned only for append blobs."}}}, "default": {"description": "Failure", "headers": {"x-ms-error-code": {"x-ms-client-name": "ErrorCode", "type": "string"}}, "schema": {"$ref": "#/definitions/StorageError"}}}}, "parameters": [{"name": "comp", "in": "query", "required": true, "type": "string", "enum": ["appendblock"]}]}}, "definitions": {"KeyInfo": {"type": "object", "required": ["Start", "Expiry"], "description": "Key information", "properties": {"Start": {"description": "The date-time the key is active in ISO 8601 UTC time", "type": "string"}, "Expiry": {"description": "The date-time the key expires in ISO 8601 UTC time", "type": "string"}}}, "UserDelegationKey": {"type": "object", "required": ["SignedOid", "SignedTid", "SignedStart", "SignedExpiry", "SignedService", "SignedVersion", "Value"], "description": "A user delegation key", "properties": {"SignedOid": {"description": "The Azure Active Directory object ID in GUID format.", "type": "string"}, "SignedTid": {"description": "The Azure Active Directory tenant ID in GUID format", "type": "string"}, "SignedStart": {"description": "The date-time the key is active", "type": "string", "format": "date-time"}, "SignedExpiry": {"description": "The date-time the key expires", "type": "string", "format": "date-time"}, "SignedService": {"description": "Abbreviation of the Azure Storage service that accepts the key", "type": "string"}, "SignedVersion": {"description": "The service version that created the key", "type": "string"}, "Value": {"description": "The key as a base64 string", "type": "string"}}}, "PublicAccessType": {"type": "string", "enum": ["container", "blob"], "x-ms-enum": {"name": "PublicAccessType", "modelAsString": true}}, "CopyStatus": {"type": "string", "enum": ["pending", "success", "aborted", "failed"], "x-ms-enum": {"name": "CopyStatusType", "modelAsString": false}}, "LeaseDuration": {"type": "string", "enum": ["infinite", "fixed"], "x-ms-enum": {"name": "LeaseDurationType", "modelAsString": false}}, "LeaseState": {"type": "string", "enum": ["available", "leased", "expired", "breaking", "broken"], "x-ms-enum": {"name": "LeaseStateType", "modelAsString": false}}, "LeaseStatus": {"type": "string", "enum": ["locked", "unlocked"], "x-ms-enum": {"name": "LeaseStatusType", "modelAsString": false}}, "StorageError": {"type": "object", "properties": {"Code": {"type": "string"}, "Message": {"type": "string"}}}, "AccessPolicy": {"type": "object", "required": ["Start", "Expiry", "Permission"], "description": "An Access policy", "properties": {"Start": {"description": "the date-time the policy is active", "type": "string", "format": "date-time"}, "Expiry": {"description": "the date-time the policy expires", "type": "string", "format": "date-time"}, "Permission": {"description": "the permissions for the acl policy", "type": "string"}}}, "AccessTier": {"type": "string", "enum": ["P4", "P6", "P10", "P20", "P30", "P40", "P50", "Hot", "Cool", "Archive"], "x-ms-enum": {"name": "AccessTier", "modelAsString": true}}, "ArchiveStatus": {"type": "string", "enum": ["rehydrate-pending-to-hot", "rehydrate-pending-to-cool"], "x-ms-enum": {"name": "ArchiveStatus", "modelAsString": true}}, "BlobItem": {"xml": {"name": "Blob"}, "description": "An Azure Storage blob", "type": "object", "required": ["Name", "Deleted", "Snapshot", "Properties"], "properties": {"Name": {"type": "string"}, "Deleted": {"type": "boolean"}, "Snapshot": {"type": "string"}, "Properties": {"$ref": "#/definitions/BlobProperties"}, "Metadata": {"$ref": "#/definitions/Metadata"}}}, "BlobProperties": {"xml": {"name": "Properties"}, "description": "Properties of a blob", "type": "object", "required": ["Etag", "Last-Modified"], "properties": {"Creation-Time": {"type": "string", "format": "date-time-rfc1123"}, "Last-Modified": {"type": "string", "format": "date-time-rfc1123"}, "Etag": {"type": "string", "format": "etag"}, "Content-Length": {"type": "integer", "format": "int64", "description": "Size in bytes"}, "Content-Type": {"type": "string"}, "Content-Encoding": {"type": "string"}, "Content-Language": {"type": "string"}, "Content-MD5": {"type": "string", "format": "byte"}, "Content-Disposition": {"type": "string"}, "Cache-Control": {"type": "string"}, "x-ms-blob-sequence-number": {"x-ms-client-name": "blobSequenceNumber", "type": "integer", "format": "int64"}, "BlobType": {"type": "string", "enum": ["BlockBlob", "PageBlob", "AppendBlob"], "x-ms-enum": {"name": "BlobType", "modelAsString": false}}, "LeaseStatus": {"$ref": "#/definitions/LeaseStatus"}, "LeaseState": {"$ref": "#/definitions/LeaseState"}, "LeaseDuration": {"$ref": "#/definitions/LeaseDuration"}, "CopyId": {"type": "string"}, "CopyStatus": {"$ref": "#/definitions/CopyStatus"}, "CopySource": {"type": "string"}, "CopyProgress": {"type": "string"}, "CopyCompletionTime": {"type": "string", "format": "date-time-rfc1123"}, "CopyStatusDescription": {"type": "string"}, "ServerEncrypted": {"type": "boolean"}, "IncrementalCopy": {"type": "boolean"}, "DestinationSnapshot": {"type": "string"}, "DeletedTime": {"type": "string", "format": "date-time-rfc1123"}, "RemainingRetentionDays": {"type": "integer"}, "AccessTier": {"$ref": "#/definitions/AccessTier"}, "AccessTierInferred": {"type": "boolean"}, "ArchiveStatus": {"$ref": "#/definitions/ArchiveStatus"}, "AccessTierChangeTime": {"type": "string", "format": "date-time-rfc1123"}}}, "ListBlobsFlatSegmentResponse": {"xml": {"name": "EnumerationResults"}, "description": "An enumeration of blobs", "type": "object", "required": ["ServiceEndpoint", "ContainerName", "Segment"], "properties": {"ServiceEndpoint": {"type": "string", "xml": {"attribute": true}}, "ContainerName": {"type": "string", "xml": {"attribute": true}}, "Prefix": {"type": "string"}, "Marker": {"type": "string"}, "MaxResults": {"type": "integer"}, "Delimiter": {"type": "string"}, "Segment": {"$ref": "#/definitions/BlobFlatListSegment"}, "NextMarker": {"type": "string"}}}, "ListBlobsHierarchySegmentResponse": {"xml": {"name": "EnumerationResults"}, "description": "An enumeration of blobs", "type": "object", "required": ["ServiceEndpoint", "ContainerName", "Segment"], "properties": {"ServiceEndpoint": {"type": "string", "xml": {"attribute": true}}, "ContainerName": {"type": "string", "xml": {"attribute": true}}, "Prefix": {"type": "string"}, "Marker": {"type": "string"}, "MaxResults": {"type": "integer"}, "Delimiter": {"type": "string"}, "Segment": {"$ref": "#/definitions/BlobHierarchyListSegment"}, "NextMarker": {"type": "string"}}}, "BlobFlatListSegment": {"xml": {"name": "Blobs"}, "required": ["BlobItems"], "type": "object", "properties": {"BlobItems": {"type": "array", "items": {"$ref": "#/definitions/BlobItem"}}}}, "BlobHierarchyListSegment": {"xml": {"name": "Blobs"}, "type": "object", "required": ["BlobItems"], "properties": {"BlobPrefixes": {"type": "array", "items": {"$ref": "#/definitions/BlobPrefix"}}, "BlobItems": {"type": "array", "items": {"$ref": "#/definitions/BlobItem"}}}}, "BlobPrefix": {"type": "object", "required": ["Name"], "properties": {"Name": {"type": "string"}}}, "Block": {"type": "object", "required": ["Name", "Size"], "description": "Represents a single block in a block blob.  It describes the block's ID and size.", "properties": {"Name": {"description": "The base64 encoded block ID.", "type": "string"}, "Size": {"description": "The block size in bytes.", "type": "integer"}}}, "BlockList": {"type": "object", "properties": {"CommittedBlocks": {"xml": {"wrapped": true}, "type": "array", "items": {"$ref": "#/definitions/Block"}}, "UncommittedBlocks": {"xml": {"wrapped": true}, "type": "array", "items": {"$ref": "#/definitions/Block"}}}}, "BlockLookupList": {"type": "object", "properties": {"Committed": {"type": "array", "items": {"type": "string", "xml": {"name": "Committed"}}}, "Uncommitted": {"type": "array", "items": {"type": "string", "xml": {"name": "Uncommitted"}}}, "Latest": {"type": "array", "items": {"type": "string", "xml": {"name": "Latest"}}}}, "xml": {"name": "BlockList"}}, "ContainerItem": {"xml": {"name": "Container"}, "type": "object", "required": ["Name", "Properties"], "description": "An Azure Storage container", "properties": {"Name": {"type": "string"}, "Properties": {"$ref": "#/definitions/ContainerProperties"}, "Metadata": {"$ref": "#/definitions/Metadata"}}}, "ContainerProperties": {"type": "object", "required": ["Last-Modified", "Etag"], "description": "Properties of a container", "properties": {"Last-Modified": {"type": "string", "format": "date-time-rfc1123"}, "Etag": {"type": "string", "format": "etag"}, "LeaseStatus": {"$ref": "#/definitions/LeaseStatus"}, "LeaseState": {"$ref": "#/definitions/LeaseState"}, "LeaseDuration": {"$ref": "#/definitions/LeaseDuration"}, "PublicAccess": {"$ref": "#/definitions/PublicAccessType"}, "HasImmutabilityPolicy": {"type": "boolean"}, "HasLegalHold": {"type": "boolean"}}}, "ListContainersSegmentResponse": {"xml": {"name": "EnumerationResults"}, "description": "An enumeration of containers", "type": "object", "required": ["ServiceEndpoint", "ContainerItems"], "properties": {"ServiceEndpoint": {"type": "string", "xml": {"attribute": true}}, "Prefix": {"type": "string"}, "Marker": {"type": "string"}, "MaxResults": {"type": "integer"}, "ContainerItems": {"xml": {"wrapped": true, "name": "Containers"}, "type": "array", "items": {"$ref": "#/definitions/ContainerItem"}}, "NextMarker": {"type": "string"}}}, "CorsRule": {"description": "CORS is an HTTP feature that enables a web application running under one domain to access resources in another domain. Web browsers implement a security restriction known as same-origin policy that prevents a web page from calling APIs in a different domain; CORS provides a secure way to allow one domain (the origin domain) to call APIs in another domain", "type": "object", "required": ["Allowed<PERSON><PERSON><PERSON>", "AllowedMethods", "AllowedHeaders", "ExposedHeaders", "MaxAgeInSeconds"], "properties": {"AllowedOrigins": {"description": "The origin domains that are permitted to make a request against the storage service via CORS. The origin domain is the domain from which the request originates. Note that the origin must be an exact case-sensitive match with the origin that the user age sends to the service. You can also use the wildcard character '*' to allow all origin domains to make requests via CORS.", "type": "string"}, "AllowedMethods": {"description": "The methods (HTTP request verbs) that the origin domain may use for a CORS request. (comma separated)", "type": "string"}, "AllowedHeaders": {"description": "the request headers that the origin domain may specify on the CORS request.", "type": "string"}, "ExposedHeaders": {"description": "The response headers that may be sent in the response to the CORS request and exposed by the browser to the request issuer", "type": "string"}, "MaxAgeInSeconds": {"description": "The maximum amount time that a browser should cache the preflight OPTIONS request.", "type": "integer", "minimum": 0}}}, "ErrorCode": {"description": "Error codes returned by the service", "type": "string", "enum": ["AccountAlreadyExists", "AccountBeingCreated", "AccountIsDisabled", "AuthenticationFailed", "AuthorizationFailure", "ConditionHeadersNotSupported", "ConditionNotMet", "EmptyMetadataKey", "InsufficientAccountPermissions", "InternalError", "InvalidAuthenticationInfo", "InvalidHeaderValue", "InvalidHttpVerb", "InvalidInput", "InvalidMd5", "InvalidMetadata", "InvalidQueryParameterValue", "InvalidRange", "InvalidResourceName", "InvalidUri", "InvalidXmlDocument", "InvalidXmlNodeValue", "Md5Mismatch", "MetadataTooLarge", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MissingRequiredQueryParameter", "MissingRequiredHeader", "MissingRequiredXmlNode", "MultipleConditionHeadersNotSupported", "OperationTimedOut", "OutOfRangeInput", "OutOfRangeQueryParameterValue", "RequestBodyTooLarge", "ResourceTypeMismatch", "RequestUrlFailedToParse", "ResourceAlreadyExists", "ResourceNotFound", "ServerBusy", "Unsupported<PERSON><PERSON>er", "UnsupportedXmlNode", "UnsupportedQueryParameter", "UnsupportedHttpVerb", "AppendPositionConditionNotMet", "BlobAlreadyExists", "BlobNotFound", "BlobOverwritten", "BlobTierInadequateForContentLength", "BlockCountExceedsLimit", "BlockListTooLong", "CannotChangeToLowerTier", "CannotVerifyCopySource", "ContainerAlreadyExists", "ContainerBeingDeleted", "ContainerDisabled", "ContainerNotFound", "ContentLengthLargerThanTierLimit", "CopyAcrossAccountsNotSupported", "CopyIdMismatch", "FeatureVersionMismatch", "IncrementalCopyBlobMismatch", "IncrementalCopyOfEralierVersionSnapshotNotAllowed", "IncrementalCopySourceMustBeSnapshot", "InfiniteLeaseDurationRequired", "InvalidBlobOrBlock", "InvalidBlobTier", "InvalidBlobType", "InvalidBlockId", "InvalidBlockList", "InvalidOperation", "InvalidPageRange", "InvalidSourceBlobType", "InvalidSourceBlobUrl", "InvalidVersionForPageBlobOperation", "LeaseAlreadyPresent", "LeaseAlreadyBroken", "LeaseIdMismatchWithBlobOperation", "LeaseIdMismatchWithContainerOperation", "LeaseIdMismatchWithLeaseOperation", "LeaseIdMissing", "LeaseIsBreakingAndCannotBeAcquired", "LeaseIsBreakingAndCannotBeChanged", "LeaseIsBrokenAndCannotBeRenewed", "LeaseLost", "LeaseNotPresentWithBlobOperation", "LeaseNotPresentWithContainerOperation", "LeaseNotPresentWithLeaseOperation", "MaxBlobSizeConditionNotMet", "NoPendingCopyOperation", "OperationNotAllowedOnIncrementalCopyBlob", "PendingCopyOperation", "PreviousSnapshotCannotBeNewer", "PreviousSnapshotNotFound", "PreviousSnapshotOperationNotSupported", "SequenceNumberConditionNotMet", "SequenceNumberIncrementTooLarge", "SnapshotCountExceeded", "SnaphotOperationRateExceeded", "SnapshotsPresent", "SourceConditionNotMet", "SystemInUse", "TargetConditionNotMet", "UnauthorizedBlobOverwrite", "BlobBeingRehydrated", "BlobArchived", "BlobNotArchived"], "x-ms-enum": {"name": "StorageErrorCode", "modelAsString": true}}, "GeoReplication": {"description": "Geo-Replication information for the Secondary Storage Service", "type": "object", "required": ["Status", "LastSyncTime"], "properties": {"Status": {"description": "The status of the secondary location", "type": "string", "enum": ["live", "bootstrap", "unavailable"], "x-ms-enum": {"name": "GeoReplicationStatusType", "modelAsString": true}}, "LastSyncTime": {"description": "A GMT date/time value, to the second. All primary writes preceding this value are guaranteed to be available for read operations at the secondary. Primary writes after this point in time may or may not be available for reads.", "type": "string", "format": "date-time-rfc1123"}}}, "Logging": {"description": "Azure Analytics Logging settings.", "type": "object", "required": ["Version", "Delete", "Read", "Write", "RetentionPolicy"], "properties": {"Version": {"description": "The version of Storage Analytics to configure.", "type": "string"}, "Delete": {"description": "Indicates whether all delete requests should be logged.", "type": "boolean"}, "Read": {"description": "Indicates whether all read requests should be logged.", "type": "boolean"}, "Write": {"description": "Indicates whether all write requests should be logged.", "type": "boolean"}, "RetentionPolicy": {"$ref": "#/definitions/RetentionPolicy"}}}, "Metadata": {"type": "object", "additionalProperties": {"type": "string"}}, "Metrics": {"description": "a summary of request statistics grouped by API in hour or minute aggregates for blobs", "required": ["Enabled"], "properties": {"Version": {"description": "The version of Storage Analytics to configure.", "type": "string"}, "Enabled": {"description": "Indicates whether metrics are enabled for the Blob service.", "type": "boolean"}, "IncludeAPIs": {"description": "Indicates whether metrics should generate summary statistics for called API operations.", "type": "boolean"}, "RetentionPolicy": {"$ref": "#/definitions/RetentionPolicy"}}}, "PageList": {"description": "the list of pages", "type": "object", "properties": {"PageRange": {"type": "array", "items": {"$ref": "#/definitions/PageRange"}}, "ClearRange": {"type": "array", "items": {"$ref": "#/definitions/ClearRange"}}}}, "PageRange": {"type": "object", "required": ["Start", "End"], "properties": {"Start": {"type": "integer", "format": "int64", "xml": {"name": "Start"}}, "End": {"type": "integer", "format": "int64", "xml": {"name": "End"}}}, "xml": {"name": "PageRang<PERSON>"}}, "ClearRange": {"type": "object", "required": ["Start", "End"], "properties": {"Start": {"type": "integer", "format": "int64", "xml": {"name": "Start"}}, "End": {"type": "integer", "format": "int64", "xml": {"name": "End"}}}, "xml": {"name": "ClearRange"}}, "RetentionPolicy": {"description": "the retention policy which determines how long the associated data should persist", "type": "object", "required": ["Enabled"], "properties": {"Enabled": {"description": "Indicates whether a retention policy is enabled for the storage service", "type": "boolean"}, "Days": {"description": "Indicates the number of days that metrics or logging or soft-deleted data should be retained. All data older than this value will be deleted", "type": "integer", "minimum": 1}}}, "SignedIdentifier": {"xml": {"name": "SignedIdentifier"}, "description": "signed identifier", "type": "object", "required": ["Id", "AccessPolicy"], "properties": {"Id": {"type": "string", "description": "a unique id"}, "AccessPolicy": {"$ref": "#/definitions/AccessPolicy"}}}, "SignedIdentifiers": {"description": "a collection of signed identifiers", "type": "array", "items": {"$ref": "#/definitions/SignedIdentifier"}, "xml": {"wrapped": true, "name": "SignedIdentifiers"}}, "StaticWebsite": {"description": "The properties that enable an account to host a static website", "type": "object", "required": ["Enabled"], "properties": {"Enabled": {"description": "Indicates whether this account is hosting a static website", "type": "boolean"}, "IndexDocument": {"description": "The default name of the index page under each directory", "type": "string"}, "ErrorDocument404Path": {"description": "The absolute path of the custom 404 page", "type": "string"}}}, "StorageServiceProperties": {"description": "Storage Service Properties.", "type": "object", "properties": {"Logging": {"$ref": "#/definitions/Logging"}, "HourMetrics": {"$ref": "#/definitions/Metrics"}, "MinuteMetrics": {"$ref": "#/definitions/Metrics"}, "Cors": {"description": "The set of CORS rules.", "type": "array", "items": {"$ref": "#/definitions/CorsRule"}, "xml": {"wrapped": true}}, "DefaultServiceVersion": {"description": "The default version to use for requests to the Blob service if an incoming request's version is not specified. Possible values include version 2008-10-27 and all more recent versions", "type": "string"}, "DeleteRetentionPolicy": {"$ref": "#/definitions/RetentionPolicy"}, "StaticWebsite": {"$ref": "#/definitions/StaticWebsite"}}}, "StorageServiceStats": {"description": "Stats for the storage service.", "type": "object", "properties": {"GeoReplication": {"$ref": "#/definitions/GeoReplication"}}}}, "parameters": {"Url": {"name": "url", "description": "The URL of the service account, container, or blob that is the targe of the desired operation.", "required": true, "type": "string", "in": "path", "x-ms-skip-url-encoding": true}, "ApiVersionParameter": {"name": "x-ms-version", "x-ms-client-name": "version", "in": "header", "required": true, "type": "string", "description": "Specifies the version of the operation to use for this request.", "enum": ["2018-11-09"]}, "Blob": {"name": "blob", "in": "path", "required": true, "type": "string", "pattern": "^[a-zA-Z0-9]+(?:/[a-zA-Z0-9]+)*(?:\\.[a-zA-Z0-9]+){0,1}$", "minLength": 1, "maxLength": 1024, "x-ms-parameter-location": "method", "description": "The blob name."}, "BlobCacheControl": {"name": "x-ms-blob-cache-control", "x-ms-client-name": "blobCacheControl", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "blob-HTTP-headers"}, "description": "Optional. Sets the blob's cache control. If specified, this property is stored with the blob and returned with a read request."}, "BlobConditionAppendPos": {"name": "x-ms-blob-condition-appendpos", "x-ms-client-name": "appendPosition", "in": "header", "required": false, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "append-position-access-conditions"}, "description": "Optional conditional header, used only for the Append Block operation. A number indicating the byte offset to compare. Append Block will succeed only if the append position is equal to this number. If it is not, the request will fail with the AppendPositionConditionNotMet error (HTTP status code 412 - Precondition Failed)."}, "BlobConditionMaxSize": {"name": "x-ms-blob-condition-maxsize", "x-ms-client-name": "maxSize", "in": "header", "required": false, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "append-position-access-conditions"}, "description": "Optional conditional header. The max length in bytes permitted for the append blob. If the Append Block operation would cause the blob to exceed that limit or if the blob size is already greater than the value specified in this header, the request will fail with MaxBlobSizeConditionNotMet error (HTTP status code 412 - Precondition Failed)."}, "BlobPublicAccess": {"name": "x-ms-blob-public-access", "x-ms-client-name": "access", "in": "header", "required": false, "x-ms-parameter-location": "method", "description": "Specifies whether data in the container may be accessed publicly and the level of access", "type": "string", "enum": ["container", "blob"], "x-ms-enum": {"name": "PublicAccessType", "modelAsString": true}}, "AccessTier": {"name": "x-ms-access-tier", "x-ms-client-name": "tier", "in": "header", "required": true, "type": "string", "enum": ["P4", "P6", "P10", "P20", "P30", "P40", "P50", "Hot", "Cool", "Archive"], "x-ms-enum": {"name": "AccessTier", "modelAsString": true}, "x-ms-parameter-location": "method", "description": "Indicates the tier to be set on the blob."}, "BlobContentDisposition": {"name": "x-ms-blob-content-disposition", "x-ms-client-name": "blobContentDisposition", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "blob-HTTP-headers"}, "description": "Optional. Sets the blob's Content-Disposition header."}, "BlobContentEncoding": {"name": "x-ms-blob-content-encoding", "x-ms-client-name": "blobContentEncoding", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "blob-HTTP-headers"}, "description": "Optional. Sets the blob's content encoding. If specified, this property is stored with the blob and returned with a read request."}, "BlobContentLanguage": {"name": "x-ms-blob-content-language", "x-ms-client-name": "blobContentLanguage", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "blob-HTTP-headers"}, "description": "Optional. Set the blob's content language. If specified, this property is stored with the blob and returned with a read request."}, "BlobContentLengthOptional": {"name": "x-ms-blob-content-length", "x-ms-client-name": "blob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "header", "required": false, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "description": "This header specifies the maximum size for the page blob, up to 1 TB. The page blob size must be aligned to a 512-byte boundary."}, "BlobContentLengthRequired": {"name": "x-ms-blob-content-length", "x-ms-client-name": "blob<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "header", "required": true, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "description": "This header specifies the maximum size for the page blob, up to 1 TB. The page blob size must be aligned to a 512-byte boundary."}, "BlobContentMD5": {"name": "x-ms-blob-content-md5", "x-ms-client-name": "blobContentMD5", "in": "header", "required": false, "type": "string", "format": "byte", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "blob-HTTP-headers"}, "description": "Optional. An MD5 hash of the blob content. Note that this hash is not validated, as the hashes for the individual blocks were validated when each was uploaded."}, "BlobContentType": {"name": "x-ms-blob-content-type", "x-ms-client-name": "blobContentType", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "blob-HTTP-headers"}, "description": "Optional. Sets the blob's content type. If specified, this property is stored with the blob and returned with a read request."}, "BlobSequenceNumber": {"name": "x-ms-blob-sequence-number", "x-ms-client-name": "blobSequenceNumber", "in": "header", "required": false, "type": "integer", "format": "int64", "default": 0, "x-ms-parameter-location": "method", "description": "Set for page blobs only. The sequence number is a user-controlled value that you can use to track requests. The value of the sequence number must be between 0 and 2^63 - 1."}, "BlockId": {"name": "blockid", "x-ms-client-name": "blockId", "in": "query", "type": "string", "required": true, "x-ms-parameter-location": "method", "description": "A valid Base64 string value that identifies the block. Prior to encoding, the string must be less than or equal to 64 bytes in size. For a given blob, the length of the value specified for the blockid parameter must be the same size for each block."}, "BlockListType": {"name": "blocklisttype", "x-ms-client-name": "listType", "in": "query", "required": true, "default": "committed", "x-ms-parameter-location": "method", "description": "Specifies whether to return the list of committed blocks, the list of uncommitted blocks, or both lists together.", "type": "string", "enum": ["committed", "uncommitted", "all"], "x-ms-enum": {"name": "BlockListType", "modelAsString": false}}, "Body": {"name": "body", "in": "body", "required": true, "schema": {"type": "object", "format": "file"}, "x-ms-parameter-location": "method", "description": "Initial data"}, "ContainerAcl": {"name": "containerAcl", "in": "body", "schema": {"$ref": "#/definitions/SignedIdentifiers"}, "x-ms-parameter-location": "method", "description": "the acls for the container"}, "CopyId": {"name": "copyid", "x-ms-client-name": "copyId", "in": "query", "required": true, "type": "string", "x-ms-parameter-location": "method", "description": "The copy identifier provided in the x-ms-copy-id header of the original Copy Blob operation."}, "ClientRequestId": {"name": "x-ms-client-request-id", "x-ms-client-name": "requestId", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "Provides a client-generated, opaque value with a 1 KB character limit that is recorded in the analytics logs when storage analytics logging is enabled."}, "ContainerName": {"name": "containerName", "in": "path", "required": true, "type": "string", "x-ms-parameter-location": "method", "description": "The container name."}, "ContentLength": {"name": "Content-Length", "in": "header", "required": true, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "description": "The length of the request."}, "ContentMD5": {"name": "Content-MD5", "x-ms-client-name": "transactionalContentMD5", "in": "header", "required": false, "type": "string", "format": "byte", "x-ms-parameter-location": "method", "description": "Specify the transactional md5 for the body, to be validated by the service."}, "CopySource": {"name": "x-ms-copy-source", "x-ms-client-name": "copySource", "in": "header", "required": true, "type": "string", "format": "url", "x-ms-parameter-location": "method", "description": "Specifies the name of the source page blob snapshot. This value is a URL of up to 2 KB in length that specifies a page blob snapshot. The value should be URL-encoded as it would appear in a request URI. The source blob must either be public or must be authenticated via a shared access signature."}, "DeleteSnapshots": {"name": "x-ms-delete-snapshots", "x-ms-client-name": "deleteSnapshots", "description": "Required if the blob has associated snapshots. Specify one of the following two options: include: Delete the base blob and all of its snapshots. only: Delete only the blob's snapshots and not the blob itself", "x-ms-parameter-location": "method", "in": "header", "required": false, "type": "string", "enum": ["include", "only"], "x-ms-enum": {"name": "DeleteSnapshotsOptionType", "modelAsString": false}}, "Delimiter": {"name": "delimiter", "description": "When the request includes this parameter, the operation returns a BlobPrefix element in the response body that acts as a placeholder for all blobs whose names begin with the same substring up to the appearance of the delimiter character. The delimiter may be a single character or a string.", "type": "string", "x-ms-parameter-location": "method", "in": "query", "required": true}, "GetRangeContentMD5": {"name": "x-ms-range-get-content-md5", "x-ms-client-name": "rangeGetContentMD5", "in": "header", "required": false, "type": "boolean", "x-ms-parameter-location": "method", "description": "When set to true and specified together with the Range, the service returns the MD5 hash for the range, as long as the range is less than or equal to 4 MB in size."}, "IfMatch": {"name": "If-Match", "x-ms-client-name": "ifMatch", "in": "header", "required": false, "type": "string", "format": "etag", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "modified-access-conditions"}, "description": "Specify an ETag value to operate only on blobs with a matching value."}, "IfModifiedSince": {"name": "If-Modified-Since", "x-ms-client-name": "ifModifiedSince", "in": "header", "required": false, "type": "string", "format": "date-time-rfc1123", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "modified-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has been modified since the specified date/time."}, "IfNoneMatch": {"name": "If-None-Match", "x-ms-client-name": "ifNoneMatch", "in": "header", "required": false, "type": "string", "format": "etag", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "modified-access-conditions"}, "description": "Specify an ETag value to operate only on blobs without a matching value."}, "IfUnmodifiedSince": {"name": "If-Unmodified-Since", "x-ms-client-name": "ifUnmodifiedSince", "in": "header", "required": false, "type": "string", "format": "date-time-rfc1123", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "modified-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has not been modified since the specified date/time."}, "IfSequenceNumberEqualTo": {"name": "x-ms-if-sequence-number-eq", "x-ms-client-name": "ifSequenceNumberEqualTo", "in": "header", "required": false, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "sequence-number-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has the specified sequence number."}, "IfSequenceNumberLessThan": {"name": "x-ms-if-sequence-number-lt", "x-ms-client-name": "ifSequenceNumberLessThan", "in": "header", "required": false, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "sequence-number-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has a sequence number less than the specified."}, "IfSequenceNumberLessThanOrEqualTo": {"name": "x-ms-if-sequence-number-le", "x-ms-client-name": "ifSequenceNumberLessThanOrEqualTo", "in": "header", "required": false, "type": "integer", "format": "int64", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "sequence-number-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has a sequence number less than or equal to the specified."}, "KeyInfo": {"name": "KeyInfo", "in": "body", "x-ms-parameter-location": "method", "required": true, "schema": {"$ref": "#/definitions/KeyInfo"}}, "ListBlobsInclude": {"name": "include", "in": "query", "required": false, "type": "array", "collectionFormat": "csv", "items": {"type": "string", "enum": ["copy", "deleted", "metadata", "snapshots", "uncommittedblobs"], "x-ms-enum": {"name": "ListBlobsIncludeItem", "modelAsString": false}}, "x-ms-parameter-location": "method", "description": "Include this parameter to specify one or more datasets to include in the response."}, "ListContainersInclude": {"name": "include", "in": "query", "required": false, "type": "string", "enum": ["metadata"], "x-ms-enum": {"name": "ListContainersIncludeType", "modelAsString": false}, "x-ms-parameter-location": "method", "description": "Include this parameter to specify that the container's metadata be returned as part of the response body."}, "LeaseBreakPeriod": {"name": "x-ms-lease-break-period", "x-ms-client-name": "break<PERSON><PERSON><PERSON>", "in": "header", "required": false, "type": "integer", "x-ms-parameter-location": "method", "description": "For a break operation, proposed duration the lease should continue before it is broken, in seconds, between 0 and 60. This break period is only used if it is shorter than the time remaining on the lease. If longer, the time remaining on the lease is used. A new lease will not be available before the break period has expired, but the lease may be held for longer than the break period. If this header does not appear with a break operation, a fixed-duration lease breaks after the remaining lease period elapses, and an infinite lease breaks immediately."}, "LeaseDuration": {"name": "x-ms-lease-duration", "x-ms-client-name": "duration", "in": "header", "required": false, "type": "integer", "x-ms-parameter-location": "method", "description": "Specifies the duration of the lease, in seconds, or negative one (-1) for a lease that never expires. A non-infinite lease can be between 15 and 60 seconds. A lease duration cannot be changed using renew or change."}, "LeaseIdOptional": {"name": "x-ms-lease-id", "x-ms-client-name": "leaseId", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "lease-access-conditions"}, "description": "If specified, the operation only succeeds if the resource's lease is active and matches this ID."}, "LeaseIdRequired": {"name": "x-ms-lease-id", "x-ms-client-name": "leaseId", "in": "header", "required": true, "type": "string", "x-ms-parameter-location": "method", "description": "Specifies the current lease ID on the resource."}, "Marker": {"name": "marker", "in": "query", "required": false, "type": "string", "description": "A string value that identifies the portion of the list of containers to be returned with the next listing operation. The operation returns the NextMarker value within the response body if the listing operation did not return all containers remaining to be listed with the current page. The NextMarker value can be used as the value for the marker parameter in a subsequent call to request the next page of list items. The marker value is opaque to the client.", "x-ms-parameter-location": "method"}, "MaxResults": {"name": "maxresults", "in": "query", "required": false, "type": "integer", "minimum": 1, "x-ms-parameter-location": "method", "description": "Specifies the maximum number of containers to return. If the request does not specify maxresults, or specifies a value greater than 5000, the server will return up to 5000 items. Note that if the listing operation crosses a partition boundary, then the service will return a continuation token for retrieving the remainder of the results. For this reason, it is possible that the service will return fewer results than specified by maxresults, or than the default of 5000."}, "Metadata": {"name": "x-ms-meta", "x-ms-client-name": "metadata", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "Optional. Specifies a user-defined name-value pair associated with the blob. If no name-value pairs are specified, the operation will copy the metadata from the source blob or file to the destination blob. If one or more name-value pairs are specified, the destination blob is created with the specified metadata, and metadata is not copied from the source blob or file. Note that beginning with version 2009-09-19, metadata names must adhere to the naming rules for C# identifiers. See Naming and Referencing Containers, Blobs, and Metadata for more information.", "x-ms-header-collection-prefix": "x-ms-meta-"}, "Prefix": {"name": "prefix", "in": "query", "required": false, "type": "string", "description": "Filters the results to return only containers whose name begins with the specified prefix.", "x-ms-parameter-location": "method"}, "PrevSnapshot": {"name": "prevsnapshot", "in": "query", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "Optional in version 2015-07-08 and newer. The prevsnapshot parameter is a DateTime value that specifies that the response will contain only pages that were changed between target blob and previous snapshot. Changed pages include both updated and cleared pages. The target blob may be a snapshot, as long as the snapshot specified by prevsnapshot is the older of the two. Note that incremental snapshots are currently supported only for blobs created on or after January 1, 2016."}, "ProposedLeaseIdOptional": {"name": "x-ms-proposed-lease-id", "x-ms-client-name": "proposedLeaseId", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "Proposed lease ID, in a GUID string format. The Blob service returns 400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor (String) for a list of valid GUID string formats."}, "ProposedLeaseIdRequired": {"name": "x-ms-proposed-lease-id", "x-ms-client-name": "proposedLeaseId", "in": "header", "required": true, "type": "string", "x-ms-parameter-location": "method", "description": "Proposed lease ID, in a GUID string format. The Blob service returns 400 (Invalid request) if the proposed lease ID is not in the correct format. See Guid Constructor (String) for a list of valid GUID string formats."}, "Range": {"name": "x-ms-range", "x-ms-client-name": "range", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "Return only the bytes of the blob in the specified range."}, "RangeRequiredPutPageFromUrl": {"name": "x-ms-range", "x-ms-client-name": "range", "in": "header", "required": true, "type": "string", "x-ms-parameter-location": "method", "description": "The range of bytes to which the source range would be written. The range should be 512 aligned and range-end is required."}, "SequenceNumberAction": {"name": "x-ms-sequence-number-action", "x-ms-client-name": "sequenceNumberAction", "in": "header", "required": true, "x-ms-parameter-location": "method", "description": "Required if the x-ms-blob-sequence-number header is set for the request. This property applies to page blobs only. This property indicates how the service should modify the blob's sequence number", "type": "string", "enum": ["max", "update", "increment"], "x-ms-enum": {"name": "SequenceNumberActionType", "modelAsString": false}}, "Snapshot": {"name": "snapshot", "in": "query", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "The snapshot parameter is an opaque DateTime value that, when present, specifies the blob snapshot to retrieve. For more information on working with blob snapshots, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/creating-a-snapshot-of-a-blob\">Creating a Snapshot of a Blob.</a>"}, "SourceContentMD5": {"name": "x-ms-source-content-md5", "x-ms-client-name": "sourceContentMD5", "in": "header", "required": false, "type": "string", "format": "byte", "x-ms-parameter-location": "method", "description": "Specify the md5 calculated for the range of bytes that must be read from the copy source."}, "SourceRange": {"name": "x-ms-source-range", "x-ms-client-name": "sourceRange", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "Bytes of source data in the specified range."}, "SourceRangeRequiredPutPageFromUrl": {"name": "x-ms-source-range", "x-ms-client-name": "sourceRange", "in": "header", "required": true, "type": "string", "x-ms-parameter-location": "method", "description": "Bytes of source data in the specified range. The length of this range should match the ContentLength header and x-ms-range/Range destination range header."}, "SourceIfMatch": {"name": "x-ms-source-if-match", "x-ms-client-name": "sourceIfMatch", "in": "header", "required": false, "type": "string", "format": "etag", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "source-modified-access-conditions"}, "description": "Specify an ETag value to operate only on blobs with a matching value."}, "SourceIfModifiedSince": {"name": "x-ms-source-if-modified-since", "x-ms-client-name": "sourceIfModifiedSince", "in": "header", "required": false, "type": "string", "format": "date-time-rfc1123", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "source-modified-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has been modified since the specified date/time."}, "SourceIfNoneMatch": {"name": "x-ms-source-if-none-match", "x-ms-client-name": "sourceIfNoneMatch", "in": "header", "required": false, "type": "string", "format": "etag", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "source-modified-access-conditions"}, "description": "Specify an ETag value to operate only on blobs without a matching value."}, "SourceIfUnmodifiedSince": {"name": "x-ms-source-if-unmodified-since", "x-ms-client-name": "sourceIfUnmodifiedSince", "in": "header", "required": false, "type": "string", "format": "date-time-rfc1123", "x-ms-parameter-location": "method", "x-ms-parameter-grouping": {"name": "source-modified-access-conditions"}, "description": "Specify this header value to operate only on a blob if it has not been modified since the specified date/time."}, "SourceLeaseId": {"name": "x-ms-source-lease-id", "x-ms-client-name": "sourceLeaseId", "in": "header", "required": false, "type": "string", "x-ms-parameter-location": "method", "description": "A lease ID for the source path. If specified, the source path must have an active lease and the leaase ID must match."}, "SourceUrl": {"name": "x-ms-copy-source", "x-ms-client-name": "sourceUrl", "in": "header", "required": true, "type": "string", "format": "url", "x-ms-parameter-location": "method", "description": "Specify a URL to the copy source."}, "StorageServiceProperties": {"name": "StorageServiceProperties", "in": "body", "required": true, "schema": {"$ref": "#/definitions/StorageServiceProperties"}, "x-ms-parameter-location": "method", "description": "The StorageService properties."}, "Timeout": {"name": "timeout", "in": "query", "required": false, "type": "integer", "minimum": 0, "x-ms-parameter-location": "method", "description": "The timeout parameter is expressed in seconds. For more information, see <a href=\"https://docs.microsoft.com/en-us/rest/api/storageservices/fileservices/setting-timeouts-for-blob-service-operations\">Setting Timeouts for Blob Service Operations.</a>"}}}