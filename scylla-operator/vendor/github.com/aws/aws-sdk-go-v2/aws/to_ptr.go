// Code generated by aws/generate.go DO NOT EDIT.

package aws

import (
	"github.com/aws/smithy-go/ptr"
	"time"
)

// Bool returns a pointer value for the bool value passed in.
func Bool(v bool) *bool {
	return ptr.Bool(v)
}

// BoolSlice returns a slice of bool pointers from the values
// passed in.
func BoolSlice(vs []bool) []*bool {
	return ptr.BoolSlice(vs)
}

// BoolMap returns a map of bool pointers from the values
// passed in.
func BoolMap(vs map[string]bool) map[string]*bool {
	return ptr.BoolMap(vs)
}

// Byte returns a pointer value for the byte value passed in.
func Byte(v byte) *byte {
	return ptr.Byte(v)
}

// ByteSlice returns a slice of byte pointers from the values
// passed in.
func ByteSlice(vs []byte) []*byte {
	return ptr.ByteSlice(vs)
}

// ByteMap returns a map of byte pointers from the values
// passed in.
func ByteMap(vs map[string]byte) map[string]*byte {
	return ptr.ByteMap(vs)
}

// String returns a pointer value for the string value passed in.
func String(v string) *string {
	return ptr.String(v)
}

// StringSlice returns a slice of string pointers from the values
// passed in.
func StringSlice(vs []string) []*string {
	return ptr.StringSlice(vs)
}

// StringMap returns a map of string pointers from the values
// passed in.
func StringMap(vs map[string]string) map[string]*string {
	return ptr.StringMap(vs)
}

// Int returns a pointer value for the int value passed in.
func Int(v int) *int {
	return ptr.Int(v)
}

// IntSlice returns a slice of int pointers from the values
// passed in.
func IntSlice(vs []int) []*int {
	return ptr.IntSlice(vs)
}

// IntMap returns a map of int pointers from the values
// passed in.
func IntMap(vs map[string]int) map[string]*int {
	return ptr.IntMap(vs)
}

// Int8 returns a pointer value for the int8 value passed in.
func Int8(v int8) *int8 {
	return ptr.Int8(v)
}

// Int8Slice returns a slice of int8 pointers from the values
// passed in.
func Int8Slice(vs []int8) []*int8 {
	return ptr.Int8Slice(vs)
}

// Int8Map returns a map of int8 pointers from the values
// passed in.
func Int8Map(vs map[string]int8) map[string]*int8 {
	return ptr.Int8Map(vs)
}

// Int16 returns a pointer value for the int16 value passed in.
func Int16(v int16) *int16 {
	return ptr.Int16(v)
}

// Int16Slice returns a slice of int16 pointers from the values
// passed in.
func Int16Slice(vs []int16) []*int16 {
	return ptr.Int16Slice(vs)
}

// Int16Map returns a map of int16 pointers from the values
// passed in.
func Int16Map(vs map[string]int16) map[string]*int16 {
	return ptr.Int16Map(vs)
}

// Int32 returns a pointer value for the int32 value passed in.
func Int32(v int32) *int32 {
	return ptr.Int32(v)
}

// Int32Slice returns a slice of int32 pointers from the values
// passed in.
func Int32Slice(vs []int32) []*int32 {
	return ptr.Int32Slice(vs)
}

// Int32Map returns a map of int32 pointers from the values
// passed in.
func Int32Map(vs map[string]int32) map[string]*int32 {
	return ptr.Int32Map(vs)
}

// Int64 returns a pointer value for the int64 value passed in.
func Int64(v int64) *int64 {
	return ptr.Int64(v)
}

// Int64Slice returns a slice of int64 pointers from the values
// passed in.
func Int64Slice(vs []int64) []*int64 {
	return ptr.Int64Slice(vs)
}

// Int64Map returns a map of int64 pointers from the values
// passed in.
func Int64Map(vs map[string]int64) map[string]*int64 {
	return ptr.Int64Map(vs)
}

// Uint returns a pointer value for the uint value passed in.
func Uint(v uint) *uint {
	return ptr.Uint(v)
}

// UintSlice returns a slice of uint pointers from the values
// passed in.
func UintSlice(vs []uint) []*uint {
	return ptr.UintSlice(vs)
}

// UintMap returns a map of uint pointers from the values
// passed in.
func UintMap(vs map[string]uint) map[string]*uint {
	return ptr.UintMap(vs)
}

// Uint8 returns a pointer value for the uint8 value passed in.
func Uint8(v uint8) *uint8 {
	return ptr.Uint8(v)
}

// Uint8Slice returns a slice of uint8 pointers from the values
// passed in.
func Uint8Slice(vs []uint8) []*uint8 {
	return ptr.Uint8Slice(vs)
}

// Uint8Map returns a map of uint8 pointers from the values
// passed in.
func Uint8Map(vs map[string]uint8) map[string]*uint8 {
	return ptr.Uint8Map(vs)
}

// Uint16 returns a pointer value for the uint16 value passed in.
func Uint16(v uint16) *uint16 {
	return ptr.Uint16(v)
}

// Uint16Slice returns a slice of uint16 pointers from the values
// passed in.
func Uint16Slice(vs []uint16) []*uint16 {
	return ptr.Uint16Slice(vs)
}

// Uint16Map returns a map of uint16 pointers from the values
// passed in.
func Uint16Map(vs map[string]uint16) map[string]*uint16 {
	return ptr.Uint16Map(vs)
}

// Uint32 returns a pointer value for the uint32 value passed in.
func Uint32(v uint32) *uint32 {
	return ptr.Uint32(v)
}

// Uint32Slice returns a slice of uint32 pointers from the values
// passed in.
func Uint32Slice(vs []uint32) []*uint32 {
	return ptr.Uint32Slice(vs)
}

// Uint32Map returns a map of uint32 pointers from the values
// passed in.
func Uint32Map(vs map[string]uint32) map[string]*uint32 {
	return ptr.Uint32Map(vs)
}

// Uint64 returns a pointer value for the uint64 value passed in.
func Uint64(v uint64) *uint64 {
	return ptr.Uint64(v)
}

// Uint64Slice returns a slice of uint64 pointers from the values
// passed in.
func Uint64Slice(vs []uint64) []*uint64 {
	return ptr.Uint64Slice(vs)
}

// Uint64Map returns a map of uint64 pointers from the values
// passed in.
func Uint64Map(vs map[string]uint64) map[string]*uint64 {
	return ptr.Uint64Map(vs)
}

// Float32 returns a pointer value for the float32 value passed in.
func Float32(v float32) *float32 {
	return ptr.Float32(v)
}

// Float32Slice returns a slice of float32 pointers from the values
// passed in.
func Float32Slice(vs []float32) []*float32 {
	return ptr.Float32Slice(vs)
}

// Float32Map returns a map of float32 pointers from the values
// passed in.
func Float32Map(vs map[string]float32) map[string]*float32 {
	return ptr.Float32Map(vs)
}

// Float64 returns a pointer value for the float64 value passed in.
func Float64(v float64) *float64 {
	return ptr.Float64(v)
}

// Float64Slice returns a slice of float64 pointers from the values
// passed in.
func Float64Slice(vs []float64) []*float64 {
	return ptr.Float64Slice(vs)
}

// Float64Map returns a map of float64 pointers from the values
// passed in.
func Float64Map(vs map[string]float64) map[string]*float64 {
	return ptr.Float64Map(vs)
}

// Time returns a pointer value for the time.Time value passed in.
func Time(v time.Time) *time.Time {
	return ptr.Time(v)
}

// TimeSlice returns a slice of time.Time pointers from the values
// passed in.
func TimeSlice(vs []time.Time) []*time.Time {
	return ptr.TimeSlice(vs)
}

// TimeMap returns a map of time.Time pointers from the values
// passed in.
func TimeMap(vs map[string]time.Time) map[string]*time.Time {
	return ptr.TimeMap(vs)
}

// Duration returns a pointer value for the time.Duration value passed in.
func Duration(v time.Duration) *time.Duration {
	return ptr.Duration(v)
}

// DurationSlice returns a slice of time.Duration pointers from the values
// passed in.
func DurationSlice(vs []time.Duration) []*time.Duration {
	return ptr.DurationSlice(vs)
}

// DurationMap returns a map of time.Duration pointers from the values
// passed in.
func DurationMap(vs map[string]time.Duration) map[string]*time.Duration {
	return ptr.DurationMap(vs)
}
