// Code generated by smithy-go-codegen DO NOT EDIT.

package sts

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	awsmiddleware "github.com/aws/aws-sdk-go-v2/aws/middleware"
	awsxml "github.com/aws/aws-sdk-go-v2/aws/protocol/xml"
	"github.com/aws/aws-sdk-go-v2/service/sts/types"
	smithy "github.com/aws/smithy-go"
	smithyxml "github.com/aws/smithy-go/encoding/xml"
	smithyio "github.com/aws/smithy-go/io"
	"github.com/aws/smithy-go/middleware"
	"github.com/aws/smithy-go/ptr"
	smithytime "github.com/aws/smithy-go/time"
	smithyhttp "github.com/aws/smithy-go/transport/http"
	"io"
	"strconv"
	"strings"
)

type awsAwsquery_deserializeOpAssumeRole struct {
}

func (*awsAwsquery_deserializeOpAssumeRole) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpAssumeRole) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorAssumeRole(response, &metadata)
	}
	output := &AssumeRoleOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("AssumeRoleResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentAssumeRoleOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorAssumeRole(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	case strings.EqualFold("ExpiredTokenException", errorCode):
		return awsAwsquery_deserializeErrorExpiredTokenException(response, errorBody)

	case strings.EqualFold("MalformedPolicyDocument", errorCode):
		return awsAwsquery_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("PackedPolicyTooLarge", errorCode):
		return awsAwsquery_deserializeErrorPackedPolicyTooLargeException(response, errorBody)

	case strings.EqualFold("RegionDisabledException", errorCode):
		return awsAwsquery_deserializeErrorRegionDisabledException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpAssumeRoleWithSAML struct {
}

func (*awsAwsquery_deserializeOpAssumeRoleWithSAML) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpAssumeRoleWithSAML) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorAssumeRoleWithSAML(response, &metadata)
	}
	output := &AssumeRoleWithSAMLOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("AssumeRoleWithSAMLResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentAssumeRoleWithSAMLOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorAssumeRoleWithSAML(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	case strings.EqualFold("ExpiredTokenException", errorCode):
		return awsAwsquery_deserializeErrorExpiredTokenException(response, errorBody)

	case strings.EqualFold("IDPRejectedClaim", errorCode):
		return awsAwsquery_deserializeErrorIDPRejectedClaimException(response, errorBody)

	case strings.EqualFold("InvalidIdentityToken", errorCode):
		return awsAwsquery_deserializeErrorInvalidIdentityTokenException(response, errorBody)

	case strings.EqualFold("MalformedPolicyDocument", errorCode):
		return awsAwsquery_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("PackedPolicyTooLarge", errorCode):
		return awsAwsquery_deserializeErrorPackedPolicyTooLargeException(response, errorBody)

	case strings.EqualFold("RegionDisabledException", errorCode):
		return awsAwsquery_deserializeErrorRegionDisabledException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpAssumeRoleWithWebIdentity struct {
}

func (*awsAwsquery_deserializeOpAssumeRoleWithWebIdentity) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpAssumeRoleWithWebIdentity) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorAssumeRoleWithWebIdentity(response, &metadata)
	}
	output := &AssumeRoleWithWebIdentityOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("AssumeRoleWithWebIdentityResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentAssumeRoleWithWebIdentityOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorAssumeRoleWithWebIdentity(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	case strings.EqualFold("ExpiredTokenException", errorCode):
		return awsAwsquery_deserializeErrorExpiredTokenException(response, errorBody)

	case strings.EqualFold("IDPCommunicationError", errorCode):
		return awsAwsquery_deserializeErrorIDPCommunicationErrorException(response, errorBody)

	case strings.EqualFold("IDPRejectedClaim", errorCode):
		return awsAwsquery_deserializeErrorIDPRejectedClaimException(response, errorBody)

	case strings.EqualFold("InvalidIdentityToken", errorCode):
		return awsAwsquery_deserializeErrorInvalidIdentityTokenException(response, errorBody)

	case strings.EqualFold("MalformedPolicyDocument", errorCode):
		return awsAwsquery_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("PackedPolicyTooLarge", errorCode):
		return awsAwsquery_deserializeErrorPackedPolicyTooLargeException(response, errorBody)

	case strings.EqualFold("RegionDisabledException", errorCode):
		return awsAwsquery_deserializeErrorRegionDisabledException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpDecodeAuthorizationMessage struct {
}

func (*awsAwsquery_deserializeOpDecodeAuthorizationMessage) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpDecodeAuthorizationMessage) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorDecodeAuthorizationMessage(response, &metadata)
	}
	output := &DecodeAuthorizationMessageOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("DecodeAuthorizationMessageResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentDecodeAuthorizationMessageOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorDecodeAuthorizationMessage(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	case strings.EqualFold("InvalidAuthorizationMessageException", errorCode):
		return awsAwsquery_deserializeErrorInvalidAuthorizationMessageException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpGetAccessKeyInfo struct {
}

func (*awsAwsquery_deserializeOpGetAccessKeyInfo) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpGetAccessKeyInfo) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorGetAccessKeyInfo(response, &metadata)
	}
	output := &GetAccessKeyInfoOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("GetAccessKeyInfoResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentGetAccessKeyInfoOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorGetAccessKeyInfo(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpGetCallerIdentity struct {
}

func (*awsAwsquery_deserializeOpGetCallerIdentity) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpGetCallerIdentity) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorGetCallerIdentity(response, &metadata)
	}
	output := &GetCallerIdentityOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("GetCallerIdentityResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentGetCallerIdentityOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorGetCallerIdentity(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpGetFederationToken struct {
}

func (*awsAwsquery_deserializeOpGetFederationToken) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpGetFederationToken) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorGetFederationToken(response, &metadata)
	}
	output := &GetFederationTokenOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("GetFederationTokenResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentGetFederationTokenOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorGetFederationToken(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	case strings.EqualFold("MalformedPolicyDocument", errorCode):
		return awsAwsquery_deserializeErrorMalformedPolicyDocumentException(response, errorBody)

	case strings.EqualFold("PackedPolicyTooLarge", errorCode):
		return awsAwsquery_deserializeErrorPackedPolicyTooLargeException(response, errorBody)

	case strings.EqualFold("RegionDisabledException", errorCode):
		return awsAwsquery_deserializeErrorRegionDisabledException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

type awsAwsquery_deserializeOpGetSessionToken struct {
}

func (*awsAwsquery_deserializeOpGetSessionToken) ID() string {
	return "OperationDeserializer"
}

func (m *awsAwsquery_deserializeOpGetSessionToken) HandleDeserialize(ctx context.Context, in middleware.DeserializeInput, next middleware.DeserializeHandler) (
	out middleware.DeserializeOutput, metadata middleware.Metadata, err error,
) {
	out, metadata, err = next.HandleDeserialize(ctx, in)
	if err != nil {
		return out, metadata, err
	}

	response, ok := out.RawResponse.(*smithyhttp.Response)
	if !ok {
		return out, metadata, &smithy.DeserializationError{Err: fmt.Errorf("unknown transport type %T", out.RawResponse)}
	}

	if response.StatusCode < 200 || response.StatusCode >= 300 {
		return out, metadata, awsAwsquery_deserializeOpErrorGetSessionToken(response, &metadata)
	}
	output := &GetSessionTokenOutput{}
	out.Result = output

	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(response.Body, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return out, metadata, nil
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return out, metadata, &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("GetSessionTokenResult")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeOpDocumentGetSessionTokenOutput(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		err = &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
		return out, metadata, err
	}

	return out, metadata, err
}

func awsAwsquery_deserializeOpErrorGetSessionToken(response *smithyhttp.Response, metadata *middleware.Metadata) error {
	var errorBuffer bytes.Buffer
	if _, err := io.Copy(&errorBuffer, response.Body); err != nil {
		return &smithy.DeserializationError{Err: fmt.Errorf("failed to copy error response body, %w", err)}
	}
	errorBody := bytes.NewReader(errorBuffer.Bytes())

	errorCode := "UnknownError"
	errorMessage := errorCode

	errorComponents, err := awsxml.GetErrorResponseComponents(errorBody, false)
	if err != nil {
		return err
	}
	if reqID := errorComponents.RequestID; len(reqID) != 0 {
		awsmiddleware.SetRequestIDMetadata(metadata, reqID)
	}
	if len(errorComponents.Code) != 0 {
		errorCode = errorComponents.Code
	}
	if len(errorComponents.Message) != 0 {
		errorMessage = errorComponents.Message
	}
	errorBody.Seek(0, io.SeekStart)
	switch {
	case strings.EqualFold("RegionDisabledException", errorCode):
		return awsAwsquery_deserializeErrorRegionDisabledException(response, errorBody)

	default:
		genericError := &smithy.GenericAPIError{
			Code:    errorCode,
			Message: errorMessage,
		}
		return genericError

	}
}

func awsAwsquery_deserializeErrorExpiredTokenException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.ExpiredTokenException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentExpiredTokenException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorIDPCommunicationErrorException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.IDPCommunicationErrorException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentIDPCommunicationErrorException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorIDPRejectedClaimException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.IDPRejectedClaimException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentIDPRejectedClaimException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorInvalidAuthorizationMessageException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.InvalidAuthorizationMessageException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentInvalidAuthorizationMessageException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorInvalidIdentityTokenException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.InvalidIdentityTokenException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentInvalidIdentityTokenException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorMalformedPolicyDocumentException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.MalformedPolicyDocumentException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentMalformedPolicyDocumentException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorPackedPolicyTooLargeException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.PackedPolicyTooLargeException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentPackedPolicyTooLargeException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeErrorRegionDisabledException(response *smithyhttp.Response, errorBody *bytes.Reader) error {
	output := &types.RegionDisabledException{}
	var buff [1024]byte
	ringBuffer := smithyio.NewRingBuffer(buff[:])
	body := io.TeeReader(errorBody, ringBuffer)
	rootDecoder := xml.NewDecoder(body)
	t, err := smithyxml.FetchRootElement(rootDecoder)
	if err == io.EOF {
		return output
	}
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder := smithyxml.WrapNodeDecoder(rootDecoder, t)
	t, err = decoder.GetElement("Error")
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	decoder = smithyxml.WrapNodeDecoder(decoder.Decoder, t)
	err = awsAwsquery_deserializeDocumentRegionDisabledException(&output, decoder)
	if err != nil {
		var snapshot bytes.Buffer
		io.Copy(&snapshot, ringBuffer)
		return &smithy.DeserializationError{
			Err:      fmt.Errorf("failed to decode response body, %w", err),
			Snapshot: snapshot.Bytes(),
		}
	}

	return output
}

func awsAwsquery_deserializeDocumentAssumedRoleUser(v **types.AssumedRoleUser, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.AssumedRoleUser
	if *v == nil {
		sv = &types.AssumedRoleUser{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("Arn", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Arn = ptr.String(xtv)
			}

		case strings.EqualFold("AssumedRoleId", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.AssumedRoleId = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentCredentials(v **types.Credentials, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.Credentials
	if *v == nil {
		sv = &types.Credentials{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("AccessKeyId", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.AccessKeyId = ptr.String(xtv)
			}

		case strings.EqualFold("Expiration", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				t, err := smithytime.ParseDateTime(xtv)
				if err != nil {
					return err
				}
				sv.Expiration = ptr.Time(t)
			}

		case strings.EqualFold("SecretAccessKey", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SecretAccessKey = ptr.String(xtv)
			}

		case strings.EqualFold("SessionToken", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SessionToken = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentExpiredTokenException(v **types.ExpiredTokenException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.ExpiredTokenException
	if *v == nil {
		sv = &types.ExpiredTokenException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentFederatedUser(v **types.FederatedUser, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.FederatedUser
	if *v == nil {
		sv = &types.FederatedUser{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("Arn", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Arn = ptr.String(xtv)
			}

		case strings.EqualFold("FederatedUserId", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.FederatedUserId = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentIDPCommunicationErrorException(v **types.IDPCommunicationErrorException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.IDPCommunicationErrorException
	if *v == nil {
		sv = &types.IDPCommunicationErrorException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentIDPRejectedClaimException(v **types.IDPRejectedClaimException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.IDPRejectedClaimException
	if *v == nil {
		sv = &types.IDPRejectedClaimException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentInvalidAuthorizationMessageException(v **types.InvalidAuthorizationMessageException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.InvalidAuthorizationMessageException
	if *v == nil {
		sv = &types.InvalidAuthorizationMessageException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentInvalidIdentityTokenException(v **types.InvalidIdentityTokenException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.InvalidIdentityTokenException
	if *v == nil {
		sv = &types.InvalidIdentityTokenException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentMalformedPolicyDocumentException(v **types.MalformedPolicyDocumentException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.MalformedPolicyDocumentException
	if *v == nil {
		sv = &types.MalformedPolicyDocumentException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentPackedPolicyTooLargeException(v **types.PackedPolicyTooLargeException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.PackedPolicyTooLargeException
	if *v == nil {
		sv = &types.PackedPolicyTooLargeException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeDocumentRegionDisabledException(v **types.RegionDisabledException, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *types.RegionDisabledException
	if *v == nil {
		sv = &types.RegionDisabledException{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("message", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Message = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentAssumeRoleOutput(v **AssumeRoleOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *AssumeRoleOutput
	if *v == nil {
		sv = &AssumeRoleOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("AssumedRoleUser", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentAssumedRoleUser(&sv.AssumedRoleUser, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("Credentials", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentCredentials(&sv.Credentials, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("PackedPolicySize", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				i64, err := strconv.ParseInt(xtv, 10, 64)
				if err != nil {
					return err
				}
				sv.PackedPolicySize = ptr.Int32(int32(i64))
			}

		case strings.EqualFold("SourceIdentity", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SourceIdentity = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentAssumeRoleWithSAMLOutput(v **AssumeRoleWithSAMLOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *AssumeRoleWithSAMLOutput
	if *v == nil {
		sv = &AssumeRoleWithSAMLOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("AssumedRoleUser", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentAssumedRoleUser(&sv.AssumedRoleUser, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("Audience", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Audience = ptr.String(xtv)
			}

		case strings.EqualFold("Credentials", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentCredentials(&sv.Credentials, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("Issuer", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Issuer = ptr.String(xtv)
			}

		case strings.EqualFold("NameQualifier", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.NameQualifier = ptr.String(xtv)
			}

		case strings.EqualFold("PackedPolicySize", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				i64, err := strconv.ParseInt(xtv, 10, 64)
				if err != nil {
					return err
				}
				sv.PackedPolicySize = ptr.Int32(int32(i64))
			}

		case strings.EqualFold("SourceIdentity", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SourceIdentity = ptr.String(xtv)
			}

		case strings.EqualFold("Subject", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Subject = ptr.String(xtv)
			}

		case strings.EqualFold("SubjectType", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SubjectType = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentAssumeRoleWithWebIdentityOutput(v **AssumeRoleWithWebIdentityOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *AssumeRoleWithWebIdentityOutput
	if *v == nil {
		sv = &AssumeRoleWithWebIdentityOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("AssumedRoleUser", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentAssumedRoleUser(&sv.AssumedRoleUser, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("Audience", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Audience = ptr.String(xtv)
			}

		case strings.EqualFold("Credentials", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentCredentials(&sv.Credentials, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("PackedPolicySize", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				i64, err := strconv.ParseInt(xtv, 10, 64)
				if err != nil {
					return err
				}
				sv.PackedPolicySize = ptr.Int32(int32(i64))
			}

		case strings.EqualFold("Provider", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Provider = ptr.String(xtv)
			}

		case strings.EqualFold("SourceIdentity", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SourceIdentity = ptr.String(xtv)
			}

		case strings.EqualFold("SubjectFromWebIdentityToken", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.SubjectFromWebIdentityToken = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentDecodeAuthorizationMessageOutput(v **DecodeAuthorizationMessageOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *DecodeAuthorizationMessageOutput
	if *v == nil {
		sv = &DecodeAuthorizationMessageOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("DecodedMessage", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.DecodedMessage = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentGetAccessKeyInfoOutput(v **GetAccessKeyInfoOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *GetAccessKeyInfoOutput
	if *v == nil {
		sv = &GetAccessKeyInfoOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("Account", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Account = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentGetCallerIdentityOutput(v **GetCallerIdentityOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *GetCallerIdentityOutput
	if *v == nil {
		sv = &GetCallerIdentityOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("Account", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Account = ptr.String(xtv)
			}

		case strings.EqualFold("Arn", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.Arn = ptr.String(xtv)
			}

		case strings.EqualFold("UserId", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				sv.UserId = ptr.String(xtv)
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentGetFederationTokenOutput(v **GetFederationTokenOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *GetFederationTokenOutput
	if *v == nil {
		sv = &GetFederationTokenOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("Credentials", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentCredentials(&sv.Credentials, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("FederatedUser", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentFederatedUser(&sv.FederatedUser, nodeDecoder); err != nil {
				return err
			}

		case strings.EqualFold("PackedPolicySize", t.Name.Local):
			val, err := decoder.Value()
			if err != nil {
				return err
			}
			if val == nil {
				break
			}
			{
				xtv := string(val)
				i64, err := strconv.ParseInt(xtv, 10, 64)
				if err != nil {
					return err
				}
				sv.PackedPolicySize = ptr.Int32(int32(i64))
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}

func awsAwsquery_deserializeOpDocumentGetSessionTokenOutput(v **GetSessionTokenOutput, decoder smithyxml.NodeDecoder) error {
	if v == nil {
		return fmt.Errorf("unexpected nil of type %T", v)
	}
	var sv *GetSessionTokenOutput
	if *v == nil {
		sv = &GetSessionTokenOutput{}
	} else {
		sv = *v
	}

	for {
		t, done, err := decoder.Token()
		if err != nil {
			return err
		}
		if done {
			break
		}
		originalDecoder := decoder
		decoder = smithyxml.WrapNodeDecoder(originalDecoder.Decoder, t)
		switch {
		case strings.EqualFold("Credentials", t.Name.Local):
			nodeDecoder := smithyxml.WrapNodeDecoder(decoder.Decoder, t)
			if err := awsAwsquery_deserializeDocumentCredentials(&sv.Credentials, nodeDecoder); err != nil {
				return err
			}

		default:
			// Do nothing and ignore the unexpected tag element
			err = decoder.Decoder.Skip()
			if err != nil {
				return err
			}

		}
		decoder = originalDecoder
	}
	*v = sv
	return nil
}
