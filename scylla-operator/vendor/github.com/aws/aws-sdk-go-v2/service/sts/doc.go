// Code generated by smithy-go-codegen DO NOT EDIT.

// Package sts provides the API client, operations, and parameter types for AWS
// Security Token Service.
//
// # Security Token Service
//
// Security Token Service (STS) enables you to request temporary,
// limited-privilege credentials for users. This guide provides descriptions of the
// STS API. For more information about using this service, see [Temporary Security Credentials].
//
// [Temporary Security Credentials]: https://docs.aws.amazon.com/IAM/latest/UserGuide/id_credentials_temp.html
package sts
