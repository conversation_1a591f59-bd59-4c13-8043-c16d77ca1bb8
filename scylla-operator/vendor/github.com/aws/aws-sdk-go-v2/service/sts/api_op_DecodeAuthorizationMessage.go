// Code generated by smithy-go-codegen DO NOT EDIT.

package sts

import (
	"context"
	"fmt"
	awsmiddleware "github.com/aws/aws-sdk-go-v2/aws/middleware"
	"github.com/aws/smithy-go/middleware"
	smithyhttp "github.com/aws/smithy-go/transport/http"
)

// Decodes additional information about the authorization status of a request from
// an encoded message returned in response to an Amazon Web Services request.
//
// For example, if a user is not authorized to perform an operation that he or she
// has requested, the request returns a Client.UnauthorizedOperation response (an
// HTTP 403 response). Some Amazon Web Services operations additionally return an
// encoded message that can provide details about this authorization failure.
//
// Only certain Amazon Web Services operations return an encoded authorization
// message. The documentation for an individual operation indicates whether that
// operation returns an encoded message in addition to returning an HTTP code.
//
// The message is encoded because the details of the authorization status can
// contain privileged information that the user who requested the operation should
// not see. To decode an authorization status message, a user must be granted
// permissions through an IAM [policy]to request the DecodeAuthorizationMessage (
// sts:DecodeAuthorizationMessage ) action.
//
// The decoded message includes the following type of information:
//
//   - Whether the request was denied due to an explicit deny or due to the
//     absence of an explicit allow. For more information, see [Determining Whether a Request is Allowed or Denied]in the IAM User
//     Guide.
//
//   - The principal who made the request.
//
//   - The requested action.
//
//   - The requested resource.
//
//   - The values of condition keys in the context of the user's request.
//
// [Determining Whether a Request is Allowed or Denied]: https://docs.aws.amazon.com/IAM/latest/UserGuide/reference_policies_evaluation-logic.html#policy-eval-denyallow
// [policy]: https://docs.aws.amazon.com/IAM/latest/UserGuide/access_policies.html
func (c *Client) DecodeAuthorizationMessage(ctx context.Context, params *DecodeAuthorizationMessageInput, optFns ...func(*Options)) (*DecodeAuthorizationMessageOutput, error) {
	if params == nil {
		params = &DecodeAuthorizationMessageInput{}
	}

	result, metadata, err := c.invokeOperation(ctx, "DecodeAuthorizationMessage", params, optFns, c.addOperationDecodeAuthorizationMessageMiddlewares)
	if err != nil {
		return nil, err
	}

	out := result.(*DecodeAuthorizationMessageOutput)
	out.ResultMetadata = metadata
	return out, nil
}

type DecodeAuthorizationMessageInput struct {

	// The encoded message that was returned with the response.
	//
	// This member is required.
	EncodedMessage *string

	noSmithyDocumentSerde
}

// A document that contains additional information about the authorization status
// of a request from an encoded message that is returned in response to an Amazon
// Web Services request.
type DecodeAuthorizationMessageOutput struct {

	// The API returns a response with the decoded message.
	DecodedMessage *string

	// Metadata pertaining to the operation's result.
	ResultMetadata middleware.Metadata

	noSmithyDocumentSerde
}

func (c *Client) addOperationDecodeAuthorizationMessageMiddlewares(stack *middleware.Stack, options Options) (err error) {
	if err := stack.Serialize.Add(&setOperationInputMiddleware{}, middleware.After); err != nil {
		return err
	}
	err = stack.Serialize.Add(&awsAwsquery_serializeOpDecodeAuthorizationMessage{}, middleware.After)
	if err != nil {
		return err
	}
	err = stack.Deserialize.Add(&awsAwsquery_deserializeOpDecodeAuthorizationMessage{}, middleware.After)
	if err != nil {
		return err
	}
	if err := addProtocolFinalizerMiddlewares(stack, options, "DecodeAuthorizationMessage"); err != nil {
		return fmt.Errorf("add protocol finalizers: %v", err)
	}

	if err = addlegacyEndpointContextSetter(stack, options); err != nil {
		return err
	}
	if err = addSetLoggerMiddleware(stack, options); err != nil {
		return err
	}
	if err = addClientRequestID(stack); err != nil {
		return err
	}
	if err = addComputeContentLength(stack); err != nil {
		return err
	}
	if err = addResolveEndpointMiddleware(stack, options); err != nil {
		return err
	}
	if err = addComputePayloadSHA256(stack); err != nil {
		return err
	}
	if err = addRetry(stack, options); err != nil {
		return err
	}
	if err = addRawResponseToMetadata(stack); err != nil {
		return err
	}
	if err = addRecordResponseTiming(stack); err != nil {
		return err
	}
	if err = addClientUserAgent(stack, options); err != nil {
		return err
	}
	if err = smithyhttp.AddErrorCloseResponseBodyMiddleware(stack); err != nil {
		return err
	}
	if err = smithyhttp.AddCloseResponseBodyMiddleware(stack); err != nil {
		return err
	}
	if err = addSetLegacyContextSigningOptionsMiddleware(stack); err != nil {
		return err
	}
	if err = addOpDecodeAuthorizationMessageValidationMiddleware(stack); err != nil {
		return err
	}
	if err = stack.Initialize.Add(newServiceMetadataMiddleware_opDecodeAuthorizationMessage(options.Region), middleware.Before); err != nil {
		return err
	}
	if err = addRecursionDetection(stack); err != nil {
		return err
	}
	if err = addRequestIDRetrieverMiddleware(stack); err != nil {
		return err
	}
	if err = addResponseErrorMiddleware(stack); err != nil {
		return err
	}
	if err = addRequestResponseLogging(stack, options); err != nil {
		return err
	}
	if err = addDisableHTTPSMiddleware(stack, options); err != nil {
		return err
	}
	return nil
}

func newServiceMetadataMiddleware_opDecodeAuthorizationMessage(region string) *awsmiddleware.RegisterServiceMetadata {
	return &awsmiddleware.RegisterServiceMetadata{
		Region:        region,
		ServiceID:     ServiceID,
		OperationName: "DecodeAuthorizationMessage",
	}
}
