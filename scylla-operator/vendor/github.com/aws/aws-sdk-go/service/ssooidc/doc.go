// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

// Package ssooidc provides the client and types for making API
// requests to AWS SSO OIDC.
//
// IAM Identity Center OpenID Connect (OIDC) is a web service that enables a
// client (such as CLI or a native application) to register with IAM Identity
// Center. The service also enables the client to fetch the user’s access
// token upon successful authentication and authorization with IAM Identity
// Center.
//
// IAM Identity Center uses the sso and identitystore API namespaces.
//
// # Considerations for Using This Guide
//
// Before you begin using this guide, we recommend that you first review the
// following important information about how the IAM Identity Center OIDC service
// works.
//
//   - The IAM Identity Center OIDC service currently implements only the portions
//     of the OAuth 2.0 Device Authorization Grant standard (https://tools.ietf.org/html/rfc8628
//     (https://tools.ietf.org/html/rfc8628)) that are necessary to enable single
//     sign-on authentication with the CLI.
//
//   - With older versions of the CLI, the service only emits OIDC access tokens,
//     so to obtain a new token, users must explicitly re-authenticate. To access
//     the OIDC flow that supports token refresh and doesn’t require re-authentication,
//     update to the latest CLI version (1.27.10 for CLI V1 and 2.9.0 for CLI
//     V2) with support for OIDC token refresh and configurable IAM Identity
//     Center session durations. For more information, see Configure Amazon Web
//     Services access portal session duration (https://docs.aws.amazon.com/singlesignon/latest/userguide/configure-user-session.html).
//
//   - The access tokens provided by this service grant access to all Amazon
//     Web Services account entitlements assigned to an IAM Identity Center user,
//     not just a particular application.
//
//   - The documentation in this guide does not describe the mechanism to convert
//     the access token into Amazon Web Services Auth (“sigv4”) credentials
//     for use with IAM-protected Amazon Web Services service endpoints. For
//     more information, see GetRoleCredentials (https://docs.aws.amazon.com/singlesignon/latest/PortalAPIReference/API_GetRoleCredentials.html)
//     in the IAM Identity Center Portal API Reference Guide.
//
// For general information about IAM Identity Center, see What is IAM Identity
// Center? (https://docs.aws.amazon.com/singlesignon/latest/userguide/what-is.html)
// in the IAM Identity Center User Guide.
//
// See https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10 for more information on this service.
//
// See ssooidc package documentation for more information.
// https://docs.aws.amazon.com/sdk-for-go/api/service/ssooidc/
//
// # Using the Client
//
// To contact AWS SSO OIDC with the SDK use the New function to create
// a new service client. With that client you can make API requests to the service.
// These clients are safe to use concurrently.
//
// See the SDK's documentation for more information on how to use the SDK.
// https://docs.aws.amazon.com/sdk-for-go/api/
//
// See aws.Config documentation for more information on configuring SDK clients.
// https://docs.aws.amazon.com/sdk-for-go/api/aws/#Config
//
// See the AWS SSO OIDC client SSOOIDC for more
// information on creating client for this service.
// https://docs.aws.amazon.com/sdk-for-go/api/service/ssooidc/#New
package ssooidc
