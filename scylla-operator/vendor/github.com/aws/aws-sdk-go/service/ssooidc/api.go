// Code generated by private/model/cli/gen-api/main.go. DO NOT EDIT.

package ssooidc

import (
	"fmt"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/awsutil"
	"github.com/aws/aws-sdk-go/aws/credentials"
	"github.com/aws/aws-sdk-go/aws/request"
	"github.com/aws/aws-sdk-go/private/protocol"
)

const opCreateToken = "CreateToken"

// CreateTokenRequest generates a "aws/request.Request" representing the
// client's request for the CreateToken operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CreateToken for more information on using the CreateToken
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the CreateTokenRequest method.
//	req, resp := client.CreateTokenRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/CreateToken
func (c *SSOOIDC) CreateTokenRequest(input *CreateTokenInput) (req *request.Request, output *CreateTokenOutput) {
	op := &request.Operation{
		Name:       opCreateToken,
		HTTPMethod: "POST",
		HTTPPath:   "/token",
	}

	if input == nil {
		input = &CreateTokenInput{}
	}

	output = &CreateTokenOutput{}
	req = c.newRequest(op, input, output)
	req.Config.Credentials = credentials.AnonymousCredentials
	return
}

// CreateToken API operation for AWS SSO OIDC.
//
// Creates and returns access and refresh tokens for clients that are authenticated
// using client secrets. The access token can be used to fetch short-term credentials
// for the assigned AWS accounts or to access application APIs using bearer
// authentication.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO OIDC's
// API operation CreateToken for usage and error information.
//
// Returned Error Types:
//
//   - InvalidRequestException
//     Indicates that something is wrong with the input to the request. For example,
//     a required parameter might be missing or out of range.
//
//   - InvalidClientException
//     Indicates that the clientId or clientSecret in the request is invalid. For
//     example, this can occur when a client sends an incorrect clientId or an expired
//     clientSecret.
//
//   - InvalidGrantException
//     Indicates that a request contains an invalid grant. This can occur if a client
//     makes a CreateToken request with an invalid grant type.
//
//   - UnauthorizedClientException
//     Indicates that the client is not currently authorized to make the request.
//     This can happen when a clientId is not issued for a public client.
//
//   - UnsupportedGrantTypeException
//     Indicates that the grant type in the request is not supported by the service.
//
//   - InvalidScopeException
//     Indicates that the scope provided in the request is invalid.
//
//   - AuthorizationPendingException
//     Indicates that a request to authorize a client with an access user session
//     token is pending.
//
//   - SlowDownException
//     Indicates that the client is making the request too frequently and is more
//     than the service can handle.
//
//   - AccessDeniedException
//     You do not have sufficient access to perform this action.
//
//   - ExpiredTokenException
//     Indicates that the token issued by the service is expired and is no longer
//     valid.
//
//   - InternalServerException
//     Indicates that an error from the service occurred while trying to process
//     a request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/CreateToken
func (c *SSOOIDC) CreateToken(input *CreateTokenInput) (*CreateTokenOutput, error) {
	req, out := c.CreateTokenRequest(input)
	return out, req.Send()
}

// CreateTokenWithContext is the same as CreateToken with the addition of
// the ability to pass a context and additional request options.
//
// See CreateToken for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *SSOOIDC) CreateTokenWithContext(ctx aws.Context, input *CreateTokenInput, opts ...request.Option) (*CreateTokenOutput, error) {
	req, out := c.CreateTokenRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opCreateTokenWithIAM = "CreateTokenWithIAM"

// CreateTokenWithIAMRequest generates a "aws/request.Request" representing the
// client's request for the CreateTokenWithIAM operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See CreateTokenWithIAM for more information on using the CreateTokenWithIAM
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the CreateTokenWithIAMRequest method.
//	req, resp := client.CreateTokenWithIAMRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/CreateTokenWithIAM
func (c *SSOOIDC) CreateTokenWithIAMRequest(input *CreateTokenWithIAMInput) (req *request.Request, output *CreateTokenWithIAMOutput) {
	op := &request.Operation{
		Name:       opCreateTokenWithIAM,
		HTTPMethod: "POST",
		HTTPPath:   "/token?aws_iam=t",
	}

	if input == nil {
		input = &CreateTokenWithIAMInput{}
	}

	output = &CreateTokenWithIAMOutput{}
	req = c.newRequest(op, input, output)
	return
}

// CreateTokenWithIAM API operation for AWS SSO OIDC.
//
// Creates and returns access and refresh tokens for clients and applications
// that are authenticated using IAM entities. The access token can be used to
// fetch short-term credentials for the assigned AWS accounts or to access application
// APIs using bearer authentication.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO OIDC's
// API operation CreateTokenWithIAM for usage and error information.
//
// Returned Error Types:
//
//   - InvalidRequestException
//     Indicates that something is wrong with the input to the request. For example,
//     a required parameter might be missing or out of range.
//
//   - InvalidClientException
//     Indicates that the clientId or clientSecret in the request is invalid. For
//     example, this can occur when a client sends an incorrect clientId or an expired
//     clientSecret.
//
//   - InvalidGrantException
//     Indicates that a request contains an invalid grant. This can occur if a client
//     makes a CreateToken request with an invalid grant type.
//
//   - UnauthorizedClientException
//     Indicates that the client is not currently authorized to make the request.
//     This can happen when a clientId is not issued for a public client.
//
//   - UnsupportedGrantTypeException
//     Indicates that the grant type in the request is not supported by the service.
//
//   - InvalidScopeException
//     Indicates that the scope provided in the request is invalid.
//
//   - AuthorizationPendingException
//     Indicates that a request to authorize a client with an access user session
//     token is pending.
//
//   - SlowDownException
//     Indicates that the client is making the request too frequently and is more
//     than the service can handle.
//
//   - AccessDeniedException
//     You do not have sufficient access to perform this action.
//
//   - ExpiredTokenException
//     Indicates that the token issued by the service is expired and is no longer
//     valid.
//
//   - InternalServerException
//     Indicates that an error from the service occurred while trying to process
//     a request.
//
//   - InvalidRequestRegionException
//     Indicates that a token provided as input to the request was issued by and
//     is only usable by calling IAM Identity Center endpoints in another region.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/CreateTokenWithIAM
func (c *SSOOIDC) CreateTokenWithIAM(input *CreateTokenWithIAMInput) (*CreateTokenWithIAMOutput, error) {
	req, out := c.CreateTokenWithIAMRequest(input)
	return out, req.Send()
}

// CreateTokenWithIAMWithContext is the same as CreateTokenWithIAM with the addition of
// the ability to pass a context and additional request options.
//
// See CreateTokenWithIAM for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *SSOOIDC) CreateTokenWithIAMWithContext(ctx aws.Context, input *CreateTokenWithIAMInput, opts ...request.Option) (*CreateTokenWithIAMOutput, error) {
	req, out := c.CreateTokenWithIAMRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opRegisterClient = "RegisterClient"

// RegisterClientRequest generates a "aws/request.Request" representing the
// client's request for the RegisterClient operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See RegisterClient for more information on using the RegisterClient
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the RegisterClientRequest method.
//	req, resp := client.RegisterClientRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/RegisterClient
func (c *SSOOIDC) RegisterClientRequest(input *RegisterClientInput) (req *request.Request, output *RegisterClientOutput) {
	op := &request.Operation{
		Name:       opRegisterClient,
		HTTPMethod: "POST",
		HTTPPath:   "/client/register",
	}

	if input == nil {
		input = &RegisterClientInput{}
	}

	output = &RegisterClientOutput{}
	req = c.newRequest(op, input, output)
	req.Config.Credentials = credentials.AnonymousCredentials
	return
}

// RegisterClient API operation for AWS SSO OIDC.
//
// Registers a client with IAM Identity Center. This allows clients to initiate
// device authorization. The output should be persisted for reuse through many
// authentication requests.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO OIDC's
// API operation RegisterClient for usage and error information.
//
// Returned Error Types:
//
//   - InvalidRequestException
//     Indicates that something is wrong with the input to the request. For example,
//     a required parameter might be missing or out of range.
//
//   - InvalidScopeException
//     Indicates that the scope provided in the request is invalid.
//
//   - InvalidClientMetadataException
//     Indicates that the client information sent in the request during registration
//     is invalid.
//
//   - InternalServerException
//     Indicates that an error from the service occurred while trying to process
//     a request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/RegisterClient
func (c *SSOOIDC) RegisterClient(input *RegisterClientInput) (*RegisterClientOutput, error) {
	req, out := c.RegisterClientRequest(input)
	return out, req.Send()
}

// RegisterClientWithContext is the same as RegisterClient with the addition of
// the ability to pass a context and additional request options.
//
// See RegisterClient for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *SSOOIDC) RegisterClientWithContext(ctx aws.Context, input *RegisterClientInput, opts ...request.Option) (*RegisterClientOutput, error) {
	req, out := c.RegisterClientRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

const opStartDeviceAuthorization = "StartDeviceAuthorization"

// StartDeviceAuthorizationRequest generates a "aws/request.Request" representing the
// client's request for the StartDeviceAuthorization operation. The "output" return
// value will be populated with the request's response once the request completes
// successfully.
//
// Use "Send" method on the returned Request to send the API call to the service.
// the "output" return value is not valid until after Send returns without error.
//
// See StartDeviceAuthorization for more information on using the StartDeviceAuthorization
// API call, and error handling.
//
// This method is useful when you want to inject custom logic or configuration
// into the SDK's request lifecycle. Such as custom headers, or retry logic.
//
//	// Example sending a request using the StartDeviceAuthorizationRequest method.
//	req, resp := client.StartDeviceAuthorizationRequest(params)
//
//	err := req.Send()
//	if err == nil { // resp is now filled
//	    fmt.Println(resp)
//	}
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/StartDeviceAuthorization
func (c *SSOOIDC) StartDeviceAuthorizationRequest(input *StartDeviceAuthorizationInput) (req *request.Request, output *StartDeviceAuthorizationOutput) {
	op := &request.Operation{
		Name:       opStartDeviceAuthorization,
		HTTPMethod: "POST",
		HTTPPath:   "/device_authorization",
	}

	if input == nil {
		input = &StartDeviceAuthorizationInput{}
	}

	output = &StartDeviceAuthorizationOutput{}
	req = c.newRequest(op, input, output)
	req.Config.Credentials = credentials.AnonymousCredentials
	return
}

// StartDeviceAuthorization API operation for AWS SSO OIDC.
//
// Initiates device authorization by requesting a pair of verification codes
// from the authorization service.
//
// Returns awserr.Error for service API and SDK errors. Use runtime type assertions
// with awserr.Error's Code and Message methods to get detailed information about
// the error.
//
// See the AWS API reference guide for AWS SSO OIDC's
// API operation StartDeviceAuthorization for usage and error information.
//
// Returned Error Types:
//
//   - InvalidRequestException
//     Indicates that something is wrong with the input to the request. For example,
//     a required parameter might be missing or out of range.
//
//   - InvalidClientException
//     Indicates that the clientId or clientSecret in the request is invalid. For
//     example, this can occur when a client sends an incorrect clientId or an expired
//     clientSecret.
//
//   - UnauthorizedClientException
//     Indicates that the client is not currently authorized to make the request.
//     This can happen when a clientId is not issued for a public client.
//
//   - SlowDownException
//     Indicates that the client is making the request too frequently and is more
//     than the service can handle.
//
//   - InternalServerException
//     Indicates that an error from the service occurred while trying to process
//     a request.
//
// See also, https://docs.aws.amazon.com/goto/WebAPI/sso-oidc-2019-06-10/StartDeviceAuthorization
func (c *SSOOIDC) StartDeviceAuthorization(input *StartDeviceAuthorizationInput) (*StartDeviceAuthorizationOutput, error) {
	req, out := c.StartDeviceAuthorizationRequest(input)
	return out, req.Send()
}

// StartDeviceAuthorizationWithContext is the same as StartDeviceAuthorization with the addition of
// the ability to pass a context and additional request options.
//
// See StartDeviceAuthorization for details on how to use this API operation.
//
// The context must be non-nil and will be used for request cancellation. If
// the context is nil a panic will occur. In the future the SDK may create
// sub-contexts for http.Requests. See https://golang.org/pkg/context/
// for more information on using Contexts.
func (c *SSOOIDC) StartDeviceAuthorizationWithContext(ctx aws.Context, input *StartDeviceAuthorizationInput, opts ...request.Option) (*StartDeviceAuthorizationOutput, error) {
	req, out := c.StartDeviceAuthorizationRequest(input)
	req.SetContext(ctx)
	req.ApplyOptions(opts...)
	return out, req.Send()
}

// You do not have sufficient access to perform this action.
type AccessDeniedException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be access_denied.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AccessDeniedException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AccessDeniedException) GoString() string {
	return s.String()
}

func newErrorAccessDeniedException(v protocol.ResponseMetadata) error {
	return &AccessDeniedException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *AccessDeniedException) Code() string {
	return "AccessDeniedException"
}

// Message returns the exception's message.
func (s *AccessDeniedException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *AccessDeniedException) OrigErr() error {
	return nil
}

func (s *AccessDeniedException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *AccessDeniedException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *AccessDeniedException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that a request to authorize a client with an access user session
// token is pending.
type AuthorizationPendingException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be authorization_pending.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AuthorizationPendingException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s AuthorizationPendingException) GoString() string {
	return s.String()
}

func newErrorAuthorizationPendingException(v protocol.ResponseMetadata) error {
	return &AuthorizationPendingException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *AuthorizationPendingException) Code() string {
	return "AuthorizationPendingException"
}

// Message returns the exception's message.
func (s *AuthorizationPendingException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *AuthorizationPendingException) OrigErr() error {
	return nil
}

func (s *AuthorizationPendingException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *AuthorizationPendingException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *AuthorizationPendingException) RequestID() string {
	return s.RespMetadata.RequestID
}

type CreateTokenInput struct {
	_ struct{} `type:"structure"`

	// The unique identifier string for the client or application. This value comes
	// from the result of the RegisterClient API.
	//
	// ClientId is a required field
	ClientId *string `locationName:"clientId" type:"string" required:"true"`

	// A secret string generated for the client. This value should come from the
	// persisted result of the RegisterClient API.
	//
	// ClientSecret is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenInput's
	// String and GoString methods.
	//
	// ClientSecret is a required field
	ClientSecret *string `locationName:"clientSecret" type:"string" required:"true" sensitive:"true"`

	// Used only when calling this API for the Authorization Code grant type. The
	// short-term code is used to identify this authorization request. This grant
	// type is currently unsupported for the CreateToken API.
	Code *string `locationName:"code" type:"string"`

	// Used only when calling this API for the Device Code grant type. This short-term
	// code is used to identify this authorization request. This comes from the
	// result of the StartDeviceAuthorization API.
	DeviceCode *string `locationName:"deviceCode" type:"string"`

	// Supports the following OAuth grant types: Device Code and Refresh Token.
	// Specify either of the following values, depending on the grant type that
	// you want:
	//
	// * Device Code - urn:ietf:params:oauth:grant-type:device_code
	//
	// * Refresh Token - refresh_token
	//
	// For information about how to obtain the device code, see the StartDeviceAuthorization
	// topic.
	//
	// GrantType is a required field
	GrantType *string `locationName:"grantType" type:"string" required:"true"`

	// Used only when calling this API for the Authorization Code grant type. This
	// value specifies the location of the client or application that has registered
	// to receive the authorization code.
	RedirectUri *string `locationName:"redirectUri" type:"string"`

	// Used only when calling this API for the Refresh Token grant type. This token
	// is used to refresh short-term tokens, such as the access token, that might
	// expire.
	//
	// For more information about the features and limitations of the current IAM
	// Identity Center OIDC implementation, see Considerations for Using this Guide
	// in the IAM Identity Center OIDC API Reference (https://docs.aws.amazon.com/singlesignon/latest/OIDCAPIReference/Welcome.html).
	//
	// RefreshToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenInput's
	// String and GoString methods.
	RefreshToken *string `locationName:"refreshToken" type:"string" sensitive:"true"`

	// The list of scopes for which authorization is requested. The access token
	// that is issued is limited to the scopes that are granted. If this value is
	// not specified, IAM Identity Center authorizes all scopes that are configured
	// for the client during the call to RegisterClient.
	Scope []*string `locationName:"scope" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CreateTokenInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CreateTokenInput"}
	if s.ClientId == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientId"))
	}
	if s.ClientSecret == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientSecret"))
	}
	if s.GrantType == nil {
		invalidParams.Add(request.NewErrParamRequired("GrantType"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetClientId sets the ClientId field's value.
func (s *CreateTokenInput) SetClientId(v string) *CreateTokenInput {
	s.ClientId = &v
	return s
}

// SetClientSecret sets the ClientSecret field's value.
func (s *CreateTokenInput) SetClientSecret(v string) *CreateTokenInput {
	s.ClientSecret = &v
	return s
}

// SetCode sets the Code field's value.
func (s *CreateTokenInput) SetCode(v string) *CreateTokenInput {
	s.Code = &v
	return s
}

// SetDeviceCode sets the DeviceCode field's value.
func (s *CreateTokenInput) SetDeviceCode(v string) *CreateTokenInput {
	s.DeviceCode = &v
	return s
}

// SetGrantType sets the GrantType field's value.
func (s *CreateTokenInput) SetGrantType(v string) *CreateTokenInput {
	s.GrantType = &v
	return s
}

// SetRedirectUri sets the RedirectUri field's value.
func (s *CreateTokenInput) SetRedirectUri(v string) *CreateTokenInput {
	s.RedirectUri = &v
	return s
}

// SetRefreshToken sets the RefreshToken field's value.
func (s *CreateTokenInput) SetRefreshToken(v string) *CreateTokenInput {
	s.RefreshToken = &v
	return s
}

// SetScope sets the Scope field's value.
func (s *CreateTokenInput) SetScope(v []*string) *CreateTokenInput {
	s.Scope = v
	return s
}

type CreateTokenOutput struct {
	_ struct{} `type:"structure"`

	// A bearer token to access AWS accounts and applications assigned to a user.
	//
	// AccessToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenOutput's
	// String and GoString methods.
	AccessToken *string `locationName:"accessToken" type:"string" sensitive:"true"`

	// Indicates the time in seconds when an access token will expire.
	ExpiresIn *int64 `locationName:"expiresIn" type:"integer"`

	// The idToken is not implemented or supported. For more information about the
	// features and limitations of the current IAM Identity Center OIDC implementation,
	// see Considerations for Using this Guide in the IAM Identity Center OIDC API
	// Reference (https://docs.aws.amazon.com/singlesignon/latest/OIDCAPIReference/Welcome.html).
	//
	// A JSON Web Token (JWT) that identifies who is associated with the issued
	// access token.
	//
	// IdToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenOutput's
	// String and GoString methods.
	IdToken *string `locationName:"idToken" type:"string" sensitive:"true"`

	// A token that, if present, can be used to refresh a previously issued access
	// token that might have expired.
	//
	// For more information about the features and limitations of the current IAM
	// Identity Center OIDC implementation, see Considerations for Using this Guide
	// in the IAM Identity Center OIDC API Reference (https://docs.aws.amazon.com/singlesignon/latest/OIDCAPIReference/Welcome.html).
	//
	// RefreshToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenOutput's
	// String and GoString methods.
	RefreshToken *string `locationName:"refreshToken" type:"string" sensitive:"true"`

	// Used to notify the client that the returned token is an access token. The
	// supported token type is Bearer.
	TokenType *string `locationName:"tokenType" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenOutput) GoString() string {
	return s.String()
}

// SetAccessToken sets the AccessToken field's value.
func (s *CreateTokenOutput) SetAccessToken(v string) *CreateTokenOutput {
	s.AccessToken = &v
	return s
}

// SetExpiresIn sets the ExpiresIn field's value.
func (s *CreateTokenOutput) SetExpiresIn(v int64) *CreateTokenOutput {
	s.ExpiresIn = &v
	return s
}

// SetIdToken sets the IdToken field's value.
func (s *CreateTokenOutput) SetIdToken(v string) *CreateTokenOutput {
	s.IdToken = &v
	return s
}

// SetRefreshToken sets the RefreshToken field's value.
func (s *CreateTokenOutput) SetRefreshToken(v string) *CreateTokenOutput {
	s.RefreshToken = &v
	return s
}

// SetTokenType sets the TokenType field's value.
func (s *CreateTokenOutput) SetTokenType(v string) *CreateTokenOutput {
	s.TokenType = &v
	return s
}

type CreateTokenWithIAMInput struct {
	_ struct{} `type:"structure"`

	// Used only when calling this API for the JWT Bearer grant type. This value
	// specifies the JSON Web Token (JWT) issued by a trusted token issuer. To authorize
	// a trusted token issuer, configure the JWT Bearer GrantOptions for the application.
	//
	// Assertion is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenWithIAMInput's
	// String and GoString methods.
	Assertion *string `locationName:"assertion" type:"string" sensitive:"true"`

	// The unique identifier string for the client or application. This value is
	// an application ARN that has OAuth grants configured.
	//
	// ClientId is a required field
	ClientId *string `locationName:"clientId" type:"string" required:"true"`

	// Used only when calling this API for the Authorization Code grant type. This
	// short-term code is used to identify this authorization request. The code
	// is obtained through a redirect from IAM Identity Center to a redirect URI
	// persisted in the Authorization Code GrantOptions for the application.
	Code *string `locationName:"code" type:"string"`

	// Supports the following OAuth grant types: Authorization Code, Refresh Token,
	// JWT Bearer, and Token Exchange. Specify one of the following values, depending
	// on the grant type that you want:
	//
	// * Authorization Code - authorization_code
	//
	// * Refresh Token - refresh_token
	//
	// * JWT Bearer - urn:ietf:params:oauth:grant-type:jwt-bearer
	//
	// * Token Exchange - urn:ietf:params:oauth:grant-type:token-exchange
	//
	// GrantType is a required field
	GrantType *string `locationName:"grantType" type:"string" required:"true"`

	// Used only when calling this API for the Authorization Code grant type. This
	// value specifies the location of the client or application that has registered
	// to receive the authorization code.
	RedirectUri *string `locationName:"redirectUri" type:"string"`

	// Used only when calling this API for the Refresh Token grant type. This token
	// is used to refresh short-term tokens, such as the access token, that might
	// expire.
	//
	// For more information about the features and limitations of the current IAM
	// Identity Center OIDC implementation, see Considerations for Using this Guide
	// in the IAM Identity Center OIDC API Reference (https://docs.aws.amazon.com/singlesignon/latest/OIDCAPIReference/Welcome.html).
	//
	// RefreshToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenWithIAMInput's
	// String and GoString methods.
	RefreshToken *string `locationName:"refreshToken" type:"string" sensitive:"true"`

	// Used only when calling this API for the Token Exchange grant type. This value
	// specifies the type of token that the requester can receive. The following
	// values are supported:
	//
	// * Access Token - urn:ietf:params:oauth:token-type:access_token
	//
	// * Refresh Token - urn:ietf:params:oauth:token-type:refresh_token
	RequestedTokenType *string `locationName:"requestedTokenType" type:"string"`

	// The list of scopes for which authorization is requested. The access token
	// that is issued is limited to the scopes that are granted. If the value is
	// not specified, IAM Identity Center authorizes all scopes configured for the
	// application, including the following default scopes: openid, aws, sts:identity_context.
	Scope []*string `locationName:"scope" type:"list"`

	// Used only when calling this API for the Token Exchange grant type. This value
	// specifies the subject of the exchange. The value of the subject token must
	// be an access token issued by IAM Identity Center to a different client or
	// application. The access token must have authorized scopes that indicate the
	// requested application as a target audience.
	//
	// SubjectToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenWithIAMInput's
	// String and GoString methods.
	SubjectToken *string `locationName:"subjectToken" type:"string" sensitive:"true"`

	// Used only when calling this API for the Token Exchange grant type. This value
	// specifies the type of token that is passed as the subject of the exchange.
	// The following value is supported:
	//
	// * Access Token - urn:ietf:params:oauth:token-type:access_token
	SubjectTokenType *string `locationName:"subjectTokenType" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenWithIAMInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenWithIAMInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *CreateTokenWithIAMInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "CreateTokenWithIAMInput"}
	if s.ClientId == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientId"))
	}
	if s.GrantType == nil {
		invalidParams.Add(request.NewErrParamRequired("GrantType"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetAssertion sets the Assertion field's value.
func (s *CreateTokenWithIAMInput) SetAssertion(v string) *CreateTokenWithIAMInput {
	s.Assertion = &v
	return s
}

// SetClientId sets the ClientId field's value.
func (s *CreateTokenWithIAMInput) SetClientId(v string) *CreateTokenWithIAMInput {
	s.ClientId = &v
	return s
}

// SetCode sets the Code field's value.
func (s *CreateTokenWithIAMInput) SetCode(v string) *CreateTokenWithIAMInput {
	s.Code = &v
	return s
}

// SetGrantType sets the GrantType field's value.
func (s *CreateTokenWithIAMInput) SetGrantType(v string) *CreateTokenWithIAMInput {
	s.GrantType = &v
	return s
}

// SetRedirectUri sets the RedirectUri field's value.
func (s *CreateTokenWithIAMInput) SetRedirectUri(v string) *CreateTokenWithIAMInput {
	s.RedirectUri = &v
	return s
}

// SetRefreshToken sets the RefreshToken field's value.
func (s *CreateTokenWithIAMInput) SetRefreshToken(v string) *CreateTokenWithIAMInput {
	s.RefreshToken = &v
	return s
}

// SetRequestedTokenType sets the RequestedTokenType field's value.
func (s *CreateTokenWithIAMInput) SetRequestedTokenType(v string) *CreateTokenWithIAMInput {
	s.RequestedTokenType = &v
	return s
}

// SetScope sets the Scope field's value.
func (s *CreateTokenWithIAMInput) SetScope(v []*string) *CreateTokenWithIAMInput {
	s.Scope = v
	return s
}

// SetSubjectToken sets the SubjectToken field's value.
func (s *CreateTokenWithIAMInput) SetSubjectToken(v string) *CreateTokenWithIAMInput {
	s.SubjectToken = &v
	return s
}

// SetSubjectTokenType sets the SubjectTokenType field's value.
func (s *CreateTokenWithIAMInput) SetSubjectTokenType(v string) *CreateTokenWithIAMInput {
	s.SubjectTokenType = &v
	return s
}

type CreateTokenWithIAMOutput struct {
	_ struct{} `type:"structure"`

	// A bearer token to access AWS accounts and applications assigned to a user.
	//
	// AccessToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenWithIAMOutput's
	// String and GoString methods.
	AccessToken *string `locationName:"accessToken" type:"string" sensitive:"true"`

	// Indicates the time in seconds when an access token will expire.
	ExpiresIn *int64 `locationName:"expiresIn" type:"integer"`

	// A JSON Web Token (JWT) that identifies the user associated with the issued
	// access token.
	//
	// IdToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenWithIAMOutput's
	// String and GoString methods.
	IdToken *string `locationName:"idToken" type:"string" sensitive:"true"`

	// Indicates the type of tokens that are issued by IAM Identity Center. The
	// following values are supported:
	//
	// * Access Token - urn:ietf:params:oauth:token-type:access_token
	//
	// * Refresh Token - urn:ietf:params:oauth:token-type:refresh_token
	IssuedTokenType *string `locationName:"issuedTokenType" type:"string"`

	// A token that, if present, can be used to refresh a previously issued access
	// token that might have expired.
	//
	// For more information about the features and limitations of the current IAM
	// Identity Center OIDC implementation, see Considerations for Using this Guide
	// in the IAM Identity Center OIDC API Reference (https://docs.aws.amazon.com/singlesignon/latest/OIDCAPIReference/Welcome.html).
	//
	// RefreshToken is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by CreateTokenWithIAMOutput's
	// String and GoString methods.
	RefreshToken *string `locationName:"refreshToken" type:"string" sensitive:"true"`

	// The list of scopes for which authorization is granted. The access token that
	// is issued is limited to the scopes that are granted.
	Scope []*string `locationName:"scope" type:"list"`

	// Used to notify the requester that the returned token is an access token.
	// The supported token type is Bearer.
	TokenType *string `locationName:"tokenType" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenWithIAMOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s CreateTokenWithIAMOutput) GoString() string {
	return s.String()
}

// SetAccessToken sets the AccessToken field's value.
func (s *CreateTokenWithIAMOutput) SetAccessToken(v string) *CreateTokenWithIAMOutput {
	s.AccessToken = &v
	return s
}

// SetExpiresIn sets the ExpiresIn field's value.
func (s *CreateTokenWithIAMOutput) SetExpiresIn(v int64) *CreateTokenWithIAMOutput {
	s.ExpiresIn = &v
	return s
}

// SetIdToken sets the IdToken field's value.
func (s *CreateTokenWithIAMOutput) SetIdToken(v string) *CreateTokenWithIAMOutput {
	s.IdToken = &v
	return s
}

// SetIssuedTokenType sets the IssuedTokenType field's value.
func (s *CreateTokenWithIAMOutput) SetIssuedTokenType(v string) *CreateTokenWithIAMOutput {
	s.IssuedTokenType = &v
	return s
}

// SetRefreshToken sets the RefreshToken field's value.
func (s *CreateTokenWithIAMOutput) SetRefreshToken(v string) *CreateTokenWithIAMOutput {
	s.RefreshToken = &v
	return s
}

// SetScope sets the Scope field's value.
func (s *CreateTokenWithIAMOutput) SetScope(v []*string) *CreateTokenWithIAMOutput {
	s.Scope = v
	return s
}

// SetTokenType sets the TokenType field's value.
func (s *CreateTokenWithIAMOutput) SetTokenType(v string) *CreateTokenWithIAMOutput {
	s.TokenType = &v
	return s
}

// Indicates that the token issued by the service is expired and is no longer
// valid.
type ExpiredTokenException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be expired_token.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ExpiredTokenException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s ExpiredTokenException) GoString() string {
	return s.String()
}

func newErrorExpiredTokenException(v protocol.ResponseMetadata) error {
	return &ExpiredTokenException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *ExpiredTokenException) Code() string {
	return "ExpiredTokenException"
}

// Message returns the exception's message.
func (s *ExpiredTokenException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *ExpiredTokenException) OrigErr() error {
	return nil
}

func (s *ExpiredTokenException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *ExpiredTokenException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *ExpiredTokenException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that an error from the service occurred while trying to process
// a request.
type InternalServerException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be server_error.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InternalServerException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InternalServerException) GoString() string {
	return s.String()
}

func newErrorInternalServerException(v protocol.ResponseMetadata) error {
	return &InternalServerException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InternalServerException) Code() string {
	return "InternalServerException"
}

// Message returns the exception's message.
func (s *InternalServerException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InternalServerException) OrigErr() error {
	return nil
}

func (s *InternalServerException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InternalServerException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InternalServerException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that the clientId or clientSecret in the request is invalid. For
// example, this can occur when a client sends an incorrect clientId or an expired
// clientSecret.
type InvalidClientException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be invalid_client.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidClientException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidClientException) GoString() string {
	return s.String()
}

func newErrorInvalidClientException(v protocol.ResponseMetadata) error {
	return &InvalidClientException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidClientException) Code() string {
	return "InvalidClientException"
}

// Message returns the exception's message.
func (s *InvalidClientException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidClientException) OrigErr() error {
	return nil
}

func (s *InvalidClientException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidClientException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidClientException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that the client information sent in the request during registration
// is invalid.
type InvalidClientMetadataException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be invalid_client_metadata.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidClientMetadataException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidClientMetadataException) GoString() string {
	return s.String()
}

func newErrorInvalidClientMetadataException(v protocol.ResponseMetadata) error {
	return &InvalidClientMetadataException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidClientMetadataException) Code() string {
	return "InvalidClientMetadataException"
}

// Message returns the exception's message.
func (s *InvalidClientMetadataException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidClientMetadataException) OrigErr() error {
	return nil
}

func (s *InvalidClientMetadataException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidClientMetadataException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidClientMetadataException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that a request contains an invalid grant. This can occur if a client
// makes a CreateToken request with an invalid grant type.
type InvalidGrantException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be invalid_grant.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidGrantException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidGrantException) GoString() string {
	return s.String()
}

func newErrorInvalidGrantException(v protocol.ResponseMetadata) error {
	return &InvalidGrantException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidGrantException) Code() string {
	return "InvalidGrantException"
}

// Message returns the exception's message.
func (s *InvalidGrantException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidGrantException) OrigErr() error {
	return nil
}

func (s *InvalidGrantException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidGrantException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidGrantException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that something is wrong with the input to the request. For example,
// a required parameter might be missing or out of range.
type InvalidRequestException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be invalid_request.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidRequestException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidRequestException) GoString() string {
	return s.String()
}

func newErrorInvalidRequestException(v protocol.ResponseMetadata) error {
	return &InvalidRequestException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidRequestException) Code() string {
	return "InvalidRequestException"
}

// Message returns the exception's message.
func (s *InvalidRequestException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidRequestException) OrigErr() error {
	return nil
}

func (s *InvalidRequestException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidRequestException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidRequestException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that a token provided as input to the request was issued by and
// is only usable by calling IAM Identity Center endpoints in another region.
type InvalidRequestRegionException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Indicates the IAM Identity Center endpoint which the requester may call with
	// this token.
	Endpoint *string `locationName:"endpoint" type:"string"`

	// Single error code. For this exception the value will be invalid_request.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`

	// Indicates the region which the requester may call with this token.
	Region *string `locationName:"region" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidRequestRegionException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidRequestRegionException) GoString() string {
	return s.String()
}

func newErrorInvalidRequestRegionException(v protocol.ResponseMetadata) error {
	return &InvalidRequestRegionException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidRequestRegionException) Code() string {
	return "InvalidRequestRegionException"
}

// Message returns the exception's message.
func (s *InvalidRequestRegionException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidRequestRegionException) OrigErr() error {
	return nil
}

func (s *InvalidRequestRegionException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidRequestRegionException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidRequestRegionException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that the scope provided in the request is invalid.
type InvalidScopeException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be invalid_scope.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidScopeException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s InvalidScopeException) GoString() string {
	return s.String()
}

func newErrorInvalidScopeException(v protocol.ResponseMetadata) error {
	return &InvalidScopeException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *InvalidScopeException) Code() string {
	return "InvalidScopeException"
}

// Message returns the exception's message.
func (s *InvalidScopeException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *InvalidScopeException) OrigErr() error {
	return nil
}

func (s *InvalidScopeException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *InvalidScopeException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *InvalidScopeException) RequestID() string {
	return s.RespMetadata.RequestID
}

type RegisterClientInput struct {
	_ struct{} `type:"structure"`

	// The friendly name of the client.
	//
	// ClientName is a required field
	ClientName *string `locationName:"clientName" type:"string" required:"true"`

	// The type of client. The service supports only public as a client type. Anything
	// other than public will be rejected by the service.
	//
	// ClientType is a required field
	ClientType *string `locationName:"clientType" type:"string" required:"true"`

	// The list of scopes that are defined by the client. Upon authorization, this
	// list is used to restrict permissions when granting an access token.
	Scopes []*string `locationName:"scopes" type:"list"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegisterClientInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegisterClientInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *RegisterClientInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "RegisterClientInput"}
	if s.ClientName == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientName"))
	}
	if s.ClientType == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientType"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetClientName sets the ClientName field's value.
func (s *RegisterClientInput) SetClientName(v string) *RegisterClientInput {
	s.ClientName = &v
	return s
}

// SetClientType sets the ClientType field's value.
func (s *RegisterClientInput) SetClientType(v string) *RegisterClientInput {
	s.ClientType = &v
	return s
}

// SetScopes sets the Scopes field's value.
func (s *RegisterClientInput) SetScopes(v []*string) *RegisterClientInput {
	s.Scopes = v
	return s
}

type RegisterClientOutput struct {
	_ struct{} `type:"structure"`

	// An endpoint that the client can use to request authorization.
	AuthorizationEndpoint *string `locationName:"authorizationEndpoint" type:"string"`

	// The unique identifier string for each client. This client uses this identifier
	// to get authenticated by the service in subsequent calls.
	ClientId *string `locationName:"clientId" type:"string"`

	// Indicates the time at which the clientId and clientSecret were issued.
	ClientIdIssuedAt *int64 `locationName:"clientIdIssuedAt" type:"long"`

	// A secret string generated for the client. The client will use this string
	// to get authenticated by the service in subsequent calls.
	//
	// ClientSecret is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by RegisterClientOutput's
	// String and GoString methods.
	ClientSecret *string `locationName:"clientSecret" type:"string" sensitive:"true"`

	// Indicates the time at which the clientId and clientSecret will become invalid.
	ClientSecretExpiresAt *int64 `locationName:"clientSecretExpiresAt" type:"long"`

	// An endpoint that the client can use to create tokens.
	TokenEndpoint *string `locationName:"tokenEndpoint" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegisterClientOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s RegisterClientOutput) GoString() string {
	return s.String()
}

// SetAuthorizationEndpoint sets the AuthorizationEndpoint field's value.
func (s *RegisterClientOutput) SetAuthorizationEndpoint(v string) *RegisterClientOutput {
	s.AuthorizationEndpoint = &v
	return s
}

// SetClientId sets the ClientId field's value.
func (s *RegisterClientOutput) SetClientId(v string) *RegisterClientOutput {
	s.ClientId = &v
	return s
}

// SetClientIdIssuedAt sets the ClientIdIssuedAt field's value.
func (s *RegisterClientOutput) SetClientIdIssuedAt(v int64) *RegisterClientOutput {
	s.ClientIdIssuedAt = &v
	return s
}

// SetClientSecret sets the ClientSecret field's value.
func (s *RegisterClientOutput) SetClientSecret(v string) *RegisterClientOutput {
	s.ClientSecret = &v
	return s
}

// SetClientSecretExpiresAt sets the ClientSecretExpiresAt field's value.
func (s *RegisterClientOutput) SetClientSecretExpiresAt(v int64) *RegisterClientOutput {
	s.ClientSecretExpiresAt = &v
	return s
}

// SetTokenEndpoint sets the TokenEndpoint field's value.
func (s *RegisterClientOutput) SetTokenEndpoint(v string) *RegisterClientOutput {
	s.TokenEndpoint = &v
	return s
}

// Indicates that the client is making the request too frequently and is more
// than the service can handle.
type SlowDownException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be slow_down.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s SlowDownException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s SlowDownException) GoString() string {
	return s.String()
}

func newErrorSlowDownException(v protocol.ResponseMetadata) error {
	return &SlowDownException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *SlowDownException) Code() string {
	return "SlowDownException"
}

// Message returns the exception's message.
func (s *SlowDownException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *SlowDownException) OrigErr() error {
	return nil
}

func (s *SlowDownException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *SlowDownException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *SlowDownException) RequestID() string {
	return s.RespMetadata.RequestID
}

type StartDeviceAuthorizationInput struct {
	_ struct{} `type:"structure"`

	// The unique identifier string for the client that is registered with IAM Identity
	// Center. This value should come from the persisted result of the RegisterClient
	// API operation.
	//
	// ClientId is a required field
	ClientId *string `locationName:"clientId" type:"string" required:"true"`

	// A secret string that is generated for the client. This value should come
	// from the persisted result of the RegisterClient API operation.
	//
	// ClientSecret is a sensitive parameter and its value will be
	// replaced with "sensitive" in string returned by StartDeviceAuthorizationInput's
	// String and GoString methods.
	//
	// ClientSecret is a required field
	ClientSecret *string `locationName:"clientSecret" type:"string" required:"true" sensitive:"true"`

	// The URL for the Amazon Web Services access portal. For more information,
	// see Using the Amazon Web Services access portal (https://docs.aws.amazon.com/singlesignon/latest/userguide/using-the-portal.html)
	// in the IAM Identity Center User Guide.
	//
	// StartUrl is a required field
	StartUrl *string `locationName:"startUrl" type:"string" required:"true"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartDeviceAuthorizationInput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartDeviceAuthorizationInput) GoString() string {
	return s.String()
}

// Validate inspects the fields of the type to determine if they are valid.
func (s *StartDeviceAuthorizationInput) Validate() error {
	invalidParams := request.ErrInvalidParams{Context: "StartDeviceAuthorizationInput"}
	if s.ClientId == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientId"))
	}
	if s.ClientSecret == nil {
		invalidParams.Add(request.NewErrParamRequired("ClientSecret"))
	}
	if s.StartUrl == nil {
		invalidParams.Add(request.NewErrParamRequired("StartUrl"))
	}

	if invalidParams.Len() > 0 {
		return invalidParams
	}
	return nil
}

// SetClientId sets the ClientId field's value.
func (s *StartDeviceAuthorizationInput) SetClientId(v string) *StartDeviceAuthorizationInput {
	s.ClientId = &v
	return s
}

// SetClientSecret sets the ClientSecret field's value.
func (s *StartDeviceAuthorizationInput) SetClientSecret(v string) *StartDeviceAuthorizationInput {
	s.ClientSecret = &v
	return s
}

// SetStartUrl sets the StartUrl field's value.
func (s *StartDeviceAuthorizationInput) SetStartUrl(v string) *StartDeviceAuthorizationInput {
	s.StartUrl = &v
	return s
}

type StartDeviceAuthorizationOutput struct {
	_ struct{} `type:"structure"`

	// The short-lived code that is used by the device when polling for a session
	// token.
	DeviceCode *string `locationName:"deviceCode" type:"string"`

	// Indicates the number of seconds in which the verification code will become
	// invalid.
	ExpiresIn *int64 `locationName:"expiresIn" type:"integer"`

	// Indicates the number of seconds the client must wait between attempts when
	// polling for a session.
	Interval *int64 `locationName:"interval" type:"integer"`

	// A one-time user verification code. This is needed to authorize an in-use
	// device.
	UserCode *string `locationName:"userCode" type:"string"`

	// The URI of the verification page that takes the userCode to authorize the
	// device.
	VerificationUri *string `locationName:"verificationUri" type:"string"`

	// An alternate URL that the client can use to automatically launch a browser.
	// This process skips the manual step in which the user visits the verification
	// page and enters their code.
	VerificationUriComplete *string `locationName:"verificationUriComplete" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartDeviceAuthorizationOutput) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s StartDeviceAuthorizationOutput) GoString() string {
	return s.String()
}

// SetDeviceCode sets the DeviceCode field's value.
func (s *StartDeviceAuthorizationOutput) SetDeviceCode(v string) *StartDeviceAuthorizationOutput {
	s.DeviceCode = &v
	return s
}

// SetExpiresIn sets the ExpiresIn field's value.
func (s *StartDeviceAuthorizationOutput) SetExpiresIn(v int64) *StartDeviceAuthorizationOutput {
	s.ExpiresIn = &v
	return s
}

// SetInterval sets the Interval field's value.
func (s *StartDeviceAuthorizationOutput) SetInterval(v int64) *StartDeviceAuthorizationOutput {
	s.Interval = &v
	return s
}

// SetUserCode sets the UserCode field's value.
func (s *StartDeviceAuthorizationOutput) SetUserCode(v string) *StartDeviceAuthorizationOutput {
	s.UserCode = &v
	return s
}

// SetVerificationUri sets the VerificationUri field's value.
func (s *StartDeviceAuthorizationOutput) SetVerificationUri(v string) *StartDeviceAuthorizationOutput {
	s.VerificationUri = &v
	return s
}

// SetVerificationUriComplete sets the VerificationUriComplete field's value.
func (s *StartDeviceAuthorizationOutput) SetVerificationUriComplete(v string) *StartDeviceAuthorizationOutput {
	s.VerificationUriComplete = &v
	return s
}

// Indicates that the client is not currently authorized to make the request.
// This can happen when a clientId is not issued for a public client.
type UnauthorizedClientException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be unauthorized_client.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnauthorizedClientException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnauthorizedClientException) GoString() string {
	return s.String()
}

func newErrorUnauthorizedClientException(v protocol.ResponseMetadata) error {
	return &UnauthorizedClientException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *UnauthorizedClientException) Code() string {
	return "UnauthorizedClientException"
}

// Message returns the exception's message.
func (s *UnauthorizedClientException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *UnauthorizedClientException) OrigErr() error {
	return nil
}

func (s *UnauthorizedClientException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *UnauthorizedClientException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *UnauthorizedClientException) RequestID() string {
	return s.RespMetadata.RequestID
}

// Indicates that the grant type in the request is not supported by the service.
type UnsupportedGrantTypeException struct {
	_            struct{}                  `type:"structure"`
	RespMetadata protocol.ResponseMetadata `json:"-" xml:"-"`

	// Single error code. For this exception the value will be unsupported_grant_type.
	Error_ *string `locationName:"error" type:"string"`

	// Human-readable text providing additional information, used to assist the
	// client developer in understanding the error that occurred.
	Error_description *string `locationName:"error_description" type:"string"`

	Message_ *string `locationName:"message" type:"string"`
}

// String returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnsupportedGrantTypeException) String() string {
	return awsutil.Prettify(s)
}

// GoString returns the string representation.
//
// API parameter values that are decorated as "sensitive" in the API will not
// be included in the string output. The member name will be present, but the
// value will be replaced with "sensitive".
func (s UnsupportedGrantTypeException) GoString() string {
	return s.String()
}

func newErrorUnsupportedGrantTypeException(v protocol.ResponseMetadata) error {
	return &UnsupportedGrantTypeException{
		RespMetadata: v,
	}
}

// Code returns the exception type name.
func (s *UnsupportedGrantTypeException) Code() string {
	return "UnsupportedGrantTypeException"
}

// Message returns the exception's message.
func (s *UnsupportedGrantTypeException) Message() string {
	if s.Message_ != nil {
		return *s.Message_
	}
	return ""
}

// OrigErr always returns nil, satisfies awserr.Error interface.
func (s *UnsupportedGrantTypeException) OrigErr() error {
	return nil
}

func (s *UnsupportedGrantTypeException) Error() string {
	return fmt.Sprintf("%s: %s\n%s", s.Code(), s.Message(), s.String())
}

// Status code returns the HTTP status code for the request's response error.
func (s *UnsupportedGrantTypeException) StatusCode() int {
	return s.RespMetadata.StatusCode
}

// RequestID returns the service's response RequestID for request.
func (s *UnsupportedGrantTypeException) RequestID() string {
	return s.RespMetadata.RequestID
}
