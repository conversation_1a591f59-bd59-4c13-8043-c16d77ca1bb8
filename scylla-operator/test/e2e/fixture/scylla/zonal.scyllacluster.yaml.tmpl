apiVersion: scylla.scylladb.com/v1
kind: ScyllaCluster
metadata:
  generateName: basic-
  labels:
   foo: bar
  annotations:
   bar: foo
spec:
  agentVersion: 3.3.0
  version: 6.1.0
  developerMode: true
  exposeOptions:
    nodeService:
      type: {{ .nodeServiceType }}
    broadcastOptions:
      nodes:
        type: {{ .nodesBroadcastAddressType }}
      clients:
        type: {{ .clientsBroadcastAddressType }}
  datacenter:
    name: us-east-1
    racks:
    {{- range $_, $rackName := .rackNames }}
    - name: "{{ $rackName }}"
      members: 1
      storage:
        capacity: 1Gi
        {{- if $.storageClassName }}
        storageClassName: {{ $.storageClassName }}
        {{- end }}
      resources:
        requests:
          cpu: 10m
          memory: 100Mi
        limits:
          cpu: 1
          memory: 1Gi
    {{- end }}
  sysctls:
   - fs.aio-max-nr=30000000
